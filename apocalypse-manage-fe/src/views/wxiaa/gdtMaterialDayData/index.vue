<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="剪辑" prop="materialEditors">
        <el-select v-model="queryParams.materialEditors" filterable multiple clearable>
          <el-option v-for="item in materialEditorList" :key="item.id" :label="item.userRealname"
                     :value="item.userRealname">
          </el-option>
        </el-select>
        <!-- <el-input v-model="queryParams.materialEditor" placeholder="请输入剪辑" clearable
          @keyup.enter.native="handleQuery" /> -->
      </el-form-item>
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker v-model="queryParams.dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions"
                        @change="handleQuery" :clearable="false"></el-date-picker>
      </el-form-item>
      <el-form-item label="组织" prop="accountId">
        <el-select v-model="queryParams.accountId" filterable clearable @change="handleQuery">
          <el-option v-for="item in organization" :key="item.accountId" :label="item.accountName"
                     :value="item.accountId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['wxiaa:gdtMaterialDayData:export']">导出
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" class="mt4">
        <DropdownCheckboxGroup v-model="queryParams.dimension" :options="dimensionOptions"
                               :default-value="dimensionDefaultValue" @change="handleDimensionChange">
          <el-link type="primary" :underline="false">维度选择<i class="el-icon-arrow-down el-icon--right"></i></el-link>
        </DropdownCheckboxGroup>
      </el-col>
      <right-toolbar :name="columnsStorageName" :showSearch.sync="showSearch" @queryTable="getList"
                     :columns.sync="columns" :default-selected-column-keys="Object.keys(columns)"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="gdtMaterialDayDataList" @selection-change="handleSelectionChange"
              @sort-change="handleSortChange" :key="tableKey">
      <el-table-column label="日期" align="center" prop="curDate" width="180"
                       v-if="queryParams.dimension.includes('cur_date')">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.curDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="queryParams.dimension.includes('material_editor')" label="剪辑" align="center"
                       prop="materialEditor"/>
      <el-table-column v-if="queryParams.dimension.includes('material_desc')" label="素材名称" align="center"
                       prop="materialDesc"/>
      <el-table-column v-if="queryParams.dimension.includes('material_type')" label="素材类型" align="center"
                       prop="materialType">
        <template slot-scope="scope">
          <span>{{ scope.row.materialType == 1 ? '图片' : '视频' }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="queryParams.dimension.includes('material_id')" label="素材" align="center"
                       prop="previewUrl"
                       width="100px">
        <template slot-scope="scope">
          <el-popover placement="top-start" trigger="hover">
            <div slot="reference" style="width: 80px; height: auto">
              <el-image v-if="scope.row.previewUrl && scope.row.materialType === 1" style="width: 80px; height: auto"
                        :src="scope.row.previewUrl" fit="contain"/>
              <video v-if="scope.row.previewUrl && scope.row.materialType === 2" style="width: 80px; height: auto"
                     :src="scope.row.previewUrl" fit="contain"/>
            </div>
            <el-image v-if="scope.row.previewUrl && scope.row.materialType === 1" style="width: 205px; height: auto"
                      :src="scope.row.previewUrl" fit="cover"/>
            <video v-if="scope.row.previewUrl && scope.row.materialType === 2" autoplay
                   style="width: 205px; height: auto;" :src="scope.row.previewUrl" fit="cover"/>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="组织" align="center" prop="accountName"
                       v-if="queryParams.dimension.includes('account_id')"/>
      <el-table-column v-if="columns.cost.visible" label="花费" align="center" sortable="custom"
                       :sort-orders="['ascending', 'descending', null]" prop="cost"/>
      <el-table-column v-if="columns.ctr.visible" label="点击率" align="center" prop="ctr">
        <template slot-scope="scope">
          <span>{{ scope.row.ctr }}%</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columns.viewCount.visible" label="曝光次数" align="center" sortable="custom"
                       :sort-orders="['ascending', 'descending', null]" prop="viewCount"/>
      <el-table-column v-if="columns.adMonetizationRoi.visible" label="广告变现ROI" align="center"
                       prop="adMonetizationRoi"/>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </div>
</template>

<script>
import {
  addGdtMaterialDayData,
  delGdtMaterialDayData,
  getGdtMaterialDayData,
  listGdtMaterialDayData,
  updateGdtMaterialDayData,
  exportGdtMaterialDayData
} from '@/api/wxiaa/gdtMaterialDayData'

import {createUniqueString} from '@/utils'
import {listAccount} from '@/api/guangdiantong/account'
import storage from '@/utils/storage'
import {parseTime} from '@/utils/ruoyi'
import {listUser} from "@/api/agtaccount/user";
import {emitter} from "@/utils/mitt.ts";

// 维度选择默认值
const dimensionDefaultValue = [
  'cur_date',
  'material_desc',
  'material_type',
  'material_editor',
  'material_id',
  'account_id'
]

// 维度选择本地存储名称
const DIMENSION_STORAGE_NAME = 'gdt-material-list-dimension'

// 显隐列本地存储名称，最后数字为版本号，为避免修改对应数据后线上不生效，需要更新版本号
const COLUMNS_STORAGE_NAME = 'gdt-material-list-columns_1'

export default {
  name: 'GdtMaterialDayData',
  data() {
    return {
      // 表格key
      tableKey: createUniqueString(),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      organization: [], //组织列表
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 广点通IAA素材日数据表格数据
      gdtMaterialDayDataList: [],
      // 剪辑列表
      materialEditorList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 维度选择
      dimensionOptions: [
        {label: '日期', value: 'cur_date'},
        {label: '剪辑', value: 'material_editor'},
        {label: '素材名称', value: 'material_desc'},
        {label: '素材类型', value: 'material_type'},
        {label: '素材id', value: 'material_id'},
        {label: '组织', value: 'account_id'}
      ],
      // 维度默认数据
      dimensionDefaultValue,
      // 显隐列本地存储
      columnsStorageName: COLUMNS_STORAGE_NAME,
      pickerMinDate: null, // 记录首次选择的日期
      pickerOptions: {
        onPick: ({minDate}) => {
          this.pickerMinDate = minDate ? minDate.getTime() : null;
        },
        disabledDate: (time) => {
          if (!this.pickerMinDate) return false;
          const range = 31 * 24 * 3600 * 1000;
          const maxTime = this.pickerMinDate + range;
          const minTime = this.pickerMinDate - range;
          return time.getTime() > maxTime || time.getTime() < minTime;
        }
      },
      // 表格列显隐控制
      columns: Object.assign(
        {
          cost: {
            label: '花费',
            key: 'cost',
            visible: true
          },
          ctr: {
            label: '点击率',
            key: 'ctr',
            visible: true
          },
          viewCount: {
            label: '曝光次数',
            key: 'viewCount',
            visible: true
          },
          adMonetizationRoi: {
            label: '广告变现ROI',
            key: 'adMonetizationRoi',
            visible: true
          }
        },
        storage.local.getByUser(COLUMNS_STORAGE_NAME)
      ),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [
          parseTime(Date.now(), '{y}-{m}-{d}'),
          parseTime(Date.now(), '{y}-{m}-{d}')
        ],
        materialId: null,
        materialType: null,
        materialDesc: null,
        materialEditors: [],
        viewCount: null,
        validClickCount: null,
        conversionsCount: null,
        appAdPayingUsers: null,
        adMonetizationAmount: null,
        cost: null,
        gmtCreate: null,
        gmtModified: null,
        dimension: storage.local.getByUser(DIMENSION_STORAGE_NAME) || [...dimensionDefaultValue]
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        curDate: [{required: true, message: '当前日期不能为空', trigger: 'blur'}],
        materialId: [
          {
            required: true,
            message: '素材ID(图片ID/视频ID)不能为空',
            trigger: 'blur'
          }
        ],
        materialType: [
          {
            required: true,
            message: '素材类型:1.图片,2.视频不能为空',
            trigger: 'change'
          }
        ],
        materialDesc: [{required: true, message: '素材描述不能为空', trigger: 'blur'}],
        materialEditor: [{required: true, message: '剪辑昵称不能为空', trigger: 'blur'}],
        viewCount: [{required: true, message: '曝光次数不能为空', trigger: 'blur'}],
        validClickCount: [{required: true, message: '点击次数不能为空', trigger: 'blur'}],
        conversionsCount: [{required: true, message: '目标转化量不能为空', trigger: 'blur'}],
        appAdPayingUsers: [{required: true, message: '广告变现人数不能为空', trigger: 'blur'}],
        adMonetizationAmount: [{required: true, message: '广告变现金额不能为空', trigger: 'blur'}],
        cost: [{required: true, message: '花费不能为空', trigger: 'blur'}],
        gmtCreate: [{required: true, message: '创建时间不能为空', trigger: 'blur'}],
        gmtModified: [{required: true, message: '修改时间不能为空', trigger: 'blur'}]
      }
    }
  },
  watch: {
    columns: {
      handler() {
        this.tableKey = createUniqueString()
      },
      deep: true
    }
  },
  created() {
    this.getList()
    this.getOrganization()
    this.getMaterialEditorList()
  },
  methods: {
    /** 维度变更 */
    handleDimensionChange(value) {
      storage.local.setByUser(DIMENSION_STORAGE_NAME, value)
      this.handleQuery()
      this.tableKey = createUniqueString()
    },
    /** 获取组织列表 */
    async getOrganization() {
      const {list} = await listAccount({
        pageSize: 10e6,
        pageNum: 1
      })
      this.organization = list
    },
    /** 获取剪辑师列表 */
    async getMaterialEditorList() {
      const {list} = await listUser({
        pageSize: 10e6,
        pageNum: 1,
        post: 1
      })
      this.materialEditorList = list
    },
    /** 查询广点通IAA素材日数据列表 */
    getList() {
      const {dateRange, ...params} = this.queryParams
      this.loading = true
      listGdtMaterialDayData({
        ...params,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1]
      }).then((response) => {
        this.gdtMaterialDayDataList = response.list
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        curDate: null,
        materialId: null,
        materialType: null,
        materialDesc: null,
        materialEditor: null,
        viewCount: null,
        validClickCount: null,
        conversionsCount: null,
        appAdPayingUsers: null,
        adMonetizationAmount: null,
        cost: null,
        gmtCreate: null,
        gmtModified: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderColumn = column.order ? column.prop : null
      this.queryParams.isAsc = column.order ? Boolean(column.order === 'ascending') : null
      this.getList()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加广点通IAA素材日数据'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getGdtMaterialDayData(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改广点通IAA素材日数据'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateGdtMaterialDayData(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addGdtMaterialDayData(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除广点通IAA素材日数据编号为"' + ids + '"的数据项？')
        .then(function () {
          return delGdtMaterialDayData(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    async handleExport() {
      const {dateRange, ...params} = this.queryParams

      try {
        await exportGdtMaterialDayData({
          ...params,
          startDate: dateRange?.[0],
          endDate: dateRange?.[1]
        })
        emitter.emit("getAsyncList")
      } catch (error) {
        ElMessage({
          type: "error",
          message: "下载任务创建失败"
        })
      }
    }
  }
}
</script>
