// import { request } from "@/utils/service"
import request from '@/utils/request'

const proPath = "https://actengine.ydns.cn"
const testPath = "https://actenginetest.ydns.cn"
const commonPath = process.env.NODE_ENV === "production" ? proPath : testPath

/**
 * 获取地区数据
 * @param {Object} params - 参数对象
 * @param {string} [params.lpk] - lpk参数
 * @param {string} [params.areaNum] - 地区编号
 */
export async function lpAddress(params) {
  return request({
    url: `${commonPath}/lp/areaList`,
    method: "get",
    params
  })
}

/**
 * 获取全部地区数据
 * @param params
 */
export async function areaList() {
  return request({
    url: `${commonPath}/kefu/intendedForm/areaList`,
    method: "get"
  })
}

/**
 * 获取行政区划列表
 */
export async function getXZAreaList(searchStr) {
  return request({
    url: "dataProcess/areaList",
    method: "get",
    params: {
      areaName: searchStr
    }
  })
}

/** 根据oss地址获取文件的blob */
export function ossUrlTransferBlob(url) {
  return request({
    url,
    method: "get",
    responseType: "blob"
  })
}
