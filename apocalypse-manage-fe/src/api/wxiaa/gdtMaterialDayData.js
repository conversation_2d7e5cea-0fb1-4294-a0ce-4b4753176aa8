import request from '@/utils/request'

// 查询广点通IAA素材日数据列表
export function listGdtMaterialDayData(query) {
  return request({
    url: '/wxiaa/gdtMaterialDayData/list',
    method: 'get',
    params: query
  })
}

export function exportGdtMaterialDayData(query) {
  return request({
    url: '/wxiaa/gdtMaterialDayData/export',
    method: 'get',
    params: query
  })
}

// 查询广点通IAA素材日数据详细
export function getGdtMaterialDayData(id) {
  return request({
    url: '/wxiaa/gdtMaterialDayData/' + id,
    method: 'get'
  })
}

// 新增广点通IAA素材日数据
export function addGdtMaterialDayData(data) {
  return request({
    url: '/wxiaa/gdtMaterialDayData/add',
    method: 'post',
    data: data
  })
}

// 修改广点通IAA素材日数据
export function updateGdtMaterialDayData(data) {
  return request({
    url: '/wxiaa/gdtMaterialDayData/update',
    method: 'post',
    data: data
  })
}

// 删除广点通IAA素材日数据
export function delGdtMaterialDayData(id) {
  return request({
    url: '/wxiaa/gdtMaterialDayData/' + id,
    method: 'delete'
  })
}
