import request from '@/utils/request'

// 查询下载中心任务列表
export function listDownloadCenterTask(query) {
  return request({
    url: '/download/downloadCenterTask/list',
    method: 'get',
    params: query
  })
}

// 查询下载中心任务详细
export function getDownloadCenterTask(id) {
  return request({
    url: '/download/downloadCenterTask/' + id,
    method: 'get'
  })
}

// 新增下载中心任务
export function addDownloadCenterTask(data) {
  return request({
    url: '/download/downloadCenterTask/add',
    method: 'post',
    data: data
  })
}

// 修改下载中心任务
export function updateDownloadCenterTask(data) {
  return request({
    url: '/download/downloadCenterTask/update',
    method: 'post',
    data: data
  })
}

// 删除下载中心任务
export function delDownloadCenterTask(id) {
  return request({
    url: '/download/downloadCenterTask/' + id,
    method: 'delete'
  })
}
