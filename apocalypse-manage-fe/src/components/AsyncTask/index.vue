<template>
  <div>
    <el-popover
      ref="popoverRef"
      v-model="visible"
      trigger="manual"
      placement="bottom"
      title="任务中心"
      :width="600"
      @hide="handlePopoverHide"
      @show="handlePopoverShow"
    >
      <template slot="reference">
        <svg-icon icon-class="downloadfilled" class="down-icon" @click.native="visible = !visible"/>
      </template>

      <el-table :data="tableList" :border="true" max-height="450px">
        <el-table-column prop="id" label="ID" width="50" align="center"/>
        <el-table-column prop="taskName" width="150" label="任务名称" align="center"/>
        <el-table-column prop="taskStatus" label="状态" align="center">
          <template slot-scope="{ row }">
            <span :style="{ color: taskStatusMap[row.taskStatus] && taskStatusMap[row.taskStatus].color }">{{
                taskStatusMap[row.taskStatus] && taskStatusMap[row.taskStatus].text
              }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="gmtCreate" width="100" label="导出时间" align="center"/>
        <el-table-column label="操作" align="center" width="80">
          <template slot-scope="scope">
            <a v-if="scope.row.taskStatus === 2" @click="handleFileDownLoad(scope.row)" href="javascript:void(0)"
               style="color: #409eff; cursor: pointer">
              下载
            </a>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="table-empty">
            <img src="@/assets/images/notData.png" alt="notData"/>
            <div>暂无数据</div>
          </div>
        </template>
      </el-table>
      <div class="pagination-wrap">
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :page-sizes="[10, 15]"
          background
          layout="total,  prev, pager, next, sizes"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-popover>
  </div>
</template>

<script>
import {emitter} from "@/utils/mitt.ts"
// import { useSSE } from "@/hooks/useSSE"
import {Message} from "element-ui"
import {useDownload} from "@/hooks/useDownload.ts"
import {ossUrlTransferBlob} from "@/api/common"
import {sliceArray} from "@/utils"
import {getAsyncDownloadTask} from "@/api/login"

let downloadTaskList = []
let interval = null
// let isFirstEnter = true

const {handleBlobDownLoad} = useDownload()

export default {
  name: 'AsyncTask',
  data() {
    return {
      tableList: [],
      visible: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      taskStatusMap: {
        1: {
          color: "#e6a23c",
          text: "导出中"
        },
        2: {
          color: "green",
          text: "导出完成"
        },
        3: {
          color: "red",
          text: "导出失败"
        }
      }
    }
  },

  mounted() {
    this.handleGetTableList()
    emitter.on("getAsyncList", async () => {
      Message({
        type: "success",
        message: "下载任务创建成功，请稍后在任务中心查看"
      })
      setTimeout(() => {
        this.visible = true
      })
    })
  },
  beforeDestroy() {
    this.handleCloseInterval()
  },
  methods: {
    async handleFileDownLoad(row) {
      const res = await ossUrlTransferBlob(row.fileUrl)
      const fileName = row.taskName + "." + row.fileUrl.split(".").pop()
      handleBlobDownLoad(res, undefined, fileName)
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getTableList()
    },
    getTableList() {
      const currentIndex = (this.pageNum - 1) * this.pageSize
      const data = downloadTaskList || []
      const endIndex = Math.min(currentIndex + this.pageSize, data.length)
      this.tableList = sliceArray(data, currentIndex, endIndex)
    },
    handleSizeChange(val) {
      this.pageNum = 1
      this.pageSize = val
      this.getTableList()
    },
    async handleGetTableList() {
      const {data} = await getAsyncDownloadTask()
      downloadTaskList = data
      this.total = downloadTaskList?.length || 0
      this.getTableList()
    },
    handleCloseInterval() {
      if (interval) {
        clearInterval(interval)
      }
    },
    handleCreateInterval() {
      if (interval) {
        clearInterval(interval)
      }
      this.handleGetTableList()
      interval = setInterval(this.handleGetTableList, 2000)
    },
    handlePopoverHide() {
      this.handleCloseInterval()
      document.removeEventListener('click', this.handleClickOutside)
    },
    handlePopoverShow() {
      this.handleCreateInterval()
      document.addEventListener('click', this.handleClickOutside)
    },
    handleClickOutside(e) {
      const popover = this.$refs.popoverRef
      if (popover && popover.popperElm && popover.referenceElm && this.visible) {
        if (!popover.popperElm.contains(e.target) && !popover.referenceElm.contains(e.target)) {
          this.visible = false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.down-icon {
  width: 21px;
  height: 21px;
  margin-top: 14px;
}

.table-empty {
  padding: 20px 0;
}

.pagination-wrap {
  padding: 10px 0;
  display: flex;
  justify-content: center;

  :deep(.el-pagination__sizes.is-last) {
    margin-right: 0;
  }
}
</style>
