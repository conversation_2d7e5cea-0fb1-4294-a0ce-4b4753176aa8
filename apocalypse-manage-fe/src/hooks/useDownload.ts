import { Loading, Message } from 'element-ui'

let downloadLoadingInstance

export const useDownload = () => {
  const handleBlobDownLoad = (blob, headers, fileName) => {
    try {
      downloadLoadingInstance = Loading.service({
        text: '下载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const downloadUrl = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.setAttribute('download', fileName || decodeURIComponent(headers && headers.filename || `${Date.now()}`))
      document.body.appendChild(link)
      downloadLoadingInstance.close()
      link.click()
      document.body.removeChild(link)
      // 释放 URL 地址
      URL.revokeObjectURL(downloadUrl)
    } catch (error) {
      if (downloadLoadingInstance) {
        downloadLoadingInstance.close()
      }
      Message.error('文件下载失败')
    }
  }
  return { handleBlobDownLoad }
}
