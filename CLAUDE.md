# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Apocalypse Management System - a comprehensive Java/Spring Boot application with Vue.js frontend for managing various apocalyptic or catastrophic scenarios. The system handles resource management, emergency response coordination, supply chain tracking, personnel management, and real-time monitoring.

### Architecture

- **Backend**: Spring Boot 2.5.15 with Java 8, based on RuoYi framework
- **Frontend**: Vue 2.6.12 with Element UI 2.15.14
- **Database**: MySQL with MyBatis
- **Cache**: Redis with Redisson
- **Build**: Maven (backend) + npm/Vue CLI (frontend)

### Module Structure

```
├── ruoyi-web/          # Main web application and controllers
├── ruoyi-system/       # Core business logic and services  
├── ruoyi-framework/    # Framework configuration and utilities
├── ruoyi-common/       # Shared utilities and constants
├── ruoyi-quartz/       # Scheduled task management
├── ruoyi-generator/    # Code generation tools
└── apocalypse-manage-fe/ # Vue.js frontend application
```

## Common Development Commands

### Backend (Java/Spring Boot)

```bash
# Build the project
mvn clean package -Dmaven.test.skip=true
```

### Frontend (Vue.js)

```bash
cd apocalypse-manage-fe

# Install dependencies
npm install

# Development server
npm run dev

```

## Key Technical Details

### Database Configuration
- Modify `application-test.yml` or `application-prod.yml` for database connections
- Initialize database using SQL files in `/sql/` directory
- Execute all SQL files for complete setup

### Important Dependencies
- **Tencent GDT**: Marketing API Java SDK for advertising integration
- **Ocean Engine**: ByteDance advertising platform integration
- **WeChat Mini Program**: WeChat integration for mini-apps
- **Aliyun Services**: OSS storage and logging
- **Payment Integration**: Alipay and WeChat Pay
- **Hutool**: Chinese utility library (prefer DateUtil for date operations)

### Caching Strategy
- Uses Guava Cache for in-memory caching
- LoadingCache pattern with automatic refresh
- Example implementation in `ShortplayRpServiceImpl.java`
- Configure refresh policies (e.g., `refreshAfterWrite(1, TimeUnit.MINUTES)`)

### Code Standards and Best Practices

#### Project-Specific "Good Code" Guidelines
- **Always use project's common utilities** from `ruoyi-common` module before creating new ones
- **Check for null values** before operations: use `Objects.nonNull()`, `StringUtils.isNotEmpty()`, or Optional patterns
- **Throw specific business exceptions** with meaningful messages instead of generic RuntimeException
- **Use RuoYi framework utilities** like `AjaxResult`, `PageUtils`, `SecurityUtils` for consistent responses
- **Validate input parameters** at controller level and throw `ServiceException` with proper error codes

#### Date and Time Handling
- **Always use Hutool's DateUtil** for date operations instead of Java's Date/Calendar
- Use consistent date formats: `DateUtil.format(date, "yyyy-MM-dd HH:mm:ss")`
- For date parsing: `DateUtil.parse(dateString, "yyyy-MM-dd")`
- **Null safety**: Always check date objects before formatting: `if (Objects.nonNull(date)) { ... }`

#### Numeric Operations
- **BigDecimal Comparisons**: Use `compareTo()` method, never `equals()` for zero checks
  - Correct: `bigDecimal.compareTo(BigDecimal.ZERO) == 0`
  - Wrong: `bigDecimal.equals(BigDecimal.ZERO)`
- Use `BigDecimal.valueOf()` instead of `new BigDecimal()` for better performance
- Always specify scale and rounding mode for monetary calculations

#### Exception Handling
- **Use ServiceException** for business logic errors with proper error codes from `ruoyi-common`
- **Always validate null parameters** at the start of methods: `Assert.notNull(param, "Parameter cannot be null")`
- **Use specific exception types** rather than generic `Exception` or `RuntimeException`
- **Log exceptions with context**: include user ID, operation details, and input parameters
- **Use @ControllerAdvice** for global exception handling in controllers
- **Return consistent error responses** using `AjaxResult.error()` with meaningful messages
- **Example**: `throw new ServiceException("用户不存在", 404)` instead of `throw new RuntimeException("User not found")`

#### Logging Practices
- Use SLF4J with Logback (configured in logback.xml)
- Use appropriate log levels: ERROR for exceptions, WARN for potential issues, INFO for business logic, DEBUG for detailed debugging
- Include relevant context in log messages (user ID, operation type, etc.)
- Avoid logging sensitive information (passwords, tokens, personal data)

#### API Response Standards
- Return appropriate HTTP status codes
- Include error codes and descriptive messages for client applications
- Use pagination for list endpoints with consistent parameter names

#### Database and MyBatis
- **Always use parameterized queries** to prevent SQL injection - never concatenate user input
- **Check for null IDs** before database operations: `if (Objects.isNull(id)) { throw new ServiceException("ID不能为空"); }`
- **Implement proper transaction boundaries** with `@Transactional` on service methods
- **Use batch operations** for bulk data processing to improve performance
- **Handle empty result sets**: Use `CollectionUtils.isEmpty()` to check list results
- **Optimize N+1 query problems** with proper joins or batch fetching
- **Use MyBatis result maps** for complex object mapping instead of manual field copying

#### Performance Guidelines
- Implement caching using Guava Cache or Redis for frequently accessed data
- Use connection pooling for database connections
- Implement proper pagination for large datasets
- Monitor and optimize slow database queries
- Use async processing for time-consuming operations

#### Redis/Redisson Usage
- Use consistent key naming conventions (e.g., `prefix:module:identifier`)
- Set appropriate TTL for cached data
- Use Redisson's distributed locks for concurrent operations
- Handle Redis connection failures gracefully

#### Frontend (Vue.js) Standards
- Follow Vue.js style guide for component naming and structure
- Use Element UI components consistently
- Implement proper error handling for API calls
- Use Vue Router for navigation management
- Implement loading states for async operations

#### Code Quality
- Write meaningful commit messages following conventional commit format
- Keep methods focused and under 50 lines when possible
- Use descriptive variable and method names
- Add JSDoc comments for public methods and complex logic

### Application Configuration
- Default admin credentials: admin/admin123
- Configure CLIENT_SECRET and CLIENT_KEY in DouyinPublishVideoBillFetchServiceImpl.java
- Set up scheduled tasks via System Monitoring → Scheduled Tasks after deployment
- Modify logback.xml for Aliyun logging configuration

### Development Environment
- Java 8 required
- Node.js >= 8.9 for frontend
- Maven repository: Aliyun nexus
- Supports both development and production profiles