package com.ruoyi.web.controller.download;

import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.download.DownloadCenterTaskEntity;
import com.ruoyi.system.service.download.DownloadCenterTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 下载中心任务Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/download/downloadCenterTask")
public class DownloadCenterTaskController {
    @Autowired
    private DownloadCenterTaskService downloadCenterTaskService;


    @GetMapping("/downloadTaskList")
    public Result<List<DownloadCenterTaskEntity>> downloadTaskList() {
        Long userId = SecurityUtils.getUserId();
        DownloadCenterTaskEntity taskEntity = new DownloadCenterTaskEntity();
        taskEntity.setOperatorId(userId);
        List<DownloadCenterTaskEntity> downloadCenterTaskEntities = downloadCenterTaskService.selectDownloadCenterTaskList(taskEntity);
        List<DownloadCenterTaskEntity> sorted = downloadCenterTaskEntities.stream().sorted((o1, o2) -> o2.getId().compareTo(o1.getId())).collect(Collectors.toList());

        return ResultBuilder.success(sorted);
    }

}
