package com.ruoyi.web.controller.wxiaa;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.download.DownloadCenterTaskEntity;
import com.ruoyi.system.entity.wxiaa.GdtMaterialDayDataEntity;
import com.ruoyi.system.entity.wxiaa.GdtMaterialEntity;
import com.ruoyi.system.param.wxiaa.GdtMaterialDayDataParam;
import com.ruoyi.system.service.download.DownloadCenterTaskService;
import com.ruoyi.system.service.wxiaa.GdtAccountService;
import com.ruoyi.system.service.wxiaa.GdtMaterialDayDataService;
import com.ruoyi.system.service.wxiaa.GdtMaterialService;
import com.ruoyi.system.vo.wxiaa.GdtMaterialDayDataVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 广点通IAA素材日数据Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wxiaa/gdtMaterialDayData")
public class GdtMaterialDayDataController {

    @Autowired
    private GdtMaterialDayDataService gdtMaterialDayDataService;
    @Autowired
    private GdtAccountService gdtAccountService;
    @Autowired
    private GdtMaterialService gdtMaterialService;
    @Autowired
    private RedisCache redisCacheClient;
    @Autowired
    private DownloadCenterTaskService downloadCenterTaskService;

    /**
     * 查询广点通IAA素材日数据列表
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtMaterialDayData:list')")
    @GetMapping("/list")
    public PageResult<GdtMaterialDayDataVO> list(GdtMaterialDayDataParam param) {
        TableSupport.startPage();
        List<GdtMaterialDayDataEntity> list = gdtMaterialDayDataService.selectList(param);
        if (CollectionUtils.isEmpty(list)) {
            return ResultBuilder.successPage(PageInfo.emptyPageInfo());
        }
        Map<Long, String> accountNameMap = gdtAccountService.selectAccountNameMap();
        Map<String, GdtMaterialEntity> materialMap = null != param.getDimension() && param.getDimension().contains("material_id") ?
                gdtMaterialService.selectMap(list.stream().map(GdtMaterialDayDataEntity::getMaterialId).collect(Collectors.toList())) :
                Collections.emptyMap();
        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(list, s -> getGdtDayDataVO(s, accountNameMap, materialMap)));
    }

    /**
     * 导出广点通IAA素材日数据列表
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtMaterialDayData:export')")
    @Log(title = "广点通IAA素材日数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(GdtMaterialDayDataParam param) {
        Long userId = SecurityUtils.getUserId();

        // 判断当前是否有任务正在下载
        String taskKey = RedisKeyFactory.K100.join(param.getStartDate(), param.getEndDate(), param.getMaterialEditors(), param.getAccountId());
        String ossFileUrl = redisCacheClient.getCacheObject(taskKey);
        DownloadCenterTaskEntity downloadCenterTaskEntity = new DownloadCenterTaskEntity();
        downloadCenterTaskEntity.setTaskName("素材报表导出 "+ DateUtil.formatDate(new Date()));
        downloadCenterTaskEntity.setTaskStatus(1);
        downloadCenterTaskEntity.setOperatorId(userId);
        if (StringUtils.isNotBlank(ossFileUrl)) {

            downloadCenterTaskEntity.setFileUrl(ossFileUrl);
            downloadCenterTaskEntity.setTaskStatus(2);
            downloadCenterTaskService.insertDownloadCenterTask(downloadCenterTaskEntity);
            return;

        }
        downloadCenterTaskService.insertDownloadCenterTask(downloadCenterTaskEntity);

        gdtMaterialDayDataService.doExportUploadToOss(param, taskKey, downloadCenterTaskEntity.getId(), userId);


    }

    private GdtMaterialDayDataVO getGdtDayDataVO(GdtMaterialDayDataEntity entity, Map<Long, String> accountNameMap,
                                                Map<String, GdtMaterialEntity> materialMap) {
        GdtMaterialDayDataVO vo = BeanUtil.copyProperties(entity, GdtMaterialDayDataVO.class);
        if (null != entity.getAccountId()) {
            vo.setAccountName(accountNameMap.get(entity.getAccountId()));
        }
        vo.setCurDate(entity.getCurDate());
        vo.setCost(NumberUtils.fenToYuan(entity.getCost()));
        vo.setMaterialTypeStr(Objects.equals(entity.getMaterialType(), 1) ? "图片" : "视频");
        vo.setAdMonetizationAmount(NumberUtils.fenToYuan(entity.getAdMonetizationAmount()));
        vo.setCtr(NumberUtils.calculateRateToDouble(100 * entity.getValidClickCount(), entity.getViewCount()));
        vo.setCtrStr(NumberUtils.calculateRate(entity.getValidClickCount(), entity.getViewCount()));
        vo.setAdMonetizationRoi(NumberUtils.calculateRateToDouble(entity.getAdMonetizationAmount(), entity.getCost(), 4));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entity.getMaterialId()) && materialMap.containsKey(entity.getMaterialId())) {
            GdtMaterialEntity material = materialMap.get(entity.getMaterialId());
            vo.setMaterialDesc(material.getMaterialDesc());
            vo.setPreviewUrl(material.getPreviewUrl());
        }
        return vo;
    }

}
