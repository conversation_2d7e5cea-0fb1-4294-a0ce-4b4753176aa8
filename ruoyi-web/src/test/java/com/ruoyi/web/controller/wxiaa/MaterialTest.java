package com.ruoyi.web.controller.wxiaa;

import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: keboom
 * @date: 2025/7/31
 */
public class MaterialTest {


    @Test
    void filterName() {
        List<String> materialNames = Arrays.asList(
            "视频封面-自动生成-20-202504211120",
            "视频封面-自动生成-20-202504301822",
            "6667e342000664441779b5fa7c6abc1e0000008d00004eec",
            "250108-陈礼灿-短剧-18",
            "250102-小惠-水墨风景-4"
        );

        // Pattern to match Chinese characters
        Pattern chinesePattern = Pattern.compile("[\u4e00-\u9fa5]");
        
        // Filter names that do NOT contain Chinese characters AND do NOT start with "视频封面"
        List<String> filteredNames = materialNames.stream()
            .filter(name -> chinesePattern.matcher(name).find()) //  contain Chinese characters
            .filter(name -> !name.startsWith("视频封面")) // Does not start with "视频封面"
            .collect(Collectors.toList());

        System.out.println("Filtered names (no Chinese characters AND not start with '视频封面'):");
        filteredNames.forEach(System.out::println);
    }
}
