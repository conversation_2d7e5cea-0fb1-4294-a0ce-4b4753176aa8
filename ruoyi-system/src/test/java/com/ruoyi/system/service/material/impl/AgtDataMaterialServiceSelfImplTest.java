package com.ruoyi.system.service.material.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.bytedance.ads.ApiClient;
import com.bytedance.ads.ApiException;
import com.bytedance.ads.api.ReportCustomGetV30Api;
import com.bytedance.ads.model.ReportCustomGetV30FiltersInner;
import com.bytedance.ads.model.ReportCustomGetV30Response;
import com.bytedance.ads.model.ReportCustomGetV30ResponseDataRowsInner;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.entity.material.AgtDataMaterialEntity;
import com.ruoyi.system.entity.metrics.AgtDataMaterialMetricsEntity;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class AgtDataMaterialServiceSelfImplTest {


    /*
                        "": "13.22",
                    "": "34",
                    "": "14.71",
                    "": "5",
                    "": "25.56",
                    "cpm_platform": "431.75",
                    "ctr": "1.69",
                    "dislike_cnt": "1",
                    "dy_comment": "0",
                    "dy_follow": "3",
                    "dy_home_visited": "4",
                    "dy_like": "14",
                    "dy_share": "1",
                    "play_duration_3s": "810",
                    "play_over_rate": "0.62",
                    "report_cnt": "0",
                    "show_cnt": "2013",
                    "stat_cost": "869.11",
                    "total_play": "1944",
                    "valid_play": "358",
                    "valid_play_rate": "18.42"
     */
    @Test
    void getAdMaterialData() throws ApiException {
        ReportCustomGetV30Api api = new ReportCustomGetV30Api();
        ApiClient client = api.getApiClient();

        client.addDefaultHeader("Access-Token", "8e02fb835527898f323993cefc0696184d2576e4");
        api.setApiClient(client);

        List<String> dimensions = ListUtil.of("ad_platform_material_content","ad_platform_material_name", "image_mode", "material_id", "stat_time_hour");
        long advertiserId = 1802285545804811L;
        List<String> metrics = ListUtil.of("average_play_time_per_play", "click_cnt", "conversion_rate", "convert_cnt","cpc_platform",
                "cpm_platform", "ctr", "dislike_cnt", "dy_comment", "dy_follow", "dy_home_visited", "dy_like", "dy_share",
                "play_duration_3s", "play_over_rate", "report_cnt", "show_cnt", "stat_cost", "total_play", "valid_play", "valid_play_rate",
                "active","active_cost");

        ArrayList<ReportCustomGetV30FiltersInner> filters = new ArrayList<>();
        ReportCustomGetV30FiltersInner filter = new ReportCustomGetV30FiltersInner();
        filter.setField("image_mode");
        filter.setType(1L);
        filter.setOperator(0L);
        filter.setValues(ListUtil.of("5","15"));
        filters.add(filter);


        String yesterday = DateUtil.formatDate(DateUtil.yesterday());


        ReportCustomGetV30Response response =
                api.openApiV30ReportCustomGetGet(dimensions, advertiserId, metrics, filters, yesterday, yesterday,
                        new ArrayList<>(), 1, 15, null);

        ReportCustomGetV30ResponseDataRowsInner inner = response.getData().getRows().get(0);
        Map<String, String> dimensions1 = inner.getDimensions();
        String adPlatformMaterialContent = dimensions1.get("ad_platform_material_content");
        JSONObject jsonObject = JSONObject.parseObject(adPlatformMaterialContent);

        String img = jsonObject.getJSONObject("video_info").getString("img");
        System.out.println(img);

        AgtDataMaterialMetricsEntity agtDataMaterialMetricsEntity = new AgtDataMaterialMetricsEntity();

        BeanUtils.copyProperties(response.getData().getRows().get(0), agtDataMaterialMetricsEntity);

        Map<String, String> metrics1 = response.getData().getRows().get(0).getMetrics();


        AgtDataMaterialMetricsEntity agtDataMaterialMetricsEntity1 = BeanUtil.mapToBean(response.getData().getRows().get(0).getMetrics(), AgtDataMaterialMetricsEntity.class, true, new CopyOptions());


        System.out.println(agtDataMaterialMetricsEntity1);


        AgtDataMaterialEntity agtDataMaterialEntity = BeanUtil.mapToBean(response.getData().getRows().get(0).getDimensions(), AgtDataMaterialEntity.class, true, new CopyOptions());


        System.out.println(agtDataMaterialEntity);

        System.out.println(response);
    }

    @Test
    void testCeil() {
        String time = "2024-07-12 02:00 - 02:59";
        String[] split = time.split(" ");
        String date = split[0];
        String[] split1 = split[1].split(":");

        int hour = NumberUtil.parseInt(split1[0]);

        System.out.println();
    }

    @Test
    void content() {

        String playDuration3sRate = StrUtil.toUnderlineCase("playDuration3sRate");
        System.out.println(playDuration3sRate);
    }

}