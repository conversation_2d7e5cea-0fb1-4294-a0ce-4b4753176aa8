package com.ruoyi.system.service.liuyi.impl;

import cn.hutool.http.HttpUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;


class LiuyiServiceImplTest {

    @Test
    void test() {
        String token = "1d830345165093d6ae9e0464587c4aa4";

        String body = HttpUtil.createGet("https://liuyiyingshi.cn/cps/order/distributionOrderListVersionTwo?token=" + token+"&start_date=2024-09-13 00:00:00&end_date=2024-09-14 23:59:59&limit=100&page=1")
                .execute().body();

        System.out.println(body);
    }
}