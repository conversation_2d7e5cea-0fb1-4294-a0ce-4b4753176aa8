package com.ruoyi.system.service.dianzhong.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.LineHandler;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class DianzhongServiceTest {

    @Test
    void downloadTest() {
        // 要下载的文件URL
        String fileUrl = "https://xcxoss.dzyds.com/download/channel/20240820/10007931_alias_cpsvideo_order_1724118964667.txt?taskId=hB6Sksyx12qHLso";

        String taskId = "3134";

        // 保存文件的本地路径
        String savePath = "./dianzhong/" + taskId + ".txt";

        try {
            // 使用Hutool的HttpUtil下载文件
            long size = HttpUtil.downloadFile(fileUrl, FileUtil.file(savePath));

            System.out.println("文件下载成功!");
            System.out.println("下载文件大小: " + size + " 字节");
        } catch (Exception e) {
            System.err.println("文件下载失败: " + e.getMessage());
            e.printStackTrace();
        }

        FileReader reader = new FileReader(savePath);
        ArrayList<AgtDataSupplierSnapshotDianzhongEntity> entities = new ArrayList<>();
        reader.readLines((LineHandler) (line) -> {
            AgtDataSupplierSnapshotDianzhongEntity entity = JSONObject.parseObject(line, AgtDataSupplierSnapshotDianzhongEntity.class);
            // 处理那些对应不上的字段
            JSONObject jsonObject = JSONObject.parseObject(line);
            Date ctime = new Date(jsonObject.getLong("ctime") * 1000);
            entity.setCtime(ctime);
            Date registerDate = new Date(Long.valueOf(jsonObject.getString("registerDate")) * 1000);
            entity.setRegisterDate(registerDate);
            Date finishTime = new Date(jsonObject.getLong("finishTime") * 1000);
            entity.setFinishTime(finishTime);
            Date schannelTime = new Date(Long.valueOf(jsonObject.getString("schannelTime")) * 1000);
            entity.setSchannelTime(schannelTime);
            Date dyeTime = new Date(Long.valueOf(jsonObject.getString("dyeTime")) * 1000);
            entity.setDyeTime(dyeTime);

            String referralName = entity.getReferralName();
            if (StringUtils.isEmpty(referralName)) {
                return;
            }
            String[] split = referralName.split("-");
            String adId = split[0];
            if (NumberUtil.isLong(adId)) {
                entity.setAdvertiserId(adId);
            } else {
                entity.setAdvertiserId("0");
                log.error("点众订单推广名称解析失败: {} ，请立即通知投手修改。", referralName);
            }
            // 暂时用 finish time，作为 cur date hour 等字段
            DateTime curDate = DateUtil.beginOfDay(finishTime);
            int curHour = DateUtil.hour(finishTime, true);
            entity.setCurDate(curDate);
            entity.setCurHour(Long.valueOf(curHour));

            entities.add(entity);
        });
    }

}