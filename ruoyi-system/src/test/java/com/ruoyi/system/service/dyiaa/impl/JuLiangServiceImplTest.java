package com.ruoyi.system.service.dyiaa.impl;


import com.bytedance.ads.ApiCallback;
import com.bytedance.ads.ApiClient;
import com.bytedance.ads.ApiException;
import com.bytedance.ads.api.CustomerCenterAdvertiserCopyV2Api;
import com.bytedance.ads.api.CustomerCenterAdvertiserListV2Api;
import com.bytedance.ads.api.ReportAdvertiserGetV2Api;
import com.bytedance.ads.model.CustomerCenterAdvertiserListV2Response;
import com.bytedance.ads.model.ReportAdvertiserGetV2Response;
import lombok.SneakyThrows;
import okhttp3.Call;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;

import static org.junit.jupiter.api.Assertions.*;

class JuLiangServiceImplTest {



    @SneakyThrows
    @Test
    public void GetAdvertiserListWithAccountId() {


        CustomerCenterAdvertiserListV2Api api = new CustomerCenterAdvertiserListV2Api();
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", "dddf91fa2a852c52e75573549830caf4be04eee7");

        api.setApiClient(client);

        CustomerCenterAdvertiserListV2Response customerCenterAdvertiserListV2Response
                = api.openApi2CustomerCenterAdvertiserListGet(null, 1780879268725772L, null, 1L, 3L);

        System.out.println(customerCenterAdvertiserListV2Response);

    }


    @SneakyThrows
    @Test
    public void GetAdvertiserInfoById() {
        Semaphore semaphore = new Semaphore(0);
        ReportAdvertiserGetV2Api api = new ReportAdvertiserGetV2Api();
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", "dddf91fa2a852c52e75573549830caf4be04eee7");
        api.setApiClient(client);
        ReportAdvertiserGetV2Response response = api.openApi2ReportAdvertiserGetGet(1801562804272140L, "2024-06-26",
                null, null, null, null, null, 1L,
                10L, "2024-06-26", null);





    }

}