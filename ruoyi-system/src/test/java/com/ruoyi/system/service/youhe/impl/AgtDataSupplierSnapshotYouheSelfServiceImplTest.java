package com.ruoyi.system.service.youhe.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.entity.youhe.YouheResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import static org.junit.jupiter.api.Assertions.*;

class AgtDataSupplierSnapshotYouheSelfServiceImplTest {


    /*
    youhe:
  wechat_id: 196
  app_key: 4526791196
  secret: 43466c4a71bfe865906c65da9a9863d4
  username: nhbojc0531
  password: Nhbojc@0531
  host: https://open-playlet.64xiaoshuo.cn
     */

    /**
     * 不填startTime 默认一小时前
     */
    @Test
    void getOrderInfo() {
        StringBuilder param = new StringBuilder();
        param.append("wechat_id=").append(196)
                .append("&app_key=").append("4526791196")
                .append("&secret=").append("43466c4a71bfe865906c65da9a9863d4")
                .append("&username=").append("nhbojc0531")
                .append("&password=").append("Nhbojc@0531");
//                .append("&num=").append(1)
//                .append("&page=").append(10);
        String response = HttpUtils.sendGet("https://open-playlet.64xiaoshuo.cn/third/order/getOrderLists", param.toString());

        YouheResponse youheResponse = JSONObject.parseObject(response, YouheResponse.class);

        System.out.println(youheResponse);
    }

    @Test
    void checkName() {
        String legalAdvertiserId = getLegalAdvertiserId("1805893384332297-妈妈我错了超低2");

        System.out.println(legalAdvertiserId);
    }

    private String getLegalAdvertiserId(String promotionsName) {
        // 去除空格
        promotionsName = promotionsName.replaceAll("\\s+", "");
        if (!StringUtils.contains(promotionsName, "-")) {
            return "";
        }
        String[] nameSplit = promotionsName.split("-");
        String advertiserId = nameSplit[0];
        // 前面是广告账户id
        if (!NumberUtil.isLong(advertiserId)) {
            return "";
        }
        return advertiserId;
    }
}