package com.ruoyi.system.service.nuohe.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity;
import com.ruoyi.system.entity.nuohe.NuoheOrder;
import com.ruoyi.system.entity.nuohe.NuoheOrderResp;
import org.junit.jupiter.api.Test;

import javax.xml.crypto.Data;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class NuoheServiceImplTest {

    @Test
    void getOrder() {
        String organizationId = "f380561066c45a6707b441eb140cd5cd";
        Date date = new Date();
        String start = DateUtil.format(DateUtil.beginOfDay(date), DatePattern.NORM_DATETIME_PATTERN);
        String end = DateUtil.format(DateUtil.endOfDay(date), DatePattern.NORM_DATETIME_PATTERN);
        HashMap<String, String> reqParamMap = new HashMap<>();
        // 此处 id，是机构 id
        reqParamMap.put("id","f380561066c45a6707b441eb140cd5cd");
        reqParamMap.put("startTime",start);
        reqParamMap.put("endTime", end);
        reqParamMap.put("pageNum", "1");
        reqParamMap.put("pageSize", "1");
        reqParamMap.put("secret", "mL2l4Y6SEuL534TutNEfZJFCMQmDajQt");

        StringBuilder param = new StringBuilder();
        reqParamMap.forEach((k, v) -> {
            param.append(k).append("=").append(v).append("&");
        });
        param.deleteCharAt(param.length() - 1);

        String resp = HttpUtil.createGet("https://tcb-sjzsxaxmi7c3ybb-1cp15f4b3c5b.service.tcloudbase.com/agent_data?"
                + param).execute().body();

        NuoheOrderResp nuoheOrderResp = JSONObject.parseObject(resp, NuoheOrderResp.class);
        List<NuoheOrder> data = nuoheOrderResp.getData();
        for (NuoheOrder order : data) {

            AgtDataSupplierSnapshotNuoheEntity entity = new AgtDataSupplierSnapshotNuoheEntity();
            BeanUtil.copyProperties(order,entity);
            entity.setOrganizationId(organizationId);
            entity.setAppId(order.getAppid());
            entity.setAppName(order.getAppname());
            System.out.println(entity);
        }
    }

}