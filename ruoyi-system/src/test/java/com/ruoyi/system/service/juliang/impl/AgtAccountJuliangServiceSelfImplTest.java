package com.ruoyi.system.service.juliang.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bytedance.ads.ApiClient;
import com.bytedance.ads.ApiException;
import com.bytedance.ads.api.BusinessPlatformCompanyInfoGetV30Api;
import com.bytedance.ads.api.CustomerCenterAdvertiserListV2Api;
import com.bytedance.ads.api.Oauth2AdvertiserGetApi;
import com.bytedance.ads.api.ReportAdvertiserGetV2Api;
import com.bytedance.ads.model.*;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

class AgtAccountJuliangServiceSelfImplTest {

    @Test
    void businessPlatformCompanyInfo() {
        BusinessPlatformCompanyInfoGetV30Api api = new BusinessPlatformCompanyInfoGetV30Api();
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", "9ecdf5a5aafa2b89e5d919d595bee6fa2dcc8fa8");
        api.setApiClient(client);

        try {
            BusinessPlatformCompanyInfoGetV30Response response = api.openApiV30BusinessPlatformCompanyInfoGetGet(1739341090482190L, 1, 10);
//            System.out.println(response);
            Assert.hasText(response.getData().getCompanyInfo().get(0).getCompanyName(), "杭州芬奇学堂教育科技有限公司");
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void plus24Hours() {
        // 获取当前日期和时间
        DateTime now = DateUtil.date();
        System.out.println("当前日期和时间: " + now);

        // 将当前日期和时间加上24小时
        DateTime newTime = DateUtil.offsetHour(now, 24);
        System.out.println("加上24小时后的日期和时间: " + newTime);
    }

    @SneakyThrows
    @Test
    void getAccountBelongToAdvertiser() {
        CustomerCenterAdvertiserListV2Api api = new CustomerCenterAdvertiserListV2Api();
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", "9ecdf5a5aafa2b89e5d919d595bee6fa2dcc8fa8");
        api.setApiClient(client);

//        CustomerCenterAdvertiserListV2Response listGet = api.openApi2CustomerCenterAdvertiserListGet(null, ****************L, null, 1L, 100L);

//        System.out.println(listGet);
        /*
        class CustomerCenterAdvertiserListV2Response {
    code: 40002
    data: null
    message: Account **************** doesn't exist or the role is wrong
    requestId: 2024062714215509A24C12D832B2E81579
}
         */

        CustomerCenterAdvertiserListV2Response listGet = api.openApi2CustomerCenterAdvertiserListGet(null, 1739341090482190L, null, 1L, 100L);
        System.out.println(listGet);
        // 这个是没问题的，能查到数据。

    }

    @SneakyThrows
    @Test
    void getAdvertiserInfo() {
        ReportAdvertiserGetV2Api api = new ReportAdvertiserGetV2Api();
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", "d0cdc00eccf718f6bfcf2cf62a03a029475da9c7");
        api.setApiClient(client);

        ArrayList<String> list = new ArrayList<>();
        list.add("cost");
        list.add("show");
        list.add("avg_show_cost");
        list.add("click");
        list.add("avg_click_cost");
        list.add("ctr");
        list.add("convert");
        list.add("convert_cost");
        list.add("convert_rate");
        ReportAdvertiserGetV2Response response = api.openApi2ReportAdvertiserGetGet(1801562895128026L,
                "2024-06-28", list,
                null, null, null
                , null, 1L, 10L, "2024-06-28", null);

        System.out.println(response);

    }

    @Test
    void getDate() {
//        String currentDateStr = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN);
//        System.out.println("当前日期 (yyyy-MM-dd): " + currentDateStr);
        long between = DateUtil.between(DateUtil.date(), DateUtil.endOfDay(new Date()), DateUnit.HOUR);

        System.out.println("当前时间距离今天结束大于 23 小时");

    }

    @Test
    void getTokenAccount() throws ApiException {
        Oauth2AdvertiserGetApi api = new Oauth2AdvertiserGetApi();

        Oauth2AdvertiserGetResponse response = api.openApiOauth2AdvertiserGetGet("e2e9a2f4c7aff6af500093ea9cf036db1b928aa0");
        System.out.println(response);
    }

    @Test
    void accountSplit() {
        String s = "****************";
        String[] split = s.split(",");
        ArrayList<Long> accountIds = new ArrayList<>();
        for (String account : split) {
            accountIds.add(Long.parseLong(account));
        }
        System.out.println(accountIds);
    }



}