package com.ruoyi.system.service.dianzhong.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class AgtDataSupplierSnapshotDianzhongServiceImplTest {

    @Test
    void postDian() {
        long clientId = 10007931;
        String token = "2bArpcjo5gPEsSQzJF";

        Date cur = new Date();
        long sdate = DateUtil.beginOfDay(cur).getTime() / 1000;
        long edate = DateUtil.endOfDay(cur).getTime() / 1000;

        long timestamp = System.currentTimeMillis();

        String signKey = getSignKey(clientId, token, timestamp);
        String queryField = "orderStatus";

        JSONObject requestBody = new JSONObject();
        requestBody.put("clientId", clientId);
        requestBody.put("timestamp", timestamp);
        requestBody.put("signKey", signKey);
        requestBody.put("queryField", queryField);
        requestBody.put("sdate", sdate);
        requestBody.put("edate", edate);
        String body = HttpUtil.createPost("https://routine.wqxsw.com/flames/channel/query/order")
                .body(requestBody.toJSONString()).execute().body();

        //{
        //  "retCode" : 0,
        //  "retMsg" : "成功",
        //  "data" : "eA90zK56fjDhqHB"
        //}
        log.info("拉去点众订单数据，taskId：{}", body);

    }

    /**
     * 签名秘钥(小写md5(clientId+接口token+timestamp)
     *
     * @param clientId
     * @param token
     * @param timestamp
     * @return
     */
    private String getSignKey(long clientId, String token, long timestamp) {
        // md5(clientId+token+timestamp)
        return Md5Util.MD5(clientId + token + timestamp);
    }

    @Test
    void postTou() {
        long clientId = 10007931;
        String token = "2bArpcjo5gPEsSQzJF";

        long timestamp = System.currentTimeMillis();

        String signKey = getSignKey(clientId, token, timestamp);

        JSONObject requestBody = new JSONObject();
        requestBody.put("clientId", clientId);
        requestBody.put("timestamp", timestamp);
        requestBody.put("signKey", signKey);
        String body = HttpUtil.createPost("https://routine.wqxsw.com/flames/channel/query/channelInfoList")
                .body(requestBody.toJSONString()).execute().body();

        System.out.println(body);

        JSONObject resp = JSONObject.parseObject(body);
        if (resp == null || resp.getInteger("retCode") != 0) {
            // 渠道就是投手
            log.warn("获取渠道信息失败: {}", resp.toString());
            return;
        }
        JSONArray channelInfoList = resp.getJSONObject("data").getJSONArray("channelInfoList");
        for (int i = 0; i < channelInfoList.size(); i++) {
            JSONObject channelInfo = channelInfoList.getJSONObject(i);

        }


    }
}