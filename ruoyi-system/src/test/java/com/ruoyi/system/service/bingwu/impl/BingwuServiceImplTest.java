package com.ruoyi.system.service.bingwu.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class BingwuServiceImplTest {

/*
OkHttpClient client = new OkHttpClient().newBuilder()
   .build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"page_size\": 10,\n    \"page_num\": 1,\n    \"start_time\": \"2024-07-20 00:00:00\",\n    \"end_time\": \"2024-07-30 23:59:59\"\n}");
Request request = new Request.Builder()
   .url("https://vnovel-out.bwumedia.com/api/order/query?key=akXSSjEnCWzmbks23n")
   .method("POST", body)
   .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
   .addHeader("Content-Type", "application/json")
   .addHeader("Accept", "*//*")
            .addHeader("Host", "vnovel-out.bwumedia.com")
   .addHeader("Connection", "keep-alive")
   .build();
    Response response = client.newCall(request).execute();*/

    @Test
    void getOrder() {
        String key = "akXSSjEnCWzmbks23n";

        JSONObject requestBody = new JSONObject();
        requestBody.put("page_size", 10);
        requestBody.put("page_num", 1);
        requestBody.put("start_time", "2024-07-20 00:00:00");
        requestBody.put("end_time", "2024-07-30 23:59:59");
        String body = HttpUtil.createPost("https://vnovel-out.bwumedia.com/api/order/query?key=" + key)
                .body(requestBody.toString()).execute().body();

        System.out.println(body);
    }

}