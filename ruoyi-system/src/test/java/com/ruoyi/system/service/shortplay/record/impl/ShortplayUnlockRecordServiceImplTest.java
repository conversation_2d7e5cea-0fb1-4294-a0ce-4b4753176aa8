package com.ruoyi.system.service.shortplay.record.impl;

import com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity;
import com.ruoyi.system.mapper.shortplay.record.ShortplayUnlockRecordMapper;
import com.ruoyi.system.service.shortplay.record.ShortplayUnlockRecordService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * ShortplayUnlockRecordService批量插入测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ShortplayUnlockRecordServiceImplTest {

    @Mock
    private ShortplayUnlockRecordMapper shortplayUnlockRecordMapper;

    @InjectMocks
    private ShortplayUnlockRecordServiceImpl shortplayUnlockRecordService;

    private List<ShortplayUnlockRecordEntity> testRecords;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testRecords = new ArrayList<>();
        
        ShortplayUnlockRecordEntity record1 = new ShortplayUnlockRecordEntity();
        record1.setAppId("testApp001");
        record1.setUserId(12345L);
        record1.setTvId(67890L);
        record1.setSeries(1);
        
        ShortplayUnlockRecordEntity record2 = new ShortplayUnlockRecordEntity();
        record2.setAppId("testApp001");
        record2.setUserId(12345L);
        record2.setTvId(67890L);
        record2.setSeries(2);
        
        ShortplayUnlockRecordEntity record3 = new ShortplayUnlockRecordEntity();
        record3.setAppId("testApp001");
        record3.setUserId(12345L);
        record3.setTvId(67890L);
        record3.setSeries(3);
        
        testRecords.add(record1);
        testRecords.add(record2);
        testRecords.add(record3);
    }

    /**
     * 测试批量插入成功场景
     */
    @Test
    void testBatchInsert_Success() {
        // 模拟mapper返回成功插入的记录数
        when(shortplayUnlockRecordMapper.batchInsert(anyList())).thenReturn(3);

        // 执行测试
        int result = shortplayUnlockRecordService.batchInsert(testRecords);

        // 验证结果
        assertEquals(3, result, "批量插入应该返回插入的记录数");
        
        // 验证mapper方法被调用
        verify(shortplayUnlockRecordMapper, times(1)).batchInsert(testRecords);
    }

    /**
     * 测试空列表场景
     */
    @Test
    void testBatchInsert_EmptyList() {
        List<ShortplayUnlockRecordEntity> emptyList = Collections.emptyList();

        // 执行测试
        int result = shortplayUnlockRecordService.batchInsert(emptyList);

        // 验证结果
        assertEquals(0, result, "空列表应该返回0");
        
        // 验证mapper方法未被调用
        verify(shortplayUnlockRecordMapper, never()).batchInsert(any());
    }

    /**
     * 测试null列表场景
     */
    @Test
    void testBatchInsert_NullList() {
        // 执行测试
        int result = shortplayUnlockRecordService.batchInsert(null);

        // 验证结果
        assertEquals(0, result, "null列表应该返回0");
        
        // 验证mapper方法未被调用
        verify(shortplayUnlockRecordMapper, never()).batchInsert(any());
    }

    /**
     * 测试单条记录批量插入
     */
    @Test
    void testBatchInsert_SingleRecord() {
        List<ShortplayUnlockRecordEntity> singleRecord = Arrays.asList(testRecords.get(0));
        
        // 模拟mapper返回成功插入的记录数
        when(shortplayUnlockRecordMapper.batchInsert(anyList())).thenReturn(1);

        // 执行测试
        int result = shortplayUnlockRecordService.batchInsert(singleRecord);

        // 验证结果
        assertEquals(1, result, "单条记录批量插入应该返回1");
        
        // 验证mapper方法被调用
        verify(shortplayUnlockRecordMapper, times(1)).batchInsert(singleRecord);
    }

    /**
     * 测试大批量数据插入
     */
    @Test
    void testBatchInsert_LargeBatch() {
        // 创建大批量测试数据
        List<ShortplayUnlockRecordEntity> largeBatch = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            ShortplayUnlockRecordEntity record = new ShortplayUnlockRecordEntity();
            record.setAppId("testApp001");
            record.setUserId(12345L);
            record.setTvId(67890L);
            record.setSeries(i);
            largeBatch.add(record);
        }
        
        // 模拟mapper返回成功插入的记录数
        when(shortplayUnlockRecordMapper.batchInsert(anyList())).thenReturn(100);

        // 执行测试
        int result = shortplayUnlockRecordService.batchInsert(largeBatch);

        // 验证结果
        assertEquals(100, result, "大批量插入应该返回正确的记录数");
        
        // 验证mapper方法被调用
        verify(shortplayUnlockRecordMapper, times(1)).batchInsert(largeBatch);
    }

    /**
     * 测试包含null series的记录（这种情况在Controller层已经过滤，但Service层应该能处理）
     */
    @Test
    void testBatchInsert_WithNullSeries() {
        List<ShortplayUnlockRecordEntity> recordsWithNull = new ArrayList<>();
        
        ShortplayUnlockRecordEntity record1 = new ShortplayUnlockRecordEntity();
        record1.setAppId("testApp001");
        record1.setUserId(12345L);
        record1.setTvId(67890L);
        record1.setSeries(1);
        
        ShortplayUnlockRecordEntity record2 = new ShortplayUnlockRecordEntity();
        record2.setAppId("testApp001");
        record2.setUserId(12345L);
        record2.setTvId(67890L);
        record2.setSeries(null); // null series
        
        recordsWithNull.add(record1);
        recordsWithNull.add(record2);
        
        // 模拟mapper返回成功插入的记录数
        when(shortplayUnlockRecordMapper.batchInsert(anyList())).thenReturn(2);

        // 执行测试
        int result = shortplayUnlockRecordService.batchInsert(recordsWithNull);

        // 验证结果
        assertEquals(2, result, "包含null series的记录也应该能正常处理");
        
        // 验证mapper方法被调用
        verify(shortplayUnlockRecordMapper, times(1)).batchInsert(recordsWithNull);
    }

    /**
     * 测试mapper异常场景
     */
    @Test
    void testBatchInsert_MapperException() {
        // 模拟mapper抛出异常
        when(shortplayUnlockRecordMapper.batchInsert(anyList()))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            shortplayUnlockRecordService.batchInsert(testRecords);
        }, "mapper异常应该被抛出");
        
        // 验证mapper方法被调用
        verify(shortplayUnlockRecordMapper, times(1)).batchInsert(testRecords);
    }

    /**
     * 测试部分插入成功场景（返回值小于输入记录数）
     */
    @Test
    void testBatchInsert_PartialSuccess() {
        // 模拟mapper返回部分成功插入的记录数（可能由于重复键等原因）
        when(shortplayUnlockRecordMapper.batchInsert(anyList())).thenReturn(2);

        // 执行测试
        int result = shortplayUnlockRecordService.batchInsert(testRecords);

        // 验证结果
        assertEquals(2, result, "部分插入成功应该返回实际插入的记录数");
        
        // 验证mapper方法被调用
        verify(shortplayUnlockRecordMapper, times(1)).batchInsert(testRecords);
    }
}
