package com.ruoyi.system.service.liuyi.impl;

import cn.hutool.http.HttpUtil;
import org.junit.jupiter.api.Test;

/**
 * @author: keboom
 * @date: 2024/9/14
 */
class TestOrder {


    @Test
    void test() {
        String token = "1d830345165093d6ae9e0464587c4aa4";

        String body = HttpUtil.createGet("https://liuyiyingshi.cn/cps/order/distributionOrderListVersionTwo?token=" + token)
                .execute().body();

        System.out.println(body);
    }
}
