package com.ruoyi.system.service.lianfan.impl;

import cn.hutool.Hutool;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class AgtDataSupplierSnapshotLianfanSelfServiceImplTest {

    String host = "https://oper.lsttonline.com/prod-api";
    String apiId = "WgfrXftSlcBZ";
    String apiSecret = "upOt1Slg4PQV4l2DcgUlPyIsQ2feakXR8XM9";

    int pageNum = 1;
    int pageSize = 1;

    HashMap<Long, String> linkIdPromotionMap = new HashMap<>();

    /**
     * {
     * "total": 29,
     * "rows": [
     * {
     * "linkId": 11736,
     * "userName": "jxff",
     * "createTime": "2024-07-17 11:29:13",
     * "playletCode": "op_1811275786271854592",
     * "promotionName": "1804721873039497-我的女儿-小额1",
     * "appId": "wx7bc47b8a4ead8ad5",
     * "appType": "wx_mini",
     * "putPath": "/pages/client/video/index?dramaId=op_1811275786271854592&linkId=11736&dramaSubId=ov_1811283355161858048&dt=1721217684120"
     * }
     * ],
     * "code": 200,
     * "msg": "查询成功"
     * }
     */
    @Test
    void getPromotionList() {

        JSONObject resp = getPromotionList(pageNum, pageSize);

        Integer total = resp.getInteger("total");
        int pageTotal = total / pageSize;
        // 上面已经查询了一页了，我们这里从第二页开始查询
        pageNum++;
        while (pageNum <= pageTotal) {
            getPromotionList(pageNum, pageSize);
            pageNum++;
        }

        log.info("linkIdPromotionMap: {}", linkIdPromotionMap);
    }

    public JSONObject getPromotionList(int pageNum, int pageSize) {
        String param = "pageNum=" + pageNum +
                "&pageSize=" + pageSize;
        long curTime = System.currentTimeMillis()/1000;
        String apiToken = DigestUtil.sha1Hex(apiSecret + curTime);
        String body = HttpUtil.createGet(host + "/cddist/third/promotion/list?" + param)
                .header("apiId", apiId)
                .header("apiToken", apiToken)
                .header("timestamp", String.valueOf(curTime))
                .header("Content-Type", "application/json")
                .execute().body();
        JSONObject resp = null;
        try {
            resp = JSONObject.parseObject(body);
        } catch (Exception e) {
            log.error("获取推广列表失败: {}", body);
            return null;
        }
        if (resp == null) {
            return null;
        }
        if (resp.getInteger("code") != 200) {
            log.error("获取推广列表失败: {}", body);
            return null;
        }
        JSONArray rows = resp.getJSONArray("rows");
        // 处理 rows，存到 本地就行，没人回去修改他，我们每次都会拉取最新 linkid 和 promotionName
        for (int i = 0; i < rows.size(); i++) {
            JSONObject row = rows.getJSONObject(i);
            linkIdPromotionMap.put(row.getLong("linkId"), row.getString("promotionName"));
        }
        return JSONObject.parseObject(body);
    }

    /**
     * {
     * "total": 79,
     * "rows": [
     * {
     * "orderId": 1813530697195134976,
     * "orderStatus": 1,
     * "productType": 1,
     * "createTime": "2024-07-17T19:06:25.000+08:00",
     * "chargeAmount": 6.90,
     * "userId": "991830413",
     * "externalOrderId": "4200002352202407179245681397",
     * "linkId": 11736,
     * "linkUsername": "jxff",
     * "playletCode": "op_1811275786271854592",
     * "playletName": "我的女儿是至尊",
     * "miniAppId": "wx7bc47b8a4ead8ad5",
     * "miniAppName": "豆果短剧",
     * "refundOrderId": 1813530697195134976,
     * "linkDt": "2024-07-17 18:44:06",
     * "linkSourceChannel": "1",
     * "paymentModel": "android",
     * "appType": "wx_mini",
     * "projectId": "7392474122256039975",
     * "promotionId": "7392474490695909388",
     * "registerTime": "2024-07-17T18:44:06.000+08:00"
     * }
     * ],
     * "code": 200,
     * "msg": "查询成功"
     * }
     */
    @Test
    void getOrderList() {
        String param = "pageNum=" + pageNum +
                "&pageSize=" + pageSize;

        JSONObject requestBody = new JSONObject();
        requestBody.put("orderCreateDate", "2024-07-17");
        String respStr = HttpUtil.createPost(host + "/cddist/third/order/infoList?" + param)
                .header("apiId", apiId)
                .header("apiSecret", apiSecret)
                .header("Content-Type", "application/json")
                .body(requestBody.toJSONString())
                .execute().body();

        log.info("respStr: {}", respStr);
        if (StringUtils.isEmpty(respStr)) {
            return;
        }
        JSONObject resp;
        try {
            resp = JSONObject.parseObject(respStr);
        } catch (Exception e) {
            log.error("获取订单列表失败: {}", respStr);
            return;
        }
        if (resp.getInteger("code") != 200) {
            log.error("获取订单列表失败: {}", respStr);
            return;
        }
        JSONArray rows = resp.getJSONArray("rows");

        log.info("rows: {}", rows);


    }


    @Test
    void getTime() {
        DateTime dateTime = DateUtil.beginOfDay(DateUtil.yesterday());
        DateTime end = DateUtil.endOfDay(DateUtil.yesterday());
        System.out.println(dateTime.getTime());
        System.out.println(end.getTime());
    }
}