package com.ruoyi.system.service.chumo.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.entity.chumo.ChumoOrderResp;
import com.ruoyi.system.entity.chumo.ChumoTokenResp;
import com.ruoyi.system.entity.youhe.YouheResponse;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

class AgtDataSupplierSnapshotChumoSelfServiceImplTest {

    @Test
    void getToken() {
        String host = "https://tsd.api.cmappgogo.cn";
        String secret = "05fcc712ccc63694c3499dda090e6395";
        StringBuilder param = new StringBuilder();
        param.append("secret_key=").append(secret);
        String response = HttpUtils.sendGet(host + "/open/V1/getAuthToken", param.toString());
        ChumoTokenResp chumoTokenResp = JSONObject.parseObject(response, ChumoTokenResp.class);

        System.out.println(chumoTokenResp);

        String token="321bf2a3b01c398836a9544edbb61893a5277a0cf7f77996c7fd7beec307ece8";
    }

    @Test
    void getOrder() {
        String host = "https://tsd.api.cmappgogo.cn";

        JSONObject body = new JSONObject();
        body.put("start_date", "2024-07-09 12:04:05");
        body.put("end_date", "2024-07-09 13:04:05");
        body.put("state", 1);
        body.put("page", 1);
        body.put("size", 100);

        String resp = HttpUtil.createPost(host + "/open/V1/getOrderList")
                .header("Authorization", "Bearer 321bf2a3b01c398836a9544edbb61893a5277a0cf7f77996c7fd7beec307ece8")
                .body(body.toString(), ContentType.JSON.toString()).execute().body();

        ChumoOrderResp chumoOrderResp = JSONObject.parseObject(resp, ChumoOrderResp.class);

        System.out.println(chumoOrderResp);
    }

    @Test
    void testDate() {

        String isoDate = "2024-07-09T11:39:01+08:00";

        // 解析 ISO 8601 日期时间格式
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(isoDate);
        System.out.println("Parsed ZonedDateTime: " + zonedDateTime);


        System.out.println(Date.from(ZonedDateTime.parse(isoDate).toInstant()));

        // 格式化为 ISO 8601 日期时间格式
        String formattedDate = zonedDateTime.format(DateTimeFormatter.ISO_ZONED_DATE_TIME);
        System.out.println("Formatted ZonedDateTime: " + formattedDate);
    }
}