package com.ruoyi.system.service.fundStat.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.bytedance.ads.ApiClient;
import com.bytedance.ads.ApiException;
import com.bytedance.ads.api.AdvertiserFundDailyStatV2Api;
import com.bytedance.ads.api.CustomerCenterAdvertiserListV2Api;
import com.bytedance.ads.model.AdvertiserFundDailyStatV2AccountType;
import com.bytedance.ads.model.AdvertiserFundDailyStatV2Response;
import com.bytedance.ads.model.AdvertiserFundDailyStatV2ResponseDataListInner;
import com.bytedance.ads.model.AdvertiserFundDailyStatV2ResponseDataPageInfo;
import com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
class AgtAdFundDailyStatSelfServiceImplTest {

    @Test
    void getAllAdFundDailyStat() throws ApiException {
        // 拉取所有广告流水数据，一般来说是拉取昨天的。我们这里传日期参数。
        // 我们一直在拉取所有账户，这里就不去巨量拉取所有账户了。直接从数据库中取了。
        // 1. 查询所有账户

        // 2. 遍历账户，查询账户下的所有广告流水
        AdvertiserFundDailyStatV2Api api = new AdvertiserFundDailyStatV2Api();
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", "af81232d93cdc9dce3f55ef751b9d9615bc40806");
        api.setApiClient(client);

        AdvertiserFundDailyStatV2Response response = api.openApi2AdvertiserFundDailyStatGet(1800818271343690L,
                "2024-07-24", "2024-07-24",
                1L, 5L, AdvertiserFundDailyStatV2AccountType.AD);

        if (response == null || response.getCode() != 0 || response.getData() == null){
            log.warn("拉取广告流水数据失败，response: {}", response);
        }

        List<AdvertiserFundDailyStatV2ResponseDataListInner> list = response.getData().getList();
        ArrayList<AgtAdvertiserFundDailyStatEntity> saveList = new ArrayList<>(list.size());
        for (AdvertiserFundDailyStatV2ResponseDataListInner inner : list) {
            AgtAdvertiserFundDailyStatEntity entity = BeanUtil.copyProperties(inner, AgtAdvertiserFundDailyStatEntity.class);
            saveList.add(entity);
            // 3. 保存到数据库
        }
//        saveToDB(saveList);

        AdvertiserFundDailyStatV2ResponseDataPageInfo pageInfo = response.getData().getPageInfo();
        Long curPage = pageInfo.getPage();
        curPage++;
        while (curPage <= pageInfo.getTotalPage()) {
            response = api.openApi2AdvertiserFundDailyStatGet(1800818271343690L,
                    "2024-07-24", "2024-07-24",
                    curPage, 5L, AdvertiserFundDailyStatV2AccountType.AD);
            if (response == null || response.getCode() != 0 || response.getData() == null){
                log.warn("拉取广告流水数据失败，response: {}", response);
            }
            curPage++;
        }


        System.out.println(response);

    }

    @Test
    void compareDate() {
        DateTime date1 = DateUtil.parse("2024-07-24");

        DateTime date2 = DateUtil.parse("2024-07-25");

        System.out.println(date1.isBefore(date2));
    }
}