package com.ruoyi.system.service.fanqie.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class FanqieServiceImplTest {

    @Test
    void getOrderInfoSaveToDB() {
    }

    @Test
    void selectIncomeByDay() {
    }


    String distributor_id = "1806172584541347";
    String secretKey = "HANGJx78bZdRRnP0ODzdtg0MZL46zG64";

    @Test
    void testFanQie() {

        // 总流程：
        // 1. 获取所有 distributor_id。这个频率可以低一点。比如每天一次。如果有新投手入职，我们手动触发即可。本地缓存即可。
        // 2. 获取所有promotion 列表. 因为获取的参数有时间显示，三天前的没法获取。所以我们把他存起来。一天一次定时拉去。
        // 3. 获取每个 distributor_id 下的订单。抖小订单没有虚拟 非虚拟。只有微小有这个字段。结合推广名称，入库。
//        ArrayList<String> allDistributorId = getAllDistributorId2();

        ArrayList<String> allDistributorId = new ArrayList<>();
        // 凡富 杨勇鹏
        allDistributorId.add("1806172386167947");
        for (String distributorId : allDistributorId) {

            DateTime startTime = DateUtil.beginOfDay(new Date());
            DateTime endTime = DateUtil.endOfDay(new Date());


            int offset = 0;
            int limit = 500;
            StringBuilder param = new StringBuilder();
            long begin = TimeUnit.MILLISECONDS.toSeconds(startTime.getTime());
            long end = TimeUnit.MILLISECONDS.toSeconds(endTime.getTime());
            long ts = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
            String sign = getSign(distributorId, secretKey, ts);

            param.append("distributor_id=").append(distributorId).append("&");
            param.append("ts=").append(ts).append("&");
            param.append("sign=").append(sign).append("&");
            param.append("begin=").append(begin).append("&");
            param.append("end=").append(end).append("&");
            param.append("paid=").append(true).append("&");
            param.append("offset=").append(offset).append("&");
            param.append("limit=").append(limit);

            String body = HttpUtil.createGet("https://www.changdunovel.com/novelsale/openapi/user/recharge/v1?"
                    + param.toString()).execute().body();

            JSONObject resp = JSONObject.parseObject(body);
            if (resp.getInteger("code") != 200) {
                log.error("获取番茄订单列表失败 result: {}", resp.toString());
                continue;
            }
        }


    }


    /**
     * 这个方法，是获取所有 distributor_id
     */
    @Test
    void getAllDistributorId() {
        ArrayList<Integer> appTypeList = new ArrayList<>();
        appTypeList.add(4);
        appTypeList.add(7);
        ArrayList<String> packageList = new ArrayList<>();
        for (Integer appType : appTypeList) {
            List<Long> packageList2 = getPackageList(appType);

            /// -----------------

            for (Long appid : packageList2) {

                List<String> packageList1 = getBoundPackageList(appid);
                packageList.addAll(packageList1);
            }

        }
        System.out.println(packageList);


    }

    /**
     * 先获取抖小和微小的 appid。因为抖小和微小下面可能有多个小程序
     * 然后在获取每个小程序下面的 distributor_id。就是每个小程序下的投手。
     *
     * @return
     */
    ArrayList<String> getAllDistributorId2() {
        ArrayList<Integer> appTypeList = new ArrayList<>();
        //- 微信短剧 = 4   抖音短剧 = 7
        appTypeList.add(4);
        appTypeList.add(7);
        ArrayList<String> packageList = new ArrayList<>();
        for (Integer appType : appTypeList) {
            List<Long> packageList2 = getPackageList(appType);

            /// -----------------

            for (Long appid : packageList2) {

                List<String> packageList1 = getBoundPackageList(appid);
                packageList.addAll(packageList1);
            }

        }
        System.out.println(packageList);
        return packageList;

    }


    List<Long> getPackageList(Integer appType) {
        int pageIndex = 0;
        int pageSize = 50;
        StringBuilder param = new StringBuilder();

        param.append("page_index").append(pageIndex).append("&");
        param.append("page_size").append(pageSize).append("&");

        param.append("distributor_id=").append(distributor_id).append("&");
        long ts = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        param.append("ts=").append(ts).append("&");

        String sign = getSign(distributor_id, secretKey, ts);
        param.append("sign=").append(sign).append("&");

        //  微信短剧 = 4   抖音短剧 = 7
        param.append("app_type=").append(appType);


        String body = HttpUtil.createGet("https://www.changdunovel.com/novelsale/openapi/wx/get_package_list/v2?" + param.toString()).execute().body();


        JSONObject resp = JSONObject.parseObject(body);
        if (resp.getInteger("code") != 200) {
            log.error("获取包列表失败 result: {}", resp.toString());
        }
        if (resp.getInteger("total") > pageSize) {
            log.error("微小或者抖小下 的小程序数量超过50个了！请注意分页获取！ result: {}", resp.toString());
        }
        JSONArray openList = resp.getJSONArray("package_info_open_list");
        ArrayList<Long> appIds = new ArrayList<>();
        for (int i = 0; i < openList.size(); i++) {
            Long id = openList.getJSONObject(i).getLong("app_id");
            appIds.add(id);
        }

        return appIds;
    }


    List<String> getBoundPackageList(Long appId) {
        int pageIndex = 0;
        int pageSize = 50;
        StringBuilder param = new StringBuilder();

        param.append("page_index").append(pageIndex).append("&");
        param.append("page_size").append(pageSize).append("&");


        param.append("distributor_id=").append(distributor_id).append("&");
        long ts = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        param.append("ts=").append(ts).append("&");

        String sign = getSign(distributor_id, secretKey, ts);
        param.append("sign=").append(sign).append("&");

        param.append("app_id=").append(appId);

        String body = HttpUtil.createGet("https://www.changdunovel.com/novelsale/openapi/wx/get_bound_package_list/v1?" + param.toString()).execute().body();

        JSONObject resp = JSONObject.parseObject(body);
        if (resp.getInteger("code") != 200) {
            log.error("获取包列表失败 result: {}", resp.toString());
        }
        if (resp.getInteger("total") > pageSize) {
            log.error("小程序下的投手超过50个了！请注意分页获取！！！ result: {}", resp.toString());
        }

        ArrayList<String> list = new ArrayList<>();

        JSONArray openList = resp.getJSONArray("wx_package_info_open_list");
        for (int i = 0; i < openList.size(); i++) {
            JSONObject jsonObject = openList.getJSONObject(i);
            String distributorId1 = jsonObject.getString("distributor_id");

            list.add(distributorId1);
        }

        return list;

    }


    public static String getSign(String distributor_id, String secretKey, long ts) {
        List<Object> params = new ArrayList<>();
        params.add(distributor_id);
        params.add(secretKey);
        params.add(ts);
        String paramStr = params.toString()
                .replace("[", "")
                .replace("]", "")
                .replace(", ", "");
        return SecureUtil.md5(paramStr);
    }


    @Test
    void getPromotion() {
        // 我们都从八月一号开始吧。
        DateTime begin = DateUtil.parse("2024-07-01");
        long beginTime = TimeUnit.MILLISECONDS.toSeconds(begin.getTime());
        DateTime end = DateUtil.endOfDay(new Date());
        long endTime = TimeUnit.MILLISECONDS.toSeconds(end.getTime());
        // 1806244660804619
        StringBuilder param = new StringBuilder();

        param.append("begin=").append(beginTime).append("&");
        param.append("end=").append(endTime).append("&");

        int offset = 0;
        int limit = 500;
        param.append("offset=").append(offset).append("&");
        param.append("limit=").append(limit).append("&");

        param.append("distributor_id=").append(distributor_id).append("&");
        long ts = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        param.append("ts=").append(ts).append("&");

        String sign = getSign(distributor_id, secretKey, ts);
        param.append("sign=").append(sign);

        String body = HttpUtil.createGet("https://www.changdunovel.com/novelsale/openapi/promotion/list/v1?" + param.toString()).execute().body();

        JSONObject resp = JSONObject.parseObject(body);
        if (resp.getInteger("code") != 200) {
            log.error("获取推广列表失败 result: {}", resp.toString());
        }
        if (resp.getBoolean("has_more")) {
            log.error("推广链接列表超过{}个了！请注意分页获取！！！ result: {}", limit, resp.toString());
        }

        System.out.println(body);

    }

    @Test
    void testReUtil() {
//        int offset = 0;
//        int limit = 500;
//        StringBuilder param = new StringBuilder();
//        long begin = TimeUnit.MILLISECONDS.toSeconds(new Date().getTime());
//        long end = TimeUnit.MILLISECONDS.toSeconds(new Date().getTime());
//        long ts = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
//        String sign = getSign(distributor_id, secretKey, ts);
//
//        param.append("distributor_id=").append(distributor_id).append("&");
//        param.append("ts=").append(ts).append("&");
//        param.append("sign=").append(sign).append("&");
//        param.append("begin=").append(begin).append("&");
//        param.append("end=").append(end).append("&");
//        // 只查询已支付的订单。
//        param.append("paid=").append(true).append("&");
//        param.append("offset=").append(offset).append("&");
//        param.append("limit=").append(limit);

        String param = "distributor_id=1806172584541347&ts=1723085349&sign=58a8fd27ccaa2ab92c4a111f7452a535&begin=1723085349&end=1723085349&paid=true&offset=0&limit=500";

        int offset = 501;
        String regex = "offset=\\d+";
        String replacement = "offset=501";

        List<String> all = ReUtil.findAll(param, regex, 1);

        String replaceAll = ReUtil.replaceAll(param, regex, replacement);
//        param = new StringBuilder(replaceAll);

        System.out.println(param);
    }
}