package com.ruoyi.system.service.roiall.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

class AgtDataTotalRoiSelfServiceImplTest {

    @Test
    void getToday() {
        // 获取当前日期，只精确到天
        Date currentDate = DateUtil.beginOfDay(new Date());
        System.out.println("当前日期 (精确到天): " + currentDate);
    }

    @Test
    void dateTest() {
        Date nowDate = DateUtils.getNowDate();
        Date end = DateUtils.addHours(nowDate, 1);
        System.out.println(end);
    }

    @Test
    void testSync() {
        List<CompletableFuture<String>> futures = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            int finalI = i;
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                System.out.println("异步任务执行" + finalI);
                return "";
            });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        System.out.println("所有任务执行完成");
    }

    @Test
    void testUnderLine() {
        BigDecimal cost = new BigDecimal("100");
        BigDecimal costCash = cost.divide(new BigDecimal(1.05), 2, RoundingMode.HALF_UP);
        System.out.println(costCash);
    }

    @Test
    void testDate() {
        DateTime dateTime = DateUtil.beginOfHour(new Date());

        DateTime dateTime1 = DateUtil.beginOfDay(dateTime);

        DateTime start = DateUtil.offsetHour(dateTime1, -1);

        System.out.println(start);
    }

    @Test
    void testDecimal() throws InterruptedException {
        String content = "{\"message_id\":\"****************\",\"advertiser_ids\":[****************],\"account_relation\":\"{\\\"cc_ids\\\":{\\\"****************\\\":[****************]}}\",\"service_label\":\"report.advertiser.activeprogram\",\"data\":\"[{\\\"advertiser_id\\\":****************,\\\"core_user_ids\\\":[****************]}]\",\"publish_time\":**********,\"timestamp\":*************,\"nonce\":1413386124815478974,\"subscribe_task_id\":****************}";
        JSONObject jsonObject = JSONObject.parseObject(content);


        System.out.println(jsonObject);

        JSONArray advertiserIds = jsonObject.getJSONArray("advertiser_ids");
        for (int i = 0; i < advertiserIds.size(); i++) {
            Long id = advertiserIds.getLong(i);

            System.out.println(id);
        }
    }

}