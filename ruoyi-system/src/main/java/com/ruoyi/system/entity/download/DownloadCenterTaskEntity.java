package com.ruoyi.system.entity.download;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import com.ruoyi.common.annotation.Excel;

/**
 * 下载中心任务对象 tb_download_center_task
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_download_center_task")
public class DownloadCenterTaskEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Integer id;

    /** 任务名 */
    @Excel(name = "任务名")
    private String taskName;

    /** 任务状态，1导出中 2 导出完成 3 导出失败 */
    @Excel(name = "任务状态，1导出中 2 导出完成 3 导出失败")
    private Integer taskStatus;

    /** 文件url */
    @Excel(name = "文件url")
    private String fileUrl;

    /** 记录操作人 id */
    @Excel(name = "记录操作人 id")
    private Long operatorId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date gmtCreate;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date gmtModified;


}
