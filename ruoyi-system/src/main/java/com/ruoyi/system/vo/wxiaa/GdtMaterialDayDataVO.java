package com.ruoyi.system.vo.wxiaa;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广点通IAA素材日数据VO
 *
 * <AUTHOR>
 */
@Data
public class GdtMaterialDayDataVO implements Serializable {
    private static final long serialVersionUID = -2000332485465984834L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 当前日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 25, dateFormat = "yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date curDate;

    @Excel(name = "组织ID")
    @ExcelProperty("组织ID")
    private Long accountId;

    @Excel(name = "组织名称")
    @ExcelProperty("组织名称")
    private String accountName;

    /**
     * 素材类型:1.图片,2.视频
     */
    private Integer materialType;

    /**
     * 素材类型:1.图片,2.视频
     */
    @Excel(name = "素材类型")
    @ExcelProperty("素材类型")
    private String materialTypeStr;

    /**
     * 素材描述
     */
    @Excel(name = "素材名称", width = 50)
    @ExcelProperty("素材名称")
    private String materialDesc;

    @Excel(name = "预览地址")
    @ExcelProperty("预览地址")
    private String previewUrl;

    /**
     * 剪辑
     */
    @Excel(name = "剪辑")
    @ExcelProperty("剪辑")
    private String materialEditor;

    /**
     * 花费
     */
    @Excel(name = "花费", cellType = Excel.ColumnType.NUMERIC)
    @ExcelProperty("花费")
    private String cost;

    /**
     * 点击率:点击次数/曝光次数*100%(前端加%)
     */
    private double ctr;

    /**
     * 点击率:点击次数/曝光次数
     */
    @Excel(name = "点击率", cellType = Excel.ColumnType.NUMERIC)
    @ExcelProperty("点击率")
    private String ctrStr;

    /**
     * 曝光次数
     */
    @Excel(name = "曝光次数", cellType = Excel.ColumnType.NUMERIC)
    @ExcelProperty("曝光次数")
    private Integer viewCount;

    /**
     * 点击次数
     */
    private Integer validClickCount;

    /**
     * 目标转化量
     */
    private Integer conversionsCount;

    /**
     * 广告变现人数
     */
    private Integer appAdPayingUsers;

    /**
     * 广告变现金额
     */
    private String adMonetizationAmount;

    /**
     * 广告变现ROI 广告变现金额/花费*100%
     */
    @Excel(name = "广告变现ROI", cellType = Excel.ColumnType.NUMERIC)
    @ExcelProperty("广告变现ROI")
    private double adMonetizationRoi;
}
