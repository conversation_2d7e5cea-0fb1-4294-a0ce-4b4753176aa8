package com.ruoyi.system.vo.wxiaa;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * @author: keboom
 * @date: 2025/8/1
 */
@Data
public class GdtMaterialDayDataExportVO {

    /**
     * 当前日期
     */
    @ExcelProperty("日期")
    private String curDate;

    @ExcelProperty("组织ID")
    private Long accountId;

    @ExcelProperty("组织名称")
    private String accountName;

    /**
     * 素材类型:1.图片,2.视频
     */
    @ExcelProperty("素材类型")
    private String materialTypeStr;

    /**
     * 素材描述
     */
    @ExcelProperty("素材名称")
    private String materialDesc;

    @ExcelProperty("预览地址")
    private String previewUrl;

    /**
     * 剪辑
     */
    @ExcelProperty("剪辑")
    private String materialEditor;

    /**
     * 花费
     */
    @ExcelProperty("花费")
    private String cost;

    /**
     * 点击率:点击次数/曝光次数
     */
    @ExcelProperty("点击率")
    private String ctrStr;

    /**
     * 曝光次数
     */
    @ExcelProperty("曝光次数")
    private Integer viewCount;

    /**
     * 广告变现ROI 广告变现金额/花费*100%
     */
    @ExcelProperty("广告变现ROI")
    private double adMonetizationRoi;
}
