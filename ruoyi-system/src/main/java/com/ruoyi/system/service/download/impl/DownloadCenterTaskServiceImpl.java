package com.ruoyi.system.service.download.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.download.DownloadCenterTaskMapper;
import com.ruoyi.system.entity.download.DownloadCenterTaskEntity;
import com.ruoyi.system.service.download.DownloadCenterTaskService;

/**
 * 下载中心任务Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class DownloadCenterTaskServiceImpl implements DownloadCenterTaskService {
    @Autowired
    private DownloadCenterTaskMapper downloadCenterTaskMapper;

    /**
     * 查询下载中心任务
     *
     * @param id 下载中心任务主键
     * @return 下载中心任务
     */
    @Override
    public DownloadCenterTaskEntity selectDownloadCenterTaskById(Integer id)
    {
        return downloadCenterTaskMapper.selectDownloadCenterTaskById(id);
    }

    /**
     * 查询下载中心任务列表
     *
     * @param downloadCenterTask 下载中心任务
     * @return 下载中心任务
     */
    @Override
    public List<DownloadCenterTaskEntity> selectDownloadCenterTaskList(DownloadCenterTaskEntity downloadCenterTask)
    {
        return downloadCenterTaskMapper.selectDownloadCenterTaskList(downloadCenterTask);
    }

    /**
     * 新增下载中心任务
     *
     * @param downloadCenterTask 下载中心任务
     * @return 结果
     */
    @Override
    public int insertDownloadCenterTask(DownloadCenterTaskEntity downloadCenterTask)
    {

            return downloadCenterTaskMapper.insertDownloadCenterTask(downloadCenterTask);
    }

    /**
     * 修改下载中心任务
     *
     * @param downloadCenterTask 下载中心任务
     * @return 结果
     */
    @Override
    public int updateDownloadCenterTask(DownloadCenterTaskEntity downloadCenterTask)
    {
        return downloadCenterTaskMapper.updateDownloadCenterTask(downloadCenterTask);
    }

    /**
     * 批量删除下载中心任务
     *
     * @param ids 需要删除的下载中心任务主键
     * @return 结果
     */
    @Override
    public int deleteDownloadCenterTaskByIds(Integer[] ids)
    {
        return downloadCenterTaskMapper.deleteDownloadCenterTaskByIds(ids);
    }

    /**
     * 删除下载中心任务信息
     *
     * @param id 下载中心任务主键
     * @return 结果
     */
    @Override
    public int deleteDownloadCenterTaskById(Integer id)
    {
        return downloadCenterTaskMapper.deleteDownloadCenterTaskById(id);
    }
}
