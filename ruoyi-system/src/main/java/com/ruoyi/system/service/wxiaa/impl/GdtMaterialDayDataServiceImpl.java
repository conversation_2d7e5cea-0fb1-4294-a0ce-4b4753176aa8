package com.ruoyi.system.service.wxiaa.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.OssProperties;
import com.ruoyi.common.utils.OssUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.download.DownloadCenterTaskEntity;
import com.ruoyi.system.entity.wxiaa.GdtMaterialDayDataEntity;
import com.ruoyi.system.entity.wxiaa.GdtMaterialEntity;
import com.ruoyi.system.mapper.wxiaa.GdtMaterialDayDataMapper;
import com.ruoyi.system.param.wxiaa.GdtMaterialDayDataParam;
import com.ruoyi.system.service.download.DownloadCenterTaskService;
import com.ruoyi.system.service.wxiaa.GdtAccountService;
import com.ruoyi.system.service.wxiaa.GdtMaterialDayDataService;
import com.ruoyi.system.service.wxiaa.GdtMaterialService;
import com.ruoyi.system.vo.wxiaa.GdtMaterialDayDataExportVO;
import com.ruoyi.system.vo.wxiaa.GdtMaterialDayDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 广点通IAA素材日数据Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GdtMaterialDayDataServiceImpl implements GdtMaterialDayDataService {

    @Autowired
    private GdtMaterialDayDataMapper gdtMaterialDayDataMapper;
    @Autowired
    private GdtAccountService gdtAccountService;
    @Autowired
    private GdtMaterialService gdtMaterialService;
    @Value("${ruoyi.profile}")
    public String filePath;
    @Autowired
    private RedisCache redisCacheClient;
    @Autowired
    private DownloadCenterTaskService downloadCenterTaskService;


    /**
     * 查询广点通IAA素材日数据
     *
     * @param id 广点通IAA素材日数据主键
     * @return 广点通IAA素材日数据
     */
    @Override
    public GdtMaterialDayDataEntity selectGdtMaterialDayDataById(Long id) {
        return gdtMaterialDayDataMapper.selectGdtMaterialDayDataById(id);
    }

    @Override
    public List<GdtMaterialDayDataEntity> selectList(GdtMaterialDayDataParam param) {
        if (StringUtils.isBlank(param.getOrderColumn())) {
            param.setOrderColumn("cost");
            param.setIsAsc(false);
        }
        param.setOrderColumn(StrUtil.toUnderlineCase(param.getOrderColumn())); // 字段映射，驼峰转下划线
        param.setOrderType(BooleanUtil.isTrue(param.getIsAsc()) ? "asc" : "desc");
        return gdtMaterialDayDataMapper.selectList(param);
    }

    /**
     * 新增广点通IAA素材日数据
     *
     * @param gdtMaterialDayData 广点通IAA素材日数据
     * @return 结果
     */
    @Override
    public int insertGdtMaterialDayData(GdtMaterialDayDataEntity gdtMaterialDayData) {

        return gdtMaterialDayDataMapper.insertGdtMaterialDayData(gdtMaterialDayData);
    }

    /**
     * 修改广点通IAA素材日数据
     *
     * @param gdtMaterialDayData 广点通IAA素材日数据
     * @return 结果
     */
    @Override
    public int updateGdtMaterialDayData(GdtMaterialDayDataEntity gdtMaterialDayData) {
        return gdtMaterialDayDataMapper.updateGdtMaterialDayData(gdtMaterialDayData);
    }

    /**
     * 批量删除广点通IAA素材日数据
     *
     * @param ids 需要删除的广点通IAA素材日数据主键
     * @return 结果
     */
    @Override
    public int deleteGdtMaterialDayDataByIds(Long[] ids) {
        return gdtMaterialDayDataMapper.deleteGdtMaterialDayDataByIds(ids);
    }

    /**
     * 删除广点通IAA素材日数据信息
     *
     * @param id 广点通IAA素材日数据主键
     * @return 结果
     */
    @Override
    public int deleteGdtMaterialDayDataById(Long id) {
        return gdtMaterialDayDataMapper.deleteGdtMaterialDayDataById(id);
    }

    @Override
    public int batchInsertUpdate(List<GdtMaterialDayDataEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return gdtMaterialDayDataMapper.batchInsertUpdate(list);
    }

    @Override
    public int deleteImageDescStartsWithImage(String curDate) {
        if (StringUtils.isBlank(curDate)) {
            return 0;
        }

        int totalDeleted = 0;
        int batchSize = 2000;

        while (true) {
            // 每次都从offset 0开始查询，因为删除数据后offset会发生变化
            List<Long> ids = gdtMaterialDayDataMapper.selectImageDescStartsWithImageIds(curDate, batchSize, 0);
            if (CollectionUtils.isEmpty(ids)) {
                break;
            }

            // 批量删除
            int deleted = gdtMaterialDayDataMapper.batchDeleteByIds(ids);
            totalDeleted += deleted;

            // 如果查询到的数据少于批次大小，说明已经是最后一批
            if (ids.size() < batchSize) {
                break;
            }
        }

        return totalDeleted;
    }

    @Async
    @Override
    public void doExportUploadToOss(GdtMaterialDayDataParam param, String taskKey, Integer downloadTaskId, Long userId) {
        String fileName = downloadTaskId + "-广点通素材数据导出";
        ExcelWriter excelWriter = null;

        try {
            log.info("广点通素材数据导出, downloadTaskId:{}", downloadTaskId);
            long startTime = System.currentTimeMillis();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            excelWriter = EasyExcel.write(byteArrayOutputStream).build();
            ExcelWriter finalExcelWriter = excelWriter;


            List<GdtMaterialDayDataEntity> list = selectList(param);
            Map<Long, String> accountNameMap = gdtAccountService.selectAccountNameMap();
            Map<String, GdtMaterialEntity> materialMap = param.getDimension().contains("material_id") ?
                    gdtMaterialService.selectMap(list.stream().map(GdtMaterialDayDataEntity::getMaterialId).collect(Collectors.toList())) :
                    Collections.emptyMap();
            List<GdtMaterialDayDataExportVO> dataVOS = list.stream().map(s ->
                    getGdtDayDataVO(s, accountNameMap, materialMap)).collect(Collectors.toList());


            WriteSheet writeSheet = EasyExcel.writerSheet("号码" + 1).build();

            // 将 SmsSendRecordEntity 转化为 ExportFailedSMSBO

            WriteTable table = EasyExcel.writerTable().head(GdtMaterialDayDataExportVO.class).build();
            finalExcelWriter.write(dataVOS, writeSheet, table);

            log.info("广点通素材数据 excel 导出完成,耗时:{}", (System.currentTimeMillis() - startTime) / 1000);

            excelWriter.finish();

            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

            if (!FileUtil.exist(filePath)) {
                FileUtil.mkdir(filePath);
            }

            File targetFile = new File(filePath + File.separator + fileName + ".xlsx");
            byte[] bytes = new byte[inputStream.available()];
            inputStream.read(bytes);
            FileUtil.writeBytes(bytes, targetFile);
            String upload = OssUtils.upload(targetFile, null, userId, "天启");
            String ossPath = OssProperties.getUrl() + upload;
            redisCacheClient.setCacheObject(taskKey, ossPath, 1, TimeUnit.DAYS);//保存一天时间
            log.info("号码导出,downloadTaskId:{},总耗时:{}", downloadTaskId, (System.currentTimeMillis() - startTime) / 1000);


            DownloadCenterTaskEntity downloadCenterTaskEntity = new DownloadCenterTaskEntity();
            downloadCenterTaskEntity.setTaskStatus(2);
            downloadCenterTaskEntity.setId(downloadTaskId);
            downloadCenterTaskEntity.setFileUrl(ossPath);
            downloadCenterTaskService.updateDownloadCenterTask(downloadCenterTaskEntity);

        } catch (Exception e) {
            log.error("号码导出异常：downloadTaskId:{},e:", downloadTaskId, e);
            redisCacheClient.deleteObject(taskKey);
            DownloadCenterTaskEntity downloadCenterTaskEntity = new DownloadCenterTaskEntity();
            downloadCenterTaskEntity.setTaskStatus(3);
            downloadCenterTaskEntity.setId(downloadTaskId);
            downloadCenterTaskService.updateDownloadCenterTask(downloadCenterTaskEntity);
            throw new BizRuntimeException(e.getMessage());
        }
    }

    private GdtMaterialDayDataExportVO getGdtDayDataVO(GdtMaterialDayDataEntity entity, Map<Long, String> accountNameMap,
                                                Map<String, GdtMaterialEntity> materialMap) {
        GdtMaterialDayDataExportVO vo = BeanUtil.copyProperties(entity, GdtMaterialDayDataExportVO.class);
        if (null != entity.getAccountId()) {
            vo.setAccountName(accountNameMap.get(entity.getAccountId()));
        }
        vo.setCurDate(DateUtil.formatDate(entity.getCurDate()));
        vo.setCost(NumberUtils.fenToYuan(entity.getCost()));
        vo.setMaterialTypeStr(Objects.equals(entity.getMaterialType(), 1) ? "图片" : "视频");
        vo.setCtrStr(NumberUtils.calculateRate(entity.getValidClickCount(), entity.getViewCount()));
        vo.setAdMonetizationRoi(NumberUtils.calculateRateToDouble(entity.getAdMonetizationAmount(), entity.getCost(), 4));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entity.getMaterialId()) && materialMap.containsKey(entity.getMaterialId())) {
            GdtMaterialEntity material = materialMap.get(entity.getMaterialId());
            vo.setMaterialDesc(material.getMaterialDesc());
            vo.setPreviewUrl(material.getPreviewUrl());
        }
        return vo;
    }


}
