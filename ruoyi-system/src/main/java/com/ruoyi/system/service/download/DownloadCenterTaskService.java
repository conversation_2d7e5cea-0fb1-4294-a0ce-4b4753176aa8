package com.ruoyi.system.service.download;

import java.util.List;
import com.ruoyi.system.entity.download.DownloadCenterTaskEntity;

/**
 * 下载中心任务Service接口
 *
 * <AUTHOR>
 */
public interface DownloadCenterTaskService {
    /**
     * 查询下载中心任务
     *
     * @param id 下载中心任务主键
     * @return 下载中心任务
     */
    public DownloadCenterTaskEntity selectDownloadCenterTaskById(Integer id);

    /**
     * 查询下载中心任务列表
     *
     * @param downloadCenterTask 下载中心任务
     * @return 下载中心任务集合
     */
    public List<DownloadCenterTaskEntity> selectDownloadCenterTaskList(DownloadCenterTaskEntity downloadCenterTask);

    /**
     * 新增下载中心任务
     *
     * @param downloadCenterTask 下载中心任务
     * @return 结果
     */
    public int insertDownloadCenterTask(DownloadCenterTaskEntity downloadCenterTask);

    /**
     * 修改下载中心任务
     *
     * @param downloadCenterTask 下载中心任务
     * @return 结果
     */
    public int updateDownloadCenterTask(DownloadCenterTaskEntity downloadCenterTask);

    /**
     * 批量删除下载中心任务
     *
     * @param ids 需要删除的下载中心任务主键集合
     * @return 结果
     */
    public int deleteDownloadCenterTaskByIds(Integer[] ids);

    /**
     * 删除下载中心任务信息
     *
     * @param id 下载中心任务主键
     * @return 结果
     */
    public int deleteDownloadCenterTaskById(Integer id);
}
