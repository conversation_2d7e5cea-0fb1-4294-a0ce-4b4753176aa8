package com.ruoyi.system.service.wxiaa;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.wxiaa.GdtMaterialDayDataEntity;
import com.ruoyi.system.entity.wxiaa.GdtMaterialEntity;
import com.ruoyi.system.param.wxiaa.GdtMaterialDayDataParam;
import com.ruoyi.system.vo.wxiaa.GdtMaterialDayDataVO;

/**
 * 广点通IAA素材日数据Service接口
 *
 * <AUTHOR>
 */
public interface GdtMaterialDayDataService {

    /**
     * 查询广点通IAA素材日数据
     *
     * @param id 广点通IAA素材日数据主键
     * @return 广点通IAA素材日数据
     */
    public GdtMaterialDayDataEntity selectGdtMaterialDayDataById(Long id);

    /**
     * 查询广点通IAA素材日数据列表
     *
     * @param param 广点通IAA素材日数据
     * @return 广点通IAA素材日数据集合
     */
    List<GdtMaterialDayDataEntity> selectList(GdtMaterialDayDataParam param);

    /**
     * 新增广点通IAA素材日数据
     *
     * @param gdtMaterialDayData 广点通IAA素材日数据
     * @return 结果
     */
    public int insertGdtMaterialDayData(GdtMaterialDayDataEntity gdtMaterialDayData);

    /**
     * 修改广点通IAA素材日数据
     *
     * @param gdtMaterialDayData 广点通IAA素材日数据
     * @return 结果
     */
    public int updateGdtMaterialDayData(GdtMaterialDayDataEntity gdtMaterialDayData);

    /**
     * 批量删除广点通IAA素材日数据
     *
     * @param ids 需要删除的广点通IAA素材日数据主键集合
     * @return 结果
     */
    public int deleteGdtMaterialDayDataByIds(Long[] ids);

    /**
     * 删除广点通IAA素材日数据信息
     *
     * @param id 广点通IAA素材日数据主键
     * @return 结果
     */
    public int deleteGdtMaterialDayDataById(Long id);

    /**
     * 批量新增广点通iaa素材日数据
     *
     * @param list 数据列表
     * @return 影响行数
     */
    int batchInsertUpdate(List<GdtMaterialDayDataEntity> list);

    /**
     * 删除指定日期以"图片"开头的图片描述数据
     *
     * @param curDate 日期字符串，格式为 yyyy-MM-dd
     * @return 删除的记录数
     */
    int deleteImageDescStartsWithImage(String curDate);

    /**
     * doExportUploadToOss
     * @param param
     * @param taskKey
     * @param downloadTaskId
     * @param userId
     */
    void doExportUploadToOss(GdtMaterialDayDataParam param, String taskKey, Integer downloadTaskId, Long userId);


}
