package com.ruoyi.system.service.wxiaa.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.analysis.WxMaRetainInfo;
import cn.binarywang.wx.miniapp.bean.analysis.WxMaVisitTrend;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.DingRobotUtil;
import com.ruoyi.system.bo.wx.GdtSumDayDataBO;
import com.ruoyi.system.bo.wx.WxPublisherAdposGeneralBo;
import com.ruoyi.system.entity.account.GdtAccountEntity;
import com.ruoyi.system.entity.app.GdtAppEntity;
import com.ruoyi.system.entity.shortplay.app.ShortplayAppEntity;
import com.ruoyi.system.entity.user.AgtAccountUserEntity;
import com.ruoyi.system.entity.user.AgtAccountUserPost;
import com.ruoyi.system.entity.wxiaa.*;
import com.ruoyi.system.service.user.AgtAccountUserService;
import com.ruoyi.system.service.wxiaa.*;
import com.ruoyi.system.utils.ImageUtils;
import com.tencent.ads.ApiContextConfig;
import com.tencent.ads.ApiException;
import com.tencent.ads.exception.TencentAdsResponseException;
import com.tencent.ads.model.v3.*;
import com.tencent.ads.v3.TencentAds;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.gdt.GdtMaterialType.*;

/**
 * 广点通service
 * sdk 接口方法对应查询文档：https://developers.e.qq.com/v3.0/pages/docs/readme/java_sdk
 *
 * <AUTHOR>
 * @date 2024/3/29 13:40
 */
@Slf4j
@Service
public class GuangDianTongServiceImpl implements GuangDianTongService {

    @Value("${ruoyi.profile}")
    public String filePath;
    @Value("${server.domain}")
    public String serverDomain;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private GdtAccountService gdtAccountService;
    @Autowired
    private GdtAppService gdtAppService;
    @Autowired
    private GdtAdvertisementAccountService gdtAdvertisementAccountService;
    @Autowired
    private GdtDayDataService gdtDayDataService;
    @Autowired
    private WxMaAdService wxMaAdService;
    @Autowired
    private GdtMaterialDayDataService gdtMaterialDayDataService;
    @Autowired
    private GdtMaterialService gdtMaterialService;
    @Autowired
    private AgtAccountUserService agtAccountUserService;
    @Autowired
    private GdtAdgroupService gdtAdgroupService;
    @Autowired
    private GdtCreativeService gdtCreativeService;
    @Autowired
    private GdtMiniappService gdtMiniappService;

    Pattern chinesePattern = Pattern.compile("[\u4e00-\u9fa5]");

    @Override
    public void refreshAccessToken(Long appId, Long accountId) {
        if (!SpringEnvironmentUtils.isProd()) {
            //测试不刷新token，以免对线上造成影响
            return;
        }
        String secret = gdtAppService.selectGdtAppSecretByAppId(appId);
        String grantType = "refresh_token";
        try {
            GdtAccountEntity accountEntity = gdtAccountService.selectGdtAccountById(accountId);
            if (Objects.isNull(accountEntity)) {
                log.error("未找到广点通账号信息,appId:{},accountId:{}", appId, accountId);
                return;
            }
            TencentAds tencentAds = getApiClient(appId, accountId, false);
            OauthTokenResponseData responseData = tencentAds.oauth().oauthToken(appId, secret, grantType, "", accountEntity.getRefreshToken(), "", Collections.emptyList(), new String[0]);
            if (Objects.nonNull(responseData) && Objects.nonNull(responseData.getAccessToken())) {
                log.info("刷新广点通access token成功,accessTokenResponse:{}", responseData);
                updateAccountInfo(appId, accountId, responseData.getAccessToken(), responseData.getRefreshToken());
            } else {
                log.error("刷新广点通access token失败,accessTokenResponse:{}", responseData);
            }
        } catch (Exception e) {
            log.error("刷新广点通access token异常,e:", e);
        }
    }

    @Override
    public String getAccessToken(Long appId, Long accountId) {
        String accessToken = redisCache.getCacheObject(RedisKeyFactory.K046.join(appId, accountId));
        if (StringUtils.isEmpty(accessToken)) {
//            refreshAccessToken(appId, accountId); //广点通应用可以设置缓存永不过期，所以就不用刷新缓存
            GdtAccountEntity accountEntity = gdtAccountService.selectGdtAccountByAccountId(accountId);
            if (Objects.nonNull(accountEntity)) {
                redisCache.setCacheObject(RedisKeyFactory.K046.join(appId, accountId), accountEntity.getAccessToken(), 30, TimeUnit.DAYS);
                accessToken = accountEntity.getAccessToken();
            }
        }
        return accessToken;
    }

    @Override
    public String getAccessToken(Long accountId) {
        String accessToken = redisCache.getCacheObject(RedisKeyFactory.K046.join( accountId));
        if (StringUtils.isEmpty(accessToken)) {
//            refreshAccessToken(appId, accountId); //广点通应用可以设置缓存永不过期，所以就不用刷新缓存
            GdtAccountEntity accountEntity = gdtAccountService.selectGdtAccountByAccountId(accountId);
            if (Objects.nonNull(accountEntity)) {
                redisCache.setCacheObject(RedisKeyFactory.K046.join(accountId), accountEntity.getAccessToken(), 30, TimeUnit.DAYS);
                accessToken = accountEntity.getAccessToken();
            }
        }
        return accessToken;
    }

    @Override
    public boolean setAccessToken(String authCode, Long appId) {
        if (!SpringEnvironmentUtils.isProd()) {
            //测试不刷新token，以免对线上造成影响
            return false;
        }
        String grantType = "authorization_code";
        try {
            GdtAppEntity gdtAppEntity = gdtAppService.selectByAppId(appId);
            if (Objects.isNull(gdtAppEntity)) {
                log.error("应用不存在,appId:{}", appId);
                return false;
            }
            TencentAds tencentAds = getApiClient(appId, 0L, false);

            OauthTokenResponseData responseData = tencentAds.oauth().oauthToken(appId, gdtAppEntity.getAppSecret(), grantType, authCode, null, serverDomain+"api/guangdiantong/callback/authback", Collections.emptyList(), new String[0]);

            if (Objects.nonNull(responseData) && Objects.nonNull(responseData.getAccessToken())) {
                Long accountId = responseData.getAuthorizerInfo().getAccountId();
                log.info("设置广点通access token成功,accessTokenResponse:{}", responseData);
                //不能更新，不同广告主授权时，会调用该方法，更新采用手动更新方法
                updateAccountInfo(appId, accountId, responseData.getAccessToken(), responseData.getRefreshToken());
                return true;
            } else {
                log.error("设置广点通access token失败,accessTokenResponse:{}", responseData);
            }
        } catch (Exception e) {
            log.error("设置广点通access token异常,e:", e);
        }
        return false;

    }

    /**
     * 根据广点通账号信息
     *
     * @param appId
     * @param accessToken
     * @param refreshToken
     */
    @Override
    public void updateAccountInfo(Long appId, Long accountId, String accessToken, String refreshToken) {
        boolean isUpdate = false;
        GdtAccountEntity entity = gdtAccountService.selectGdtAccountByAccountId(accountId);
        if (Objects.isNull(entity)) {
            entity = new GdtAccountEntity();
        } else {
            isUpdate = true;
        }
        entity.setAppId(appId);
        entity.setAccountId(accountId);
        if (StringUtils.isNotBlank(accessToken)) {
            entity.setAccessToken(accessToken);
        }
        if (StringUtils.isNotBlank(refreshToken)) {
            entity.setRefreshToken(refreshToken);
        }
        if (isUpdate) {
            gdtAccountService.updateGdtAccountByAppId(entity);
            redisCache.deleteObject(RedisKeyFactory.K046.join(appId, accountId));
        } else {
            gdtAccountService.insertGdtAccount(entity);
        }
    }


    @Override
    public TencentAds getApiClient(Long appId, Long accountId, boolean needToken) {
        TencentAds tencentAds = TencentAds.getInstance();
        tencentAds.init(new ApiContextConfig());
        tencentAds.setConnectTimeout(600000);
        if (!SpringEnvironmentUtils.isProd()) {
            tencentAds.setDebug(true);
        }
        if (needToken) {
            tencentAds.setAccessToken(getAccessToken(appId, accountId));
        }
        return tencentAds;
    }

    @Override
    public void syncAdvertiserInfo(Long accountId, Long appId) {
        TencentAds apiClient = getApiClient(appId, accountId, true);
        Long cursor = null;
        Boolean hasMore = true;
        try {
            while (BooleanUtils.isTrue(hasMore)) {
                //采用游标方式获取分页数据
                OrganizationAccountRelationGetResponseData responseData = apiClient.organizationAccountRelation().organizationAccountRelationGet("PAGINATION_MODE_CURSOR", null, "SUB_ADVERTISER", cursor, 1L, 100L, null, null);
                hasMore = responseData.getCursorPageInfo().isHasMore();
                cursor = responseData.getCursorPageInfo().getCursor();
                //adx代表腾讯广告账户，true：是，false：否
                List<GdtAdvertisementAccountEntity> gdtAdvertisementAccountEntities = responseData.getList().stream().filter(item -> item.isIsAdx()).map(item -> {
                    GdtAdvertisementAccountEntity gdtAdvertisementAccountEntity = new GdtAdvertisementAccountEntity();
                    gdtAdvertisementAccountEntity.setAccountId(accountId);
                    gdtAdvertisementAccountEntity.setAdvertisementId(item.getAccountId());
                    gdtAdvertisementAccountEntity.setAdvertisementName(item.getCorporationName());
                    return gdtAdvertisementAccountEntity;
                }).collect(Collectors.toList());
                gdtAdvertisementAccountService.insertGdtAdvertisementAccountList(gdtAdvertisementAccountEntities);
            }

        } catch (ApiException e) {
            log.error("查询广点通广告账户异常,accountId:{},appId:{},e:", accountId, appId, e);
        }
    }


    public Set<Long> getTotalDeliveryAGroupId(GdtAccountEntity account, Long advertisementId) {
        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);
        List<FilteringStruct> filtering = new ArrayList<>();
        List<String> adFields = Lists.newArrayList("system_status", "adgroup_id");
        Set<Long> set = new HashSet<>();
        while (hasNext) {
            try {
                log.info("getTotalDeliveryAGroupId, advertisementId={}", advertisementId);
                AdgroupsGetResponseData responseData = apiClient.adgroups().adgroupsGet(advertisementId, filtering, page, 100L, false, adFields);
                log.info("getTotalDeliveryAGroupId response data:{}", JSON.toJSONString(responseData));
                if (responseData.getPageInfo().getTotalPage() > page) {
                    page++;
                    hasNext = true;
                } else {
                    hasNext = false;
                }
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    continue;
                }
                set.addAll(responseData.getList().stream()
                                .filter(t -> Objects.nonNull(t) && Objects.nonNull(t.getSystemStatus()) &&
                                        (Objects.equals(t.getSystemStatus().getValue(), "ADGROUP_STATUS_ACTIVE") || Objects.equals(t.getSystemStatus().getValue(), "ADGROUP_STATUS_PARTIAL_ACTIVE")))
                        .map(AdgroupsGetListStruct::getAdgroupId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet()));
            } catch (Exception e) {
                log.error("获取广告状态失败",e);
                return set;
            }

        }
        return set;

    }

    @Override
    public void syncAdvertDayData(GdtAccountEntity account, GdtAdvertisementAccountEntity advertisementAccount, Date day, Set<String> appNames, List<String> userNames, int retryTime) {
        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);
        String yesterday = DateUtil.formatDate(day);
        ReportDateRange dateRange = new ReportDateRange();
        dateRange.setStartDate(yesterday);
        dateRange.setEndDate(yesterday);
        List<String> groupBy = Lists.newArrayList("adgroup_id");
        List<String> fields = Lists.newArrayList("adgroup_id", "view_count", "adgroup_name", "cost", "ad_monetization_amount", "view_count", "valid_click_count", "conversions_count", "app_ad_paying_users", "income_val_1");
        try {
            Map<String, GdtMiniappEntity> appMap = gdtMiniappService.selectMapByAppName();
            while (hasNext) {
                DailyReportsGetResponseData responseData = apiClient.dailyReports().dailyReportsGet(advertisementAccount.getAdvertisementId(), "REPORT_LEVEL_ADGROUP", dateRange, groupBy, fields, Collections.emptyList(), Collections.emptyList(), "REPORTING_TIME", page, 1000L, new String[0]);
                log.info("同步广点通广告日数据,advertiserId:{},data:{}", advertisementAccount.getAdvertisementId(), responseData);

                if (responseData.getPageInfo().getTotalPage() > page) {
                    page++;
                    hasNext = true;
                } else {
                    hasNext = false;
                }
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    continue;
                }
                List<GdtDayDataEntity> gdtAdGroupReportEntities = responseData.getList().stream().map(item -> {
                    //没有消耗数据的,不处理后续逻辑
                    if (Objects.equals(item.getCost(), 0L)) {
                        return null;
                    }
                    GdtDayDataEntity entity = BeanUtil.copyProperties(item, GdtDayDataEntity.class);
                    entity.setAdvertisementName(advertisementAccount.getAdvertisementName());
                    //广告命名规范，投手-剧名-小程序名
                    String[] adgroupNames = item.getAdgroupName().split("-");
                    String operator = userNames.contains(adgroupNames[0]) ? adgroupNames[0] : "未知";
                    String videoName = adgroupNames.length > 1 ? adgroupNames[1] : "";
                    String appName = adgroupNames.length > 2 ? parseAppName(appNames, adgroupNames[2]) : "未知";
                    if(appName.equals("未知")){
                        //兼容
                        for (String app : appNames){
                            if(item.getAdgroupName().contains(app)){
                                appName = app;
                                break;
                            }
                        }
                    }


                    BigDecimal cashRate = BigDecimal.valueOf(0.6); // 小程序现金分成,默认0.6
                    BigDecimal adMoneyRatio = BigDecimal.valueOf(0.1); // 小程序广告金比例,默认0.1
                    if (appMap.containsKey(appName)) {
                        GdtMiniappEntity miniapp = appMap.get(appName);
                        entity.setMiniappId(miniapp.getAppId());
                        // 小程序现金分成和广告金比例
                        cashRate = NumberUtils.defaultBigDecimal(miniapp.getCashRate(), cashRate);
                        adMoneyRatio = NumberUtils.defaultBigDecimal(miniapp.getAdMoneyRatio(), adMoneyRatio);
                    } else {
                        entity.setMiniappId(StringUtils.defaultString(entity.getMiniappId())); // NPE处理
                    }

                    // 实际广告变现金额=账面广告变现金额/0.7*小程序现金分成
                    entity.setRealAdMonetizationAmount(NumberUtils.calculateRateReturnInteger(entity.getAdMonetizationAmount() * cashRate.doubleValue(), 0.7));
                    // 预估广告金=实际广告变现金额/小程序现金分成*小程序广告金比例
                    entity.setAdMoney(NumberUtils.calculateRateReturnInteger(entity.getRealAdMonetizationAmount() * adMoneyRatio.doubleValue(), cashRate.doubleValue()));

                    entity.setCurDate(day);
                    entity.setOperator(operator);
                    entity.setAdvertisementId(advertisementAccount.getAdvertisementId());
                    entity.setVideoName(videoName);
                    entity.setMiniappName(appName);
                    entity.setCashCost(NumberUtils.calculateRate(item.getCost(), (1 + account.getRebate() / 100)));

                    // 非生产环境不提醒
                    if (!SpringEnvironmentUtils.isProd()) {
                        return entity;
                    }

                    if (Objects.equals("未知", operator)) {
                        DingRobotUtil.sendText(DingWebhookConfig.getAdsReqAlert(), StringUtils.format("广点通广告计划命名不规范：\n" + "广告账户：{}\n" + "广告账户id:{}\n" + "广告名称:{}\n" + "广告id:{}\n" + "原因:未找到对应运营", advertisementAccount.getAdvertisementName(), advertisementAccount.getAdvertisementId(), item.getAdgroupName(), item.getAdgroupId()));
                    }
                    if (Objects.equals("未知", appName)) {
                        log.error("广点通未查询到小程序, adGroupName={}, appNames={}", item.getAdgroupName(), JSON.toJSONString(appNames));
                        DingRobotUtil.sendText(DingWebhookConfig.getAdsReqAlert(), StringUtils.format("广点通广告计划命名不规范：\n" + "广告账户：{}\n" + "广告账户id:{}\n" + "广告名称:{}\n" + "广告id:{}\n" + "原因:未找到对应小程序", advertisementAccount.getAdvertisementName(), advertisementAccount.getAdvertisementId(), item.getAdgroupName(), item.getAdgroupId()));
                    }
                    return entity;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                gdtDayDataService.batchInsertUpdate(gdtAdGroupReportEntities);
                ThreadUtil.sleep(5000);
            }

        } catch (Exception e) {
            if (retryTime > 0) {
                ThreadUtil.sleep(10000);
                syncAdvertDayData(account, advertisementAccount, day, appNames, userNames, retryTime - 1);
                return;
            }
            log.error("同步广点通广告日数据异常,advertiserId:{},e:", advertisementAccount.getAdvertisementId(), e);
        }
    }

    @Override
    public void syncGdtAdvertRoiNotice(GdtAccountEntity account, GdtAdvertisementAccountEntity advertisementAccount, Date day, Set<String> appNames, List<String> userNames, int retryTime, Map<String, String> realNameMap) {
        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);
        String yesterday = DateUtil.formatDate(day);
        ReportDateRange dateRange = new ReportDateRange();
        dateRange.setStartDate(yesterday);
        dateRange.setEndDate(yesterday);
        List<String> groupBy = Lists.newArrayList("adgroup_id");
        List<String> fields = Lists.newArrayList("adgroup_id", "view_count", "adgroup_name", "cost", "ad_monetization_amount", "view_count", "valid_click_count", "conversions_count", "app_ad_paying_users", "income_val_1");
        try {
            Map<String, GdtMiniappEntity> appMap = gdtMiniappService.selectMapByAppName();
            // 获取所有投放中、部分投放中的广告
            Set<Long> deliveryAGroupIdSet = getTotalDeliveryAGroupId(account, advertisementAccount.getAdvertisementId());
            log.info("deliveryAGroupIdSet={}", JSONObject.toJSONString(deliveryAGroupIdSet));
            while (hasNext) {
                DailyReportsGetResponseData responseData = apiClient.dailyReports().dailyReportsGet(advertisementAccount.getAdvertisementId(), "REPORT_LEVEL_ADGROUP", dateRange, groupBy, fields, Collections.emptyList(), Collections.emptyList(), "REPORTING_TIME", page, 1000L, new String[0]);
                log.info("同步广点通广告日数据,advertiserId:{},data:{}", advertisementAccount.getAdvertisementId(), responseData);

                if (responseData.getPageInfo().getTotalPage() > page) {
                    page++;
                    hasNext = true;
                } else {
                    hasNext = false;
                }
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    continue;
                }
                List<GdtDayDataEntity> gdtAdGroupReportEntities = responseData.getList().stream().map(item -> {
                    //没有消耗数据的,不处理后续逻辑
                    if (Objects.equals(item.getCost(), 0L)) {
                        return null;
                    }
                    GdtDayDataEntity entity = BeanUtil.copyProperties(item, GdtDayDataEntity.class);
                    entity.setAdvertisementName(advertisementAccount.getAdvertisementName());
                    //广告命名规范，投手-剧名-小程序名
                    String[] adgroupNames = item.getAdgroupName().split("-");
                    String operator = userNames.contains(adgroupNames[0]) ? adgroupNames[0] : "未知";
                    String videoName = adgroupNames.length > 1 ? adgroupNames[1] : "";
                    String appName = adgroupNames.length > 2 ? parseAppName(appNames, adgroupNames[2]) : "未知";

                    BigDecimal cashRate = BigDecimal.valueOf(0.6); // 小程序现金分成,默认0.6
                    BigDecimal adMoneyRatio = BigDecimal.valueOf(0.1); // 小程序广告金比例,默认0.1
                    if (appMap.containsKey(appName)) {
                        GdtMiniappEntity miniapp = appMap.get(appName);
                        entity.setMiniappId(miniapp.getAppId());
                        // 小程序现金分成和广告金比例
                        cashRate = NumberUtils.defaultBigDecimal(miniapp.getCashRate(), cashRate);
                        adMoneyRatio = NumberUtils.defaultBigDecimal(miniapp.getAdMoneyRatio(), adMoneyRatio);
                    } else {
                        entity.setMiniappId(StringUtils.defaultString(entity.getMiniappId())); // NPE处理
                    }

                    // 实际广告变现金额=账面广告变现金额/0.7*小程序现金分成
                    entity.setRealAdMonetizationAmount(NumberUtils.calculateRateReturnInteger(entity.getAdMonetizationAmount() * cashRate.doubleValue(), 0.7));
                    // 预估广告金=实际广告变现金额/小程序现金分成*小程序广告金比例
                    entity.setAdMoney(NumberUtils.calculateRateReturnInteger(entity.getRealAdMonetizationAmount() * adMoneyRatio.doubleValue(), cashRate.doubleValue()));

                    entity.setCurDate(day);
                    entity.setOperator(operator);
                    entity.setAdvertisementId(advertisementAccount.getAdvertisementId());
                    entity.setVideoName(videoName);
                    entity.setMiniappName(appName);
                    entity.setCashCost(NumberUtils.calculateRate(item.getCost(), (1 + account.getRebate() / 100)));

                    // 非生产环境不提醒
                    if (!SpringEnvironmentUtils.isProd()) {
                        return entity;
                    }


                    // 300>cost>=100 and roi<0.6 and 广告状态为投放中或部分投放中
                    // cost>=300 and roi < 0.7 and 广告状态为投放中或部分投放中
                    if ( Objects.nonNull(item.getCost()) && Objects.nonNull(item.getAdMonetizationAmount()) && deliveryAGroupIdSet.contains(item.getAdgroupId()) &&
                            (( 10000 <= item.getCost() && item.getCost() < 30000 && BigDecimal.valueOf(item.getAdMonetizationAmount()).divide(BigDecimal.valueOf(item.getCost()), 4, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(0.6)) < 0)
                                    || (item.getCost() >= 30000 && BigDecimal.valueOf(item.getAdMonetizationAmount()).divide(BigDecimal.valueOf(item.getCost()), 4, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(0.7)) < 0)) ){
                        DingRobotUtil.sendText(DingWebhookConfig.getRoiAlert(), StringUtils.format("账户id：{}\n" + "广告计划id：{}\n" + "消耗:{}\n" + "roi:{}\n" + "投手:{}\n" + "组织:{}",
                                advertisementAccount.getAdvertisementId(), item.getAdgroupId(), BigDecimal.valueOf(item.getCost()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString(),
                                BigDecimal.valueOf(item.getAdMonetizationAmount()).divide(BigDecimal.valueOf(item.getCost()), 4, RoundingMode.HALF_UP),
                                realNameMap.get(entity.getOperator()), account.getAccountName()));
                    }

                    return entity;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                gdtDayDataService.batchInsertUpdate(gdtAdGroupReportEntities);
                ThreadUtil.sleep(5000);
            }

        } catch (Exception e) {
            if (retryTime > 0) {
                ThreadUtil.sleep(10000);
                syncGdtAdvertRoiNotice(account, advertisementAccount, day, appNames, userNames, retryTime - 1, realNameMap);
                return;
            }
            log.error("ROI提醒失败,advertiserId:{},e:", advertisementAccount.getAdvertisementId(), e);
        }
    }

    @Override
    public void syncMaterialVideoDayData(GdtAccountEntity account, Long advertisementId, Date day) {
        // 同步视频名称
        if (day.after(DateUtil.parseDate("2024-10-27"))) {
            syncMaterial(account, advertisementId, VIDEO.getType());
        }

        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);
        String yesterday = DateUtil.formatDate(day);
        ReportDateRange dateRange = new ReportDateRange();
        dateRange.setStartDate(yesterday);
        dateRange.setEndDate(yesterday);
        List<String> groupBy = Lists.newArrayList("date", "video_id");
        List<String> fields = Lists.newArrayList("date", "account_id", "video_id", "view_count", "valid_click_count", "conversions_count", "app_ad_paying_users", "ad_monetization_amount", "cost");
        try {
            Map<String, String> editorMap = selectTotalMaterialEditor();
            Set<String> editorSet = new HashSet<>(editorMap.values());

            while (hasNext) {
                DailyReportsGetResponseData responseData = apiClient.dailyReports().dailyReportsGet(advertisementId, "REPORT_LEVEL_MATERIAL_VIDEO", dateRange, groupBy, fields, Collections.emptyList(), Collections.emptyList(), "REPORTING_TIME", page, 1000L, new String[0]);
                log.info("同步广点通视频素材日数据,advertiserId:{},data:{}", advertisementId, responseData);

                if (responseData.getPageInfo().getTotalPage() > page) {
                    page++;
                    hasNext = true;
                } else {
                    hasNext = false;
                }
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    continue;
                }
                Map<String, String> materialDescMap = gdtMaterialService.selectDescMap(responseData.getList().stream().map(DailyReportApiListStruct::getVideoId).collect(Collectors.toList()));

                List<GdtMaterialDayDataEntity> gdtAdGroupReportEntities = responseData.getList().stream().map(item -> {
                    GdtMaterialDayDataEntity entity = BeanUtil.copyProperties(item, GdtMaterialDayDataEntity.class);
                    // 查询视频描述
                    String videoDesc = materialDescMap.getOrDefault(item.getVideoId(), "");
                    if (StringUtils.isBlank(videoDesc)) {
                        videoDesc = syncMaterialById(account, NumberUtils.defaultLong(item.getAccountId(), advertisementId), VIDEO.getType(), item.getVideoId());
                    }
                    String materialEditor = parseFromMaterialDesc(videoDesc);
                    if (editorMap.containsKey(materialEditor)) {
                        materialEditor = editorMap.get(materialEditor);
                    } else if (!editorSet.contains(materialEditor)) {
                        materialEditor = "";
                    }

                    entity.setCurDate(day);
                    entity.setAdvertisementId(NumberUtils.defaultLong(item.getAccountId(), advertisementId));
                    entity.setMaterialId(item.getVideoId());
                    entity.setMaterialType(VIDEO.getType());
                    entity.setMaterialDesc(videoDesc);
                    entity.setMaterialEditor(StringUtils.defaultIfBlank(materialEditor, "未知"));
                    return entity;
                }).filter(entity -> {
                    String imageDesc = entity.getMaterialDesc();
                    return chinesePattern.matcher(imageDesc).find() || !imageDesc.startsWith("视频封面");
                }).collect(Collectors.toList());
                gdtMaterialDayDataService.batchInsertUpdate(gdtAdGroupReportEntities);
            }
        } catch (Exception e) {
            log.error("同步广点通视频素材日数据异常,advertiserId:{},e:", advertisementId, e);
        }
    }

    @Override
    public void syncMaterialImageDayData(GdtAccountEntity account, Long advertisementId, Date day) {
        // 同步图片名称
        if (day.after(DateUtil.parseDate("2024-10-27"))) {
            syncMaterial(account, advertisementId, IMAGE.getType());
        }

        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);
        String yesterday = DateUtil.formatDate(day);
        ReportDateRange dateRange = new ReportDateRange();
        dateRange.setStartDate(yesterday);
        dateRange.setEndDate(yesterday);
        List<String> groupBy = Lists.newArrayList("date", "image_id");
        List<String> fields = Lists.newArrayList("date", "account_id", "image_id", "date", "view_count", "valid_click_count", "conversions_count", "app_ad_paying_users", "ad_monetization_amount", "cost");
        try {
            Map<String, String> editorMap = selectTotalMaterialEditor();
            Set<String> editorSet = new HashSet<>(editorMap.values());

            while (hasNext) {
                DailyReportsGetResponseData responseData = apiClient.dailyReports().dailyReportsGet(advertisementId, "REPORT_LEVEL_MATERIAL_IMAGE", dateRange, groupBy, fields, Collections.emptyList(), Collections.emptyList(), "REPORTING_TIME", page, 1000L, new String[0]);
                log.info("同步广点通图片素材日数据,advertiserId:{},data:{}", advertisementId, responseData);

                if (responseData.getPageInfo().getTotalPage() > page) {
                    page++;
                    hasNext = true;
                } else {
                    hasNext = false;
                }
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    continue;
                }
                Map<String, String> materialDescMap = gdtMaterialService.selectDescMap(responseData.getList().stream().map(DailyReportApiListStruct::getImageId).collect(Collectors.toList()));

                List<GdtMaterialDayDataEntity> gdtAdGroupReportEntities = responseData.getList().stream().map(item -> {
                    GdtMaterialDayDataEntity entity = BeanUtil.copyProperties(item, GdtMaterialDayDataEntity.class);
                    // 查询图片描述
                    String imageDesc = materialDescMap.getOrDefault(item.getImageId(), "");
                    if (StringUtils.isBlank(imageDesc)) {
                        imageDesc = syncMaterialById(account, NumberUtils.defaultLong(item.getAccountId(), advertisementId), IMAGE.getType(), item.getImageId());
                    }
                    String materialEditor = parseFromMaterialDesc(imageDesc);
                    if (editorMap.containsKey(materialEditor)) {
                        materialEditor = editorMap.get(materialEditor);
                    } else if (!editorSet.contains(materialEditor)) {
                        materialEditor = "";
                    }

                    entity.setCurDate(day);
                    entity.setAdvertisementId(NumberUtils.defaultLong(item.getAccountId(), advertisementId));
                    entity.setMaterialId(item.getImageId());
                    entity.setMaterialType(IMAGE.getType());
                    entity.setMaterialDesc(imageDesc);
                    entity.setMaterialEditor(StringUtils.defaultIfBlank(materialEditor, "未知"));
                    return entity;
                }).filter(entity -> {
                    // 过滤掉以"图片"开头的imageDesc
                    String imageDesc = entity.getMaterialDesc();
                    return StringUtils.isBlank(imageDesc) || !imageDesc.startsWith("图片")
                            || chinesePattern.matcher(imageDesc).find() || !imageDesc.startsWith("视频封面");
                }).collect(Collectors.toList());
                gdtMaterialDayDataService.batchInsertUpdate(gdtAdGroupReportEntities);
            }
        } catch (Exception e) {
            log.error("同步广点通图片素材日数据异常,advertiserId:{},e:", advertisementId, e);
        }
    }

    @Override
    public GdtMiniappDayDataEntity syncMiniAppAdvertData(Date date, ShortplayAppEntity entity, GdtSumDayDataBO dayDataBO, int retryTime) {
        WxMaService wxMaService = getWxMaService(entity);
        try {
            GdtMiniappDayDataEntity dataEntity = new GdtMiniappDayDataEntity();
            dataEntity.setCurDate(date);
            dataEntity.setMiniappId(entity.getAppid());
            dataEntity.setMiniappName(entity.getAppname());
            dataEntity.setStayTimeUv(0);
            if (Objects.nonNull(dayDataBO)) {
                dataEntity.setCost(dayDataBO.getCost());
                dataEntity.setCashCost(dayDataBO.getCashCost());
                dataEntity.setAdMonetizationAmount(dayDataBO.getAdMonetizationAmount());
            } else {
                dataEntity.setCost(0);
                dataEntity.setCashCost(0);
                dataEntity.setAdMonetizationAmount(0);
            }

            //查询广点通指定小程序日数据
            WxMaRetainInfo dailyRetainInfo = wxMaService.getAnalysisService().getDailyRetainInfo(date, date);
            log.info("小程序日数据,{}", dailyRetainInfo);
            Integer dayVisitUv = Objects.isNull(dailyRetainInfo.getVisitUv()) ? 0 : dailyRetainInfo.getVisitUv().get(0);
            dataEntity.setVisitUv(dayVisitUv);
            WxPublisherAdposGeneralBo publisherStat = wxMaAdService.getPublisherStat(wxMaService, date);
            log.info("小程序广告数据,{}", publisherStat);
            dataEntity.setEcpm(NumberUtils.defaultInt(publisherStat.getEcpm()));
            dataEntity.setIncome(publisherStat.getIncome());
            dataEntity.setExposureCount(publisherStat.getExposureCount());
            dataEntity.setReqSuccCount(publisherStat.getReqSuccCount());
            dataEntity.setClickCount(publisherStat.getClickCount());

            List<WxMaVisitTrend> trendList = wxMaService.getAnalysisService().getDailyVisitTrend(date, date);
            log.info("小程序数据日趋势,{}", JSON.toJSONString(trendList));
            if (CollectionUtils.isNotEmpty(trendList)) {
                dataEntity.setStayTimeUv(Convert.toInt(trendList.get(0).getStayTimeUv(), 0));
            }
            return dataEntity;
        } catch (WxErrorException e) {
            if (retryTime >= 0) {
                ThreadUtil.sleep(5000);
                return syncMiniAppAdvertData(date, entity, dayDataBO, retryTime - 1);
            }
            log.error("同步小程序数据异常", e);
            return null;
        }
    }

    @Override
    public void syncAdgroup(GdtAccountEntity account, GdtAdvertisementAccountEntity advertisementAccount, Long adgroupId) {
        Long advertisementId = advertisementAccount.getAdvertisementId();
        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);

        List<FilteringStruct> filtering = new ArrayList<>();
        if (null != adgroupId) {
            filtering.add(new FilteringStruct().field("adgroup_id").operator(FilterOperator.EQUALS).values(Arrays.asList(String.valueOf(adgroupId))));
        }
        List<String> fields = Lists.newArrayList("adgroup_id", "targeting", "targeting_translation", "configured_status", "created_time", "last_modified_time", "is_deleted", "system_status", "adgroup_name", "marketing_goal", "marketing_sub_goal", "marketing_carrier_type", "marketing_carrier_detail", "marketing_target_type", "marketing_target_id", "begin_date", "end_date", "first_day_begin_time", "bid_amount", "optimization_goal", "time_series", "automatic_site_enabled", "site_set", "daily_budget", "scene_spec", "user_action_sets", "bid_strategy", "deep_conversion_spec", "bid_mode", "auto_acquisition_enabled", "marketing_asset_id", "auto_acquisition_budget", "marketing_asset_outer_spec", "smart_bid_type", "auto_derived_creative_enabled", "search_expand_targeting_switch", "auto_derived_landing_page_switch", "data_model_version", "bid_scene", "deep_optimization_type", "flow_optimization_enabled", "marketing_scene", "exploration_strategy", "priority_site_set", "ecom_pkam_switch", "auto_acquisition_status");
        try {
            while (hasNext) {
                AdgroupsGetResponseData responseData = apiClient.adgroups().adgroupsGet(advertisementId, filtering, 1L, 100L, false, fields);
                log.info("同步广点通广告计划,advertiserId:{},adgroupId:{},data:{}", advertisementId, adgroupId, responseData);

                if (responseData.getPageInfo().getTotalPage() > page) {
                    page++;
                    hasNext = true;
                } else {
                    hasNext = false;
                }
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    continue;
                }

                List<GdtAdgroupEntity> gdtAdGroupEntities = responseData.getList().stream().map(item -> {
                    GdtAdgroupEntity entity = new GdtAdgroupEntity();
                    entity.setAccountId(account.getAccountId());
                    entity.setAdvertisementId(advertisementId);
                    entity.setAdvertisementName(advertisementAccount.getAdvertisementName());
                    entity.setAdgroupId(item.getAdgroupId());
                    entity.setAdgroupName(item.getAdgroupName());
                    entity.setTargetingTranslation(item.getTargetingTranslation());
                    entity.setBeginDate(DateUtil.parseDate(item.getBeginDate()));
                    entity.setEndDate(DateUtil.parseDate(item.getEndDate()));
                    entity.setBidAmount(item.getBidAmount());
                    entity.setDailyBudget(item.getDailyBudget());
                    entity.setCreatedTime(new Date(item.getCreatedTime() * 1000));
                    entity.setLastModifiedTime(new Date(item.getLastModifiedTime() * 1000));
                    entity.setIsDeleted(item.isIsDeleted() ? 1 : 0);
                    entity.setAdgroupDetail(JSON.toJSONString(item));
                    entity.setTargeting("");
                    if (null != item.getTargeting()) {
                        entity.setTargeting(JSON.toJSONString(item.getTargeting()));
                    }
                    return entity;
                }).collect(Collectors.toList());
                gdtAdgroupService.batchInsertUpdate(gdtAdGroupEntities);
            }
        } catch (Exception e) {
            log.error("同步广点通广告计划异常,advertiserId:{},adgroupId:{},e:", advertisementId, adgroupId, e);
        }
    }

    @Override
    public List<DynamicCreativesGetListStruct> syncCreative(GdtAccountEntity account, Long advertisementId, Long adgroupId, Long dynamicCreativeId) {
        if (null == adgroupId) {
            return Collections.emptyList();
        }

        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);

        List<FilteringStruct> filtering = Arrays.asList(new FilteringStruct().field("adgroup_id").operator(FilterOperator.EQUALS).values(Arrays.asList(String.valueOf(adgroupId))));
        if (NumberUtils.isNotNullOrLteZero(dynamicCreativeId)) {
            filtering.add(new FilteringStruct().field("dynamic_creative_id").operator(FilterOperator.EQUALS).values(Arrays.asList(String.valueOf(dynamicCreativeId))));
        }
        List<String> fields = Lists.newArrayList("adgroup_id", "dynamic_creative_id", "program_creative_info", "dynamic_creative_name", "creative_template_id", "delivery_mode", "dynamic_creative_type", "creative_components", "impression_tracking_url", "click_tracking_url", "page_track_url", "configured_status", "created_time", "last_modified_time", "is_deleted", "marketing_asset_verification", "source", "asset_inconsistent_status");
        List<DynamicCreativesGetListStruct> dynamicCreativesList = new ArrayList<>();
        try {
            while (hasNext) {
                DynamicCreativesGetResponseData responseData = apiClient.dynamicCreatives().dynamicCreativesGet(advertisementId, filtering, 1L, 100L, fields, false);
                log.info("同步广点通创意,advertiserId:{},adgroupId:{},data:{}", advertisementId, adgroupId, responseData);

                if (responseData.getPageInfo().getTotalPage() > page) {
                    page++;
                    hasNext = true;
                } else {
                    hasNext = false;
                }
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    continue;
                }
                dynamicCreativesList.addAll(responseData.getList());
                List<GdtCreativeEntity> gdtCreativeEntities = responseData.getList().stream().map(item -> {
                    GdtCreativeEntity entity = new GdtCreativeEntity();
                    entity.setAccountId(account.getAccountId());
                    entity.setAdvertisementId(advertisementId);
                    entity.setAdgroupId(item.getAdgroupId());
                    entity.setCreativeId(item.getDynamicCreativeId());
                    entity.setCreativeName(item.getDynamicCreativeName());
                    entity.setCreatedTime(new Date(item.getCreatedTime() * 1000));
                    entity.setLastModifiedTime(new Date(item.getLastModifiedTime() * 1000));
                    entity.setIsDeleted(item.isIsDeleted() ? 1 : 0);
                    entity.setCreativeDetail(JSON.toJSONString(item));
                    return entity;
                }).collect(Collectors.toList());
                gdtCreativeService.batchInsertUpdate(gdtCreativeEntities);
            }
        } catch (Exception e) {
            log.error("同步广点通创意异常,advertiserId:{},adgroupId:{},e:", advertisementId, adgroupId, e);
        }
        return dynamicCreativesList;
    }

    @Override
    public Map<String, GdtMaterialEntity> syncImages(GdtAccountEntity account, Long advertisementId, List<String> imageIds) {
        //查询未同步的素材，然后同步
        Map<String, GdtMaterialEntity> materialEntityMap = gdtMaterialService.selectMap(imageIds);
        List<String> newImageIds = imageIds.stream().filter(image -> !materialEntityMap.containsKey(image)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newImageIds)) {
            return materialEntityMap;
        }
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);
        List<FilteringStruct> filtering = Arrays.asList(new FilteringStruct().field("image_id").operator(FilterOperator.IN).values(newImageIds));
        List<String> fields = Lists.newArrayList("image_id", "width", "height", "file_size", "type", "signature", "preview_url");
        try {
            ImagesGetResponseData responseData = apiClient.images().imagesGet(advertisementId, filtering, 1L, 100L, null, null, fields);
            //插入素材表
            List<GdtMaterialEntity> gdtMaterialEntities = responseData.getList().stream().map(item -> {
                GdtMaterialEntity entity = BeanUtil.copyProperties(item, GdtMaterialEntity.class);
                entity.setMaterialId(String.valueOf(item.getImageId()));
                entity.setMaterialType(IMAGE.getType());
                entity.setMaterialDesc(item.getDescription());
                entity.setMaterialCreateTime(new Date(item.getCreatedTime() * 1000));
                entity.setMaterialLastModifiedTime(new Date(item.getLastModifiedTime() * 1000));
                entity.setAdvertisementId(advertisementId);
                entity.setMaterialInfo(JSONObject.toJSONString(item));
                entity.setMaterialMd5(item.getSignature());
                return entity;
            }).collect(Collectors.toList());
            gdtMaterialService.batchInsertUpdate(gdtMaterialEntities);
            return gdtMaterialService.selectMap(imageIds);
        } catch (ApiException e) {
            log.error("同步图片素材异常,e:", e);
        } catch (TencentAdsResponseException e) {
            log.error("同步图片素材异常,e:", e);
        }
        return Collections.emptyMap();
    }

    @Override
    public Map<String, String> uploadImages(Long advertisementId, TencentAds apiClient, List<ImagesGetListStruct> imagesGetListStructs) {
        if (CollectionUtils.isEmpty(imagesGetListStructs)) {
            return Collections.emptyMap();
        }
        Map<String, String> imageMap = new HashMap<>();
        imagesGetListStructs.stream().forEach(imagesGetListStruct -> {
            try {
                //网络图片地址转file
                File file = ImageUtils.downloadImage(imagesGetListStruct.getPreviewUrl(), filePath);
                if (Objects.isNull(file)) {
                    log.error("广点通图片下载失败,advertiserId:{},data:{}", advertisementId, imagesGetListStruct);
                    return;
                }
                ImagesAddResponseData addResponseData = apiClient.images().imagesAdd(advertisementId, "UPLOAD_TYPE_FILE", SecureUtil.md5(file), file, null, imagesGetListStruct.getImageUsage().getValue(), imagesGetListStruct.getDescription(), imagesGetListStruct.getWidth(), imagesGetListStruct.getHeight(), imagesGetListStruct.getFileSize());
                log.info("图片上传,advertiserId:{},data:{}", advertisementId, addResponseData);
                imageMap.put(imagesGetListStruct.getImageId(), addResponseData.getImageId());

            } catch (ApiException e) {
                log.error("图片上传失败,advertiserId:{},data:{}", advertisementId, e);
            } catch (TencentAdsResponseException e) {
                log.error("图片上传失败,advertiserId:{},data:{}", advertisementId, e);
            }
        });
        return imageMap;
    }

    @Override
    public ImagesAddResponseData uploadImage(Long advertisementId, TencentAds apiClient, GdtMaterialEntity materialEntity, int retryTime) {
        try {
            if (Objects.isNull(materialEntity)) {
                return null;
            }

            //网络图片地址转file
            File file = ImageUtils.downloadImage(materialEntity.getPreviewUrl(), filePath);
            if (Objects.isNull(file)) {
                log.error("广点通图片下载失败,advertiserId:{},data:{}", advertisementId, materialEntity);
                return null;
            }
            ImagesGetListStruct struct = JSONObject.parseObject(materialEntity.getMaterialInfo(), ImagesGetListStruct.class);
            ImagesAddResponseData addResponseData;
            if (null != struct) {
                addResponseData = apiClient.images().imagesAdd(advertisementId, "UPLOAD_TYPE_FILE", SecureUtil.md5(file), file, null, struct.getImageUsage().getValue(), struct.getDescription(), struct.getWidth(), struct.getHeight(), struct.getFileSize());
            } else {
                // 创量素材没有那些数据
                addResponseData = apiClient.images().imagesAdd(advertisementId, "UPLOAD_TYPE_FILE", SecureUtil.md5(file), file, null, null, null, null, null, null);
            }
            log.info("图片上传,advertiserId:{},data:{}", advertisementId, addResponseData);
            return addResponseData;

        } catch (ApiException | TencentAdsResponseException e) {
            if (retryTime > 0) {
                ThreadUtil.sleep(1000L);
                return uploadImage(advertisementId, apiClient, materialEntity, retryTime - 1);
            }
            log.error("图片上传失败,advertiserId:{},data:{}", advertisementId, e);
        }

        return null;
    }

    @Override
    public Long promrammedAdd(Long advertisementId, Long adgroupId, TencentAds apiClient, List<MaterialGroupCreateStruct> createMaterialGroups, int retryTime, List<String> failList) {
        try {
            ProgrammedAddRequest request = new ProgrammedAddRequest();
            request.setAccountId(advertisementId);
            request.setAdgroupId(adgroupId);
            request.setAutoDerivedProgramCreativeSwitch(true);
            request.setStandardSwitch(true);
            request.setCreateMaterialGroups(createMaterialGroups);
            ProgrammedAddResponseData programmedAddResponseData = apiClient.programmed().programmedAdd(request);
            return programmedAddResponseData.getMaterialDeriveId();
        } catch (Exception e) {
            if (e.getMessage().contains("timeout") && retryTime >= 0) {
                ThreadUtil.sleep(1000L);
                return promrammedAdd(advertisementId, adgroupId, apiClient, createMaterialGroups, retryTime - 1, failList);
            }
            failList.add(StrUtil.format("广告账户{},计划{}创建创意失败，原因:{}", advertisementId, adgroupId, e.getMessage()));
            log.error("创建模版预览异常,e:", e);
        }
        return null;
    }

    @Override
    public Set<Pair<Long, Long>> getAdgroupAge(GdtAccountEntity account, Long advertisementId) {
        Set<Pair<Long, Long>> ageSet = new HashSet<>();
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);

        List<FilteringStruct> filtering = new ArrayList<>();
        List<String> fields = Lists.newArrayList("adgroup_id", "targeting", "targeting_translation", "configured_status", "created_time", "last_modified_time", "is_deleted", "system_status", "adgroup_name", "marketing_goal", "marketing_sub_goal", "marketing_carrier_type", "marketing_carrier_detail", "marketing_target_type", "marketing_target_id", "begin_date", "end_date", "first_day_begin_time", "bid_amount", "optimization_goal", "time_series", "automatic_site_enabled", "site_set", "daily_budget", "scene_spec", "user_action_sets", "bid_strategy", "deep_conversion_spec", "bid_mode", "auto_acquisition_enabled", "marketing_asset_id", "auto_acquisition_budget", "marketing_asset_outer_spec", "smart_bid_type", "auto_derived_creative_enabled", "search_expand_targeting_switch", "auto_derived_landing_page_switch", "data_model_version", "bid_scene", "deep_optimization_type", "flow_optimization_enabled", "marketing_scene", "exploration_strategy", "priority_site_set", "ecom_pkam_switch", "auto_acquisition_status");
        try {
            AdgroupsGetResponseData responseData = apiClient.adgroups().adgroupsGet(advertisementId, filtering, 1L, 100L, false, fields);
            if (CollectionUtils.isNotEmpty(responseData.getList())) {
                responseData.getList().forEach(adgroup -> {
                    List<AgeStruct> ageList = adgroup.getTargeting().getAge();
                    if (CollectionUtils.isNotEmpty(ageList)) {
                        ageList.forEach(age -> ageSet.add(Pair.of(age.getMin(), age.getMax())));
                    }
                });
            }
        } catch (Exception e) {
            log.error("查询广点通广告计划年龄定向异常,advertiserId:{},e:", advertisementId, e);
        }
        return ageSet;
    }

    private void syncMaterial(GdtAccountEntity account, Long advertisementId, Integer materialType) {
        boolean hasNext = true;
        long page = 1L;
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);

        // 获取上次创建时间(秒级时间戳)
        Long createdTime = redisCache.getCacheObject(RedisKeyFactory.K040.join(advertisementId, materialType));
        if (null != createdTime) {
            // 保险起见，查一下昨天至今天创建的素材
            createdTime = Math.min(createdTime, DateUtil.beginOfDay(DateUtil.yesterday()).getTime() / 1000);
        }

        List<FilteringStruct> filtering = new ArrayList<>();
        filtering.add(new FilteringStruct().field("created_time").operator(FilterOperator.LESS_EQUALS).values(Arrays.asList(String.valueOf(new Date().getTime() / 1000))));
        if (null != createdTime) {
            filtering.add(new FilteringStruct().field("created_time").operator(FilterOperator.GREATER_EQUALS).values(Arrays.asList(String.valueOf(createdTime))));
        }

        try {
            AtomicReference<Long> maxCreatedTime = new AtomicReference<>(0L);
            while (hasNext) {
                if (isImage(materialType)) {
                    ImagesGetResponseData responseData = apiClient.images().imagesGet(advertisementId, filtering, page, 100L, null, null, Collections.emptyList());
                    log.info("同步广点通图片素材信息,advertiserId:{},data:{}", advertisementId, responseData);

                    if (responseData.getPageInfo().getTotalPage() > page) {
                        page++;
                        hasNext = true;
                    } else {
                        hasNext = false;
                    }
                    if (CollectionUtils.isEmpty(responseData.getList())) {
                        continue;
                    }
                    //查询md5和素材id对应
                    List<GdtMaterialEntity> gdtMaterialEntities = responseData.getList().stream().map(item -> {
                        GdtMaterialEntity entity = BeanUtil.copyProperties(item, GdtMaterialEntity.class);
                        entity.setMaterialId(String.valueOf(item.getImageId()));
                        entity.setMaterialType(materialType);
                        entity.setMaterialDesc(item.getDescription());
                        entity.setMaterialCreateTime(new Date(item.getCreatedTime() * 1000));
                        entity.setMaterialLastModifiedTime(new Date(item.getLastModifiedTime() * 1000));
                        entity.setAdvertisementId(advertisementId);
                        if (null == maxCreatedTime.get() || maxCreatedTime.get() < item.getCreatedTime()) {
                            maxCreatedTime.set(item.getCreatedTime());
                        }
                        entity.setMaterialInfo(JSONObject.toJSONString(item));
                        entity.setMaterialMd5(item.getSignature());
                        return entity;
                    }).collect(Collectors.toList());
                    gdtMaterialService.batchInsertUpdate(gdtMaterialEntities);
                } else {
                    VideosGetResponseData responseData = apiClient.videos().videosGet(advertisementId, filtering, page, 100L, null, null, Collections.emptyList());
                    log.info("同步广点通视频素材信息,advertiserId:{},data:{}", advertisementId, responseData);

                    if (responseData.getPageInfo().getTotalPage() > page) {
                        page++;
                        hasNext = true;
                    } else {
                        hasNext = false;
                    }
                    if (CollectionUtils.isEmpty(responseData.getList())) {
                        continue;
                    }
                    List<GdtMaterialEntity> gdtMaterialEntities = responseData.getList().stream().map(item -> {
                        GdtMaterialEntity entity = BeanUtil.copyProperties(item, GdtMaterialEntity.class);
                        entity.setMaterialId(String.valueOf(item.getVideoId()));
                        entity.setMaterialType(materialType);
                        entity.setMaterialDesc(item.getDescription());
                        entity.setMaterialCreateTime(new Date(item.getCreatedTime() * 1000));
                        entity.setMaterialLastModifiedTime(new Date(item.getLastModifiedTime() * 1000));
                        entity.setAdvertisementId(advertisementId);
                        if (null == maxCreatedTime.get() || maxCreatedTime.get() < item.getCreatedTime()) {
                            maxCreatedTime.set(item.getCreatedTime());
                        }
                        entity.setMaterialInfo(JSONObject.toJSONString(item));
                        entity.setMaterialMd5("");
                        return entity;
                    }).collect(Collectors.toList());
                    gdtMaterialService.batchInsertUpdate(gdtMaterialEntities);
                }
            }
            if (maxCreatedTime.get() > 0L) {
                redisCache.setCacheObject(RedisKeyFactory.K040.join(advertisementId, materialType), maxCreatedTime.get());
            }
        } catch (Exception e) {
            log.error("同步广点通素材信息,advertiserId:{},e:", advertisementId, e);
        }
    }

    private String syncMaterialById(GdtAccountEntity account, Long advertisementId, Integer materialType, String materialId) {
        if (StringUtils.isBlank(materialId) || null == advertisementId) {
            return "";
        }
        TencentAds apiClient = getApiClient(account.getAppId(), account.getAccountId(), true);

        List<FilteringStruct> filtering = new ArrayList<>();
        if (isImage(materialType)) {
            filtering.add(new FilteringStruct().field("image_id").operator(FilterOperator.EQUALS).values(Arrays.asList(materialId)));
        } else {
            filtering.add(new FilteringStruct().field("video_id").operator(FilterOperator.EQUALS).values(Arrays.asList(materialId)));
        }

        try {
            if (isImage(materialType)) {
                ImagesGetResponseData responseData = apiClient.images().imagesGet(advertisementId, filtering, 1L, 10L, null, null, Collections.emptyList());
                log.info("同步广点通单条图片素材信息,advertiserId:{},data:{}", advertisementId, responseData);
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    return "";
                }
                List<GdtMaterialEntity> gdtMaterialEntities = responseData.getList().stream().map(item -> {
                    GdtMaterialEntity entity = BeanUtil.copyProperties(item, GdtMaterialEntity.class);
                    entity.setMaterialId(String.valueOf(item.getImageId()));
                    entity.setMaterialType(materialType);
                    entity.setMaterialDesc(item.getDescription());
                    entity.setMaterialCreateTime(new Date(item.getCreatedTime() * 1000));
                    entity.setMaterialLastModifiedTime(new Date(item.getLastModifiedTime() * 1000));
                    entity.setAdvertisementId(advertisementId);
                    entity.setMaterialInfo(JSONObject.toJSONString(item));
                    entity.setMaterialMd5(item.getSignature());
                    return entity;
                }).collect(Collectors.toList());
                gdtMaterialService.batchInsertUpdate(gdtMaterialEntities);
                return gdtMaterialEntities.get(0).getMaterialId();
            } else {
                VideosGetResponseData responseData = apiClient.videos().videosGet(advertisementId, filtering, 1L, 10L, null, null, Collections.emptyList());
                log.info("同步广点通单条视频素材信息,advertiserId:{},data:{}", advertisementId, responseData);
                if (CollectionUtils.isEmpty(responseData.getList())) {
                    return "";
                }
                List<GdtMaterialEntity> gdtMaterialEntities = responseData.getList().stream().map(item -> {
                    GdtMaterialEntity entity = BeanUtil.copyProperties(item, GdtMaterialEntity.class);
                    entity.setMaterialId(String.valueOf(item.getVideoId()));
                    entity.setMaterialType(materialType);
                    entity.setMaterialDesc(item.getDescription());
                    entity.setMaterialCreateTime(new Date(item.getCreatedTime() * 1000));
                    entity.setMaterialLastModifiedTime(new Date(item.getLastModifiedTime() * 1000));
                    entity.setAdvertisementId(advertisementId);
                    entity.setMaterialInfo(JSONObject.toJSONString(item));
                    entity.setMaterialMd5("");
                    return entity;
                }).collect(Collectors.toList());
                gdtMaterialService.batchInsertUpdate(gdtMaterialEntities);
                return gdtMaterialEntities.get(0).getMaterialId();
            }
        } catch (Exception e) {
            log.error("同步广点通单条素材信息,advertiserId:{},e:", advertisementId, e);
        }
        return "";
    }

    private WxMaService getWxMaService(ShortplayAppEntity entity) {
        WxMaService maService = new WxMaServiceImpl();
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(entity.getAppid());
        config.setSecret(entity.getAppsecret());
        maService.setWxMaConfig(config);
        return maService;
    }

    /**
     * 查询所有的剪辑
     *
     * @return 昵称-姓名映射
     */
    private Map<String, String> selectTotalMaterialEditor() {
        AgtAccountUserEntity param = new AgtAccountUserEntity();
        param.setPost(String.valueOf(AgtAccountUserPost.EDITOR.getCode()));
        List<AgtAccountUserEntity> list = agtAccountUserService.selectAgtAccountUserList(param);
        return list.stream().collect(Collectors.toMap(AgtAccountUserEntity::getUserNickname, AgtAccountUserEntity::getUserRealname, (v1, v2) -> v2));
    }

    /**
     * 从素材描述中解析出剪辑姓名
     *
     * @param desc 素材描述
     * @return 剪辑姓名
     */
    private String parseFromMaterialDesc(String desc) {
        if (StringUtils.isBlank(desc)) {
            return "";
        }
        List<String> parts = StrUtil.split(desc, "-");
        return parts.size() > 1 ? parts.get(1) : "";
    }

    /**
     * 解析短剧小程序名称
     */
    private String parseAppName(Set<String> appNames, String appName) {
        if (appNames.contains(appName)) {
            return appName;
        }
        // 兼容一下有问题的命名
        if (appName.contains("_")) {
            appName = appName.substring(0, appName.indexOf("_"));
            if (appNames.contains(appName)) {
                return appName;
            }
        }
        return "未知";
    }
}
