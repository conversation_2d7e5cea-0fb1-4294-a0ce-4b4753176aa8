<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.slotconfig.SlotConfigItemMapper">

    <resultMap type="com.ruoyi.system.entity.slotconfig.SlotConfigItemEntity" id="SlotConfigItemResult">
        <result property="id" column="id"/>
        <result property="slotId" column="slot_id"/>
        <result property="configId" column="config_id"/>
        <result property="contentName" column="content_name"/>
        <result property="content" column="content"/>
        <result property="ratio" column="ratio"/>
        <result property="contentType" column="content_type"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectSlotConfigItemVo">
        select id,
               slot_id,
               config_id,
               content_name,
               content,
               ratio,
               content_type,
               is_deleted,
               gmt_create,
               gmt_modified
        from tb_slot_config_item
    </sql>

    <select id="selectSlotConfigItemList" parameterType="com.ruoyi.system.entity.slotconfig.SlotConfigItemEntity"
            resultMap="SlotConfigItemResult">
        <include refid="selectSlotConfigItemVo"/>
        <where>
            <if test="slotId != null ">and slot_id = #{slotId}</if>
            <if test="configId != null ">and config_id = #{configId}</if>
            <if test="contentName != null  and contentName != ''">and content_name like concat('%', #{contentName},
                '%')
            </if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="contentType != null ">and content_type = #{contentType}</if>
            <if test="isDeleted != null ">and is_deleted = #{isDeleted}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSlotConfigItemById" parameterType="Long" resultMap="SlotConfigItemResult">
        <include refid="selectSlotConfigItemVo"/>
        where id = #{id}
    </select>
    <select id="selectBySlotId" resultMap="SlotConfigItemResult">
        <include refid="selectSlotConfigItemVo"/>
        where slot_id = #{slotId} and is_deleted = 0
    </select>
    <select id="selectByIds" resultMap="SlotConfigItemResult">
        <include refid="selectSlotConfigItemVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertSlotConfigItem" parameterType="com.ruoyi.system.entity.slotconfig.SlotConfigItemEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_config_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="slotId != null">slot_id,</if>
            <if test="configId != null">config_id,</if>
            <if test="contentName != null">content_name,</if>
            <if test="content != null">content,</if>
            <if test="contentType != null">content_type,</if>
            <if test="ratio != null">ratio,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="slotId != null">#{slotId},</if>
            <if test="configId != null">#{configId},</if>
            <if test="contentName != null">#{contentName},</if>
            <if test="content != null">#{content},</if>
            <if test="contentType != null">#{contentType},</if>
            <if test="ratio != null">#{ratio},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateSlotConfigItem" parameterType="com.ruoyi.system.entity.slotconfig.SlotConfigItemEntity">
        update tb_slot_config_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="contentName != null">content_name = #{contentName},</if>
            <if test="content != null">content = #{content},</if>
            <if test="ratio != null">ratio = #{ratio},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSlotConfigItemById" parameterType="Long">
        delete
        from tb_slot_config_item
        where id = #{id}
    </delete>

    <delete id="deleteSlotConfigItemByIds" parameterType="String">
        delete from tb_slot_config_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="deleteByIds">
        update tb_slot_config_item set is_deleted = 1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>