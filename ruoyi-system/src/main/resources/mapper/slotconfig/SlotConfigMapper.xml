<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.slotconfig.SlotConfigMapper">

    <resultMap type="com.ruoyi.system.entity.slotconfig.SlotConfigEntity" id="SlotConfigResult">
            <result property="id"    column="id"    />
            <result property="slotId"    column="slot_id"    />
            <result property="configName"    column="config_name"    />
            <result property="targetArea"    column="target_area"    />
            <result property="isDeleted"    column="is_deleted"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotConfigVo">
        select id, slot_id, config_name, target_area, is_deleted, gmt_create, gmt_modified from tb_slot_config
    </sql>

    <select id="selectSlotConfigList" parameterType="com.ruoyi.system.entity.slotconfig.SlotConfigEntity" resultMap="SlotConfigResult">
        <include refid="selectSlotConfigVo"/>
        <where>
                        <if test="slotId != null "> and slot_id = #{slotId}</if>
                        <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
                        <if test="targetArea != null  and targetArea != ''"> and target_area = #{targetArea}</if>
                        <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSlotConfigById" parameterType="Long" resultMap="SlotConfigResult">
            <include refid="selectSlotConfigVo"/>
            where id = #{id}
    </select>
    <select id="selectBySlotId" resultMap="SlotConfigResult">
        <include refid="selectSlotConfigVo"/>
        where slot_id = #{slotId} and is_deleted = 0
    </select>
    <select id="selectByIds" resultMap="SlotConfigResult">
        <include refid="selectSlotConfigVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertSlotConfig" parameterType="com.ruoyi.system.entity.slotconfig.SlotConfigEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="slotId != null">slot_id,</if>
                    <if test="configName != null and configName != ''">config_name,</if>
                    <if test="targetArea != null">target_area,</if>
                    <if test="isDeleted != null">is_deleted,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="slotId != null">#{slotId},</if>
                    <if test="configName != null and configName != ''">#{configName},</if>
                    <if test="targetArea != null">#{targetArea},</if>
                    <if test="isDeleted != null">#{isDeleted},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateSlotConfig" parameterType="com.ruoyi.system.entity.slotconfig.SlotConfigEntity">
        update tb_slot_config
        <trim prefix="SET" suffixOverrides=",">
                    <if test="slotId != null">slot_id = #{slotId},</if>
                    <if test="configName != null and configName != ''">config_name = #{configName},</if>
                    <if test="targetArea != null">target_area = #{targetArea},</if>
                    <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSlotConfigById" parameterType="Long">
        delete from tb_slot_config where id = #{id}
    </delete>

    <delete id="deleteSlotConfigByIds" parameterType="String">
        delete from tb_slot_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="deleteByIds">
        update tb_slot_config set is_deleted = 1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>