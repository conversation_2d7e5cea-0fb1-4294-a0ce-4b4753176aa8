<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.relate.VideoWxRelateMapper">

    <resultMap type="com.ruoyi.system.entity.relate.VideoWxRelateEntity" id="VideoWxRelateResult">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="videoId" column="video_id"/>
        <result property="videoGroupId" column="video_group_id"/>
        <result property="mediaId" column="media_id"/>
        <result property="wxUploadStatus" column="wx_upload_status"/>
        <result property="uploadErrorMsg" column="upload_error_msg"/>
        <result property="wxAuditStatus" column="wx_audit_status"/>
        <result property="auditErrorMsg" column="audit_error_msg"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectVideoWxRelateVo">
        select id, app_id, video_id, video_group_id, media_id, wx_upload_status, upload_error_msg, wx_audit_status,
        audit_error_msg, is_deleted, gmt_create, gmt_modified from tb_video_wx_relate
    </sql>

    <select id="selectVideoWxRelateList" parameterType="com.ruoyi.system.entity.relate.VideoWxRelateEntity"
            resultMap="VideoWxRelateResult">
        <include refid="selectVideoWxRelateVo"/>
        <where>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
            <if test="videoId != null ">and video_id = #{videoId}</if>
            <if test="videoGroupId != null ">and video_group_id = #{videoGroupId}</if>
            <if test="mediaId != null ">and media_id = #{mediaId}</if>
            <if test="wxUploadStatus != null ">and wx_upload_status = #{wxUploadStatus}</if>
            <if test="uploadErrorMsg != null  and uploadErrorMsg != ''">and upload_error_msg = #{uploadErrorMsg}</if>
            <if test="wxAuditStatus != null ">and wx_audit_status = #{wxAuditStatus}</if>
            <if test="auditErrorMsg != null  and auditErrorMsg != ''">and audit_error_msg = #{auditErrorMsg}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectVideoWxRelateById" parameterType="Integer" resultMap="VideoWxRelateResult">
        <include refid="selectVideoWxRelateVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <select id="selectListByVideoGroupIdAndAppId"
            resultType="com.ruoyi.system.entity.relate.VideoWxRelateEntity">
        <include refid="selectVideoWxRelateVo"/>
        where video_group_id = #{videoGroupId} and app_id = #{appId} and is_deleted = 0
    </select>

    <select id="selectListByVideoIds" resultType="com.ruoyi.system.entity.relate.VideoWxRelateEntity">
        <include refid="selectVideoWxRelateVo"/>
        where video_id in
        <foreach item="id" collection="videoIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertVideoWxRelate" parameterType="com.ruoyi.system.entity.relate.VideoWxRelateEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_video_wx_relate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="videoId != null">video_id,</if>
            <if test="videoGroupId != null">video_group_id,</if>
            <if test="mediaId != null">media_id,</if>
            <if test="wxUploadStatus != null">wx_upload_status,</if>
            <if test="uploadErrorMsg != null and uploadErrorMsg != ''">upload_error_msg,</if>
            <if test="wxAuditStatus != null">wx_audit_status,</if>
            <if test="auditErrorMsg != null and auditErrorMsg != ''">audit_error_msg,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="videoId != null">#{videoId},</if>
            <if test="videoGroupId != null">#{videoGroupId},</if>
            <if test="mediaId != null">#{mediaId},</if>
            <if test="wxUploadStatus != null">#{wxUploadStatus},</if>
            <if test="uploadErrorMsg != null and uploadErrorMsg != ''">#{uploadErrorMsg},</if>
            <if test="wxAuditStatus != null">#{wxAuditStatus},</if>
            <if test="auditErrorMsg != null and auditErrorMsg != ''">#{auditErrorMsg},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateVideoWxRelate" parameterType="com.ruoyi.system.entity.relate.VideoWxRelateEntity">
        update tb_video_wx_relate
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="videoGroupId != null">video_group_id = #{videoGroupId},</if>
            <if test="mediaId != null">media_id = #{mediaId},</if>
            <if test="wxUploadStatus != null">wx_upload_status = #{wxUploadStatus},</if>
            <if test="uploadErrorMsg != null and uploadErrorMsg != ''">upload_error_msg = #{uploadErrorMsg},</if>
            <if test="wxAuditStatus != null">wx_audit_status = #{wxAuditStatus},</if>
            <if test="auditErrorMsg != null and auditErrorMsg != ''">audit_error_msg = #{auditErrorMsg},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update tb_video_wx_relate
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.mediaId != null">media_id = #{item.mediaId},</if>
                <if test="item.wxUploadStatus != null">wx_upload_status = #{item.wxUploadStatus},</if>
                <if test="item.uploadErrorMsg != null and item.uploadErrorMsg != ''">upload_error_msg =
                    #{item.uploadErrorMsg},
                </if>
                <if test="item.wxAuditStatus != null">wx_audit_status = #{item.wxAuditStatus},</if>
                <if test="item.auditErrorMsg != null and item.auditErrorMsg != ''">audit_error_msg =
                    #{item.auditErrorMsg},
                </if>
            </trim>
            where video_id = #{item.videoId}
        </foreach>
    </update>

    <update id="batchUpdateByMediaId">
        <foreach collection="list" item="item" separator=";">
            UPDATE tb_video_wx_relate
            <set>
                <if test="item.wxUploadStatus != null">
                    wx_upload_status = #{item.wxUploadStatus},
                </if>
                <if test="item.wxAuditStatus != null">
                    wx_audit_status = #{item.wxAuditStatus},
                </if>
                <if test="item.uploadErrorMsg != null">
                    upload_error_msg = #{item.uploadErrorMsg},
                </if>
                <if test="item.auditErrorMsg != null">
                    audit_error_msg = #{item.auditErrorMsg},
                </if>
            </set>
            WHERE media_id=#{item.mediaId}
        </foreach>
    </update>

    <update id="batchUpdateByVideoIdAndAppId">
        <foreach collection="list" item="item" separator=";">
            update tb_video_wx_relate
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.mediaId != null">media_id = #{item.mediaId},</if>
                <if test="item.wxUploadStatus != null">wx_upload_status = #{item.wxUploadStatus},</if>
                <if test="item.uploadErrorMsg != null and item.uploadErrorMsg != ''">upload_error_msg =
                    #{item.uploadErrorMsg},
                </if>
                <if test="item.wxAuditStatus != null">wx_audit_status = #{item.wxAuditStatus},</if>
                <if test="item.auditErrorMsg != null and item.auditErrorMsg != ''">audit_error_msg =
                    #{item.auditErrorMsg},
                </if>
            </trim>
            where video_id = #{item.videoId} and app_id = #{item.appId} and is_deleted = 0
        </foreach>
    </update>

    <delete id="deleteVideoWxRelateById" parameterType="Integer">
        update tb_video_wx_relate set is_deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteVideoWxRelateByIds" parameterType="String">
        update tb_video_wx_relate set is_deleted = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByVideoId">
        update tb_video_wx_relate set is_deleted = 1 where video_id = #{videoId}
    </delete>
</mapper>
