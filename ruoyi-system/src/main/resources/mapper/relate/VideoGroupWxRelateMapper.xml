<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.relate.VideoGroupWxRelateMapper">

    <resultMap type="com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity" id="VideoGroupWxRelateResult">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="videoGroupId" column="video_group_id"/>
        <result property="dramaId" column="drama_id"/>
        <result property="wxAuditStatus" column="wx_audit_status"/>
        <result property="auditErrorMsg" column="audit_error_msg"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectVideoGroupWxRelateVo">
        select id, app_id, video_group_id, drama_id, wx_audit_status, audit_error_msg, is_deleted, gmt_create,
        gmt_modified from
        tb_video_group_wx_relate
    </sql>

    <select id="selectVideoGroupWxRelateList" parameterType="com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity"
            resultMap="VideoGroupWxRelateResult">
        <include refid="selectVideoGroupWxRelateVo"/>
        <where>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
            <if test="videoGroupId != null ">and video_group_id = #{videoGroupId}</if>
            <if test="dramaId != null ">and drama_id = #{dramaId}</if>
            <if test="wxAuditStatus != null ">and wx_audit_status = #{wxAuditStatus}</if>
            <if test="auditErrorMsg != null  and auditErrorMsg != ''">and audit_error_msg = #{auditErrorMsg}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
            and is_deleted = 0
        </where>
        order by id desc
    </select>

    <select id="selectVideoGroupWxRelateById" parameterType="Integer" resultMap="VideoGroupWxRelateResult">
        <include refid="selectVideoGroupWxRelateVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <select id="selectWxAllAuditTaskByAppId"
            resultType="com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity">
        <include refid="selectVideoGroupWxRelateVo"/>
        where is_deleted = 0 and wx_audit_status = #{status} and app_id = #{appId}
    </select>

    <select id="selectListByGroupIds" resultType="com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity">
        <include refid="selectVideoGroupWxRelateVo"/>
        where is_deleted = 0 and video_group_id in
        <foreach item="id" collection="videoGroupIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByDramaId" resultType="com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity">
        <include refid="selectVideoGroupWxRelateVo"/>
        where drama_id = #{dramaId}
    </select>

    <insert id="insertVideoGroupWxRelate" parameterType="com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_video_group_wx_relate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="videoGroupId != null">video_group_id,</if>
            <if test="dramaId != null">drama_id,</if>
            <if test="wxAuditStatus != null">wx_audit_status,</if>
            <if test="auditErrorMsg != null and auditErrorMsg != ''">audit_error_msg,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="videoGroupId != null">#{videoGroupId},</if>
            <if test="dramaId != null">#{dramaId},</if>
            <if test="wxAuditStatus != null">#{wxAuditStatus},</if>
            <if test="auditErrorMsg != null and auditErrorMsg != ''">#{auditErrorMsg},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateVideoGroupWxRelate" parameterType="com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity">
        update tb_video_group_wx_relate
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="videoGroupId != null">video_group_id = #{videoGroupId},</if>
            <if test="dramaId != null">drama_id = #{dramaId},</if>
            <if test="wxAuditStatus != null">wx_audit_status = #{wxAuditStatus},</if>
            <if test="auditErrorMsg != null and auditErrorMsg != ''">audit_error_msg = #{auditErrorMsg},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update tb_video_group_wx_relate
            <set>
                <if test="item.wxAuditStatus != null">
                    wx_audit_status = #{item.wxAuditStatus},
                </if>
                <if test="item.auditErrorMsg != null">
                    audit_error_msg = #{item.auditErrorMsg},
                </if>
            </set>
            where video_group_id = #{item.videoGroupId} and app_id = #{item.appId}
        </foreach>
    </update>

    <delete id="deleteVideoGroupWxRelateById" parameterType="Integer">
        update tb_video_group_wx_relate set is_deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteVideoGroupWxRelateByIds" parameterType="String">
        update tb_video_group_wx_relate set is_deleted = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
