<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.dydata.DouyinMonitorKeywordMapper">

    <resultMap type="com.ruoyi.system.entity.dydata.DouyinMonitorKeywordEntity" id="DouyinMonitorKeywordResult">
            <result property="id"    column="id"    />
            <result property="keyword"    column="keyword"    />
            <result property="type"    column="type"    />
            <result property="startMonitorDate"    column="start_monitor_date"    />
            <result property="endMonitorDate"    column="end_monitor_date"    />
            <result property="monitorStatus"    column="monitor_status"    />
            <result property="creatorName"    column="creator_name"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectDouyinMonitorKeywordVo">
        select id, keyword, type, start_monitor_date, end_monitor_date, if(`end_monitor_date` &lt; curdate(), 0, monitor_status)  as monitor_status, creator_name, gmt_create, gmt_modified from tb_douyin_monitor_keyword
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.dydata.DouyinMonitorKeywordEntity" resultMap="DouyinMonitorKeywordResult">
        <include refid="selectDouyinMonitorKeywordVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="keyword != null"> and keyword like concat('%', #{keyword}, '%')</if>
            <if test="type != null"> and `type` = #{type}</if>
            <if test='monitorStatus != null and monitorStatus == 1'> and monitor_status = 1 and curdate() between start_monitor_date and end_monitor_date </if>
            <if test='monitorStatus != null and monitorStatus == 0'> and (monitor_status = 0 or curdate() not between start_monitor_date and end_monitor_date) </if>
        </where>
        order by monitor_status desc, `type` asc, id desc
    </select>

    <select id="selectById" parameterType="Long" resultMap="DouyinMonitorKeywordResult">
            <include refid="selectDouyinMonitorKeywordVo"/>
            where id = #{id}
    </select>

    <select id="selectEnableKeywords" resultMap="DouyinMonitorKeywordResult">
        <include refid="selectDouyinMonitorKeywordVo"/>
        where monitor_status = 1 and start_monitor_date &lt;= curdate() and end_monitor_date &gt;= curdate() and `type` = #{type}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.dydata.DouyinMonitorKeywordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_douyin_monitor_keyword
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="keyword != null">keyword,</if>
                <if test="type != null">`type`,</if>
                <if test="startMonitorDate != null">start_monitor_date,</if>
                <if test="endMonitorDate != null">end_monitor_date,</if>
                <if test="creatorName != null">creator_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="keyword != null">#{keyword},</if>
                <if test="type != null">#{type},</if>
                <if test="startMonitorDate != null">#{startMonitorDate},</if>
                <if test="endMonitorDate != null">#{endMonitorDate},</if>
                <if test="creatorName != null">#{creatorName},</if>
        </trim>
    </insert>

    <insert id="batchInsert">
        insert into tb_douyin_monitor_keyword(`keyword`,`type`,`start_monitor_date`,`end_monitor_date`,`creator_name`)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.keyword}, #{entity.type}, #{entity.startMonitorDate}, #{entity.endMonitorDate}, #{entity.creatorName})
        </foreach>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.dydata.DouyinMonitorKeywordEntity">
        update tb_douyin_monitor_keyword
        <trim prefix="SET" suffixOverrides=",">
                <if test="keyword != null">keyword = #{keyword},</if>
                <if test="type != null">`type` = #{type},</if>
                <if test="startMonitorDate != null">start_monitor_date = #{startMonitorDate},</if>
                <if test="endMonitorDate != null">end_monitor_date = #{endMonitorDate},</if>
                <if test="monitorStatus != null">monitor_status = #{monitorStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdate">
        update tb_douyin_monitor_keyword
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorStatus != null">monitor_status = #{monitorStatus},</if>
        </trim>
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </update>

    <select id="existByKeyword" resultType="Integer">
        SELECT 1
        FROM tb_douyin_monitor_keyword
        WHERE `keyword` = #{keyword} and `id` != #{id}
        LIMIT 1
    </select>

    <select id="countOpenKeywords" resultType="Integer">
        SELECT count(1)
        FROM tb_douyin_monitor_keyword
        WHERE (type = 1 and monitor_status = 1 and curdate() between start_monitor_date and end_monitor_date)
        <if test="ids!=null and ids.size()>0">
            or (type = 1 and id in
            <foreach collection="ids" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>
</mapper>
