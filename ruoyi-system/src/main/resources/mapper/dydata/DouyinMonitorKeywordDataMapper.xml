<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.dydata.DouyinMonitorKeywordDataMapper">

    <resultMap type="com.ruoyi.system.entity.dydata.DouyinMonitorKeywordDataEntity" id="DouyinMonitorKeywordDataResult">
            <result property="id"    column="id"    />
            <result property="keywordId"    column="keyword_id"    />
            <result property="keywordName"    column="keyword_name"    />
            <result property="awemeId"    column="aweme_id"    />
            <result property="authorUid"    column="author_uid"    />
            <result property="authorSecUid"    column="author_sec_uid"    />
            <result property="authorNickname"    column="author_nickname"    />
            <result property="authorUrl"    column="author_url"    />
            <result property="awemeDayN"    column="aweme_day_n"    />
            <result property="curDate"    column="cur_date"    />
            <result property="awemeDesc"    column="aweme_desc"    />
            <result property="anchorTitle"    column="anchor_title"    />
            <result property="videoUri"    column="video_uri"    />
            <result property="titleTag"    column="title_tag"    />
            <result property="awemeUrl"    column="aweme_url"    />
            <result property="coverUrl"    column="cover_url"    />
            <result property="shareUrl"    column="share_url"    />
            <result property="diggCount"    column="digg_count"    />
            <result property="collectCount"    column="collect_count"    />
            <result property="shareCount"    column="share_count"    />
            <result property="commentCount"    column="comment_count"    />
            <result property="diggAdd"    column="digg_add"    />
            <result property="collectAdd"    column="collect_add"    />
            <result property="shareAdd"    column="share_add"    />
            <result property="commentAdd"    column="comment_add"    />
            <result property="createTime"    column="create_time"    />
            <result property="descTxt"    column="desc_txt"    />
            <result property="duration"    column="duration"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectDouyinMonitorKeywordDataVo">
        select id, keyword_id, keyword_name, aweme_id, author_uid, author_sec_uid, author_nickname, anchor_title, video_uri, share_url, aweme_day_n, cur_date, aweme_desc, title_tag, aweme_url, cover_url, share_url, digg_count, collect_count, share_count, comment_count, digg_add, collect_add, share_add, comment_add, create_time, desc_txt, duration, gmt_create, gmt_modified,
            ifnull(share_url, concat('https://www.iesdouyin.com/share/video/', aweme_id)) as share_url,
            concat('https://www.douyin.com/user/', author_sec_uid) as author_url
        from tb_douyin_monitor_keyword_data
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.req.dydata.DouyinMonitorKeywordDataListReq" resultMap="DouyinMonitorKeywordDataResult">
        <include refid="selectDouyinMonitorKeywordDataVo"/>
        <where>
            <if test="keywordId != null "> and keyword_id = #{keywordId}</if>
            <if test="keywordName != null  and keywordName != ''"> and keyword_name like concat('%', #{keywordName}, '%')</if>
            <if test="titleTag != null  and titleTag != ''"> and title_tag like concat('%', #{titleTag}, '%')</if>
            <if test="awemeId != null  and awemeId != ''"> and aweme_id like concat('%', #{awemeId}, '%')</if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
        </where>
        order by cur_date desc, digg_add desc, digg_count desc
    </select>

    <select id="selectDouyinMonitorKeywordDataById" parameterType="Long" resultMap="DouyinMonitorKeywordDataResult">
            <include refid="selectDouyinMonitorKeywordDataVo"/>
            where id = #{id}
    </select>

    <select id="selectBy" resultMap="DouyinMonitorKeywordDataResult">
        <include refid="selectDouyinMonitorKeywordDataVo"/>
        where keyword_id = #{keywordId} and aweme_id = #{awemeId} and cur_date = #{curDate}
        limit 1
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.dydata.DouyinMonitorKeywordDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_douyin_monitor_keyword_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="keywordId != null">keyword_id,</if>
                <if test="keywordName != null and keywordName != ''">keyword_name,</if>
                <if test="awemeId != null and awemeId != ''">aweme_id,</if>
                <if test="authorUid != null and authorUid != ''">author_uid,</if>
                <if test="authorSecUid != null and authorSecUid != ''">author_sec_uid,</if>
                <if test="authorNickname != null and authorNickname != ''">author_nickname,</if>
                <if test="awemeDayN != null">aweme_day_n,</if>
                <if test="curDate != null">cur_date,</if>
                <if test="awemeDesc != null">aweme_desc,</if>
                <if test="anchorTitle != null">anchor_title,</if>
                <if test="videoUri != null">video_uri,</if>
                <if test="titleTag != null">title_tag,</if>
                <if test="awemeUrl != null">aweme_url,</if>
                <if test="coverUrl != null">cover_url,</if>
                <if test="shareUrl != null">share_url,</if>
                <if test="diggCount != null">digg_count,</if>
                <if test="collectCount != null">collect_count,</if>
                <if test="shareCount != null">share_count,</if>
                <if test="commentCount != null">comment_count,</if>
                <if test="diggAdd != null">digg_add,</if>
                <if test="collectAdd != null">collect_add,</if>
                <if test="shareAdd != null">share_add,</if>
                <if test="commentAdd != null">comment_add,</if>
                <if test="createTime != null">create_time,</if>
                <if test="descTxt != null">desc_txt,</if>
                <if test="duration != null">duration,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="keywordId != null">#{keywordId},</if>
                <if test="keywordName != null and keywordName != ''">#{keywordName},</if>
                <if test="awemeId != null and awemeId != ''">#{awemeId},</if>
                <if test="authorUid != null and authorUid != ''">#{authorUid},</if>
                <if test="authorSecUid != null and authorSecUid != ''">#{authorSecUid},</if>
                <if test="authorNickname != null and authorNickname != ''">#{authorNickname},</if>
                <if test="awemeDayN != null">#{awemeDayN},</if>
                <if test="curDate != null">#{curDate},</if>
                <if test="awemeDesc != null">#{awemeDesc},</if>
                <if test="anchorTitle != null">#{anchorTitle},</if>
                <if test="videoUri != null">#{videoUri},</if>
                <if test="titleTag != null">#{titleTag},</if>
                <if test="awemeUrl != null">#{awemeUrl},</if>
                <if test="coverUrl != null">#{coverUrl},</if>
                <if test="shareUrl != null">#{shareUrl},</if>
                <if test="diggCount != null">#{diggCount},</if>
                <if test="collectCount != null">#{collectCount},</if>
                <if test="shareCount != null">#{shareCount},</if>
                <if test="commentCount != null">#{commentCount},</if>
                <if test="diggAdd != null">#{diggAdd},</if>
                <if test="collectAdd != null">#{collectAdd},</if>
                <if test="shareAdd != null">#{shareAdd},</if>
                <if test="commentAdd != null">#{commentAdd},</if>
                <if test="createTime != null">#{createTime},</if>
                <if test="descTxt != null">#{descTxt},</if>
                <if test="duration != null">duration,</if>
        </trim>
        on duplicate key update
        author_sec_uid = #{authorSecUid},
        author_nickname = #{authorNickname},
        video_uri = #{videoUri},
        aweme_url = #{awemeUrl},
        digg_count = #{diggCount},
        collect_count = #{collectCount},
        comment_count = #{commentCount},
        share_count = #{shareCount},
        digg_add = ifnull(#{diggAdd}, 0),
        collect_add = ifnull(#{collectAdd}, 0),
        share_add = ifnull(#{shareAdd}, 0),
        comment_add = ifnull(#{commentAdd}, 0),
        duration = #{duration},
        gmt_modified = NOW()
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.dydata.DouyinMonitorKeywordDataEntity">
        update tb_douyin_monitor_keyword_data
        <trim prefix="SET" suffixOverrides=",">
                    <if test="keywordId != null">keyword_id = #{keywordId},</if>
                    <if test="keywordName != null and keywordName != ''">keyword_name = #{keywordName},</if>
                    <if test="awemeId != null and awemeId != ''">aweme_id = #{awemeId},</if>
                    <if test="authorUid != null and authorUid != ''">author_uid = #{authorUid},</if>
                    <if test="authorSecUid != null and authorSecUid != ''">author_sec_uid = #{authorSecUid},</if>
                    <if test="authorNickname != null and authorNickname != ''">author_nickname = #{authorNickname},</if>
                    <if test="awemeDayN != null">aweme_day_n = #{awemeDayN},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="awemeDesc != null">aweme_desc = #{awemeDesc},</if>
                    <if test="anchorTitle != null">anchor_title = #{anchorTitle},</if>
                    <if test="titleTag != null">title_tag = #{titleTag},</if>
                    <if test="videUri != null">video_uri = #{videUri},</if>
                    <if test="awemeUrl != null">aweme_url = #{awemeUrl},</if>
                    <if test="coverUrl != null">cover_url = #{coverUrl},</if>
                    <if test="shareUrl != null">share_url = #{shareUrl},</if>
                    <if test="diggCount != null">digg_count = #{diggCount},</if>
                    <if test="collectCount != null">collect_count = #{collectCount},</if>
                    <if test="shareCount != null">share_count = #{shareCount},</if>
                    <if test="commentCount != null">comment_count = #{commentCount},</if>
                    <if test="diggAdd != null">digg_add = #{diggAdd},</if>
                    <if test="collectAdd != null">collect_add = #{collectAdd},</if>
                    <if test="shareAdd != null">share_add = #{shareAdd},</if>
                    <if test="commentAdd != null">comment_add = #{commentAdd},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="descTxt != null">desc_txt = #{descTxt},</if>
                    <if test="duration != null">duration = #{duration},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
