<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.dyAdData.ShortplayDyAdDataMapper">

    <resultMap type="com.ruoyi.system.entity.dyAdData.ShortplayDyAdDataEntity" id="ShortplayDyAdDataResult">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="appId" column="app_id"/>
        <result property="appName" column="app_name"/>
        <result property="tvId" column="tv_id"/>
        <result property="tvName" column="tv_name"/>
        <result property="seq" column="seq"/>
        <result property="times" column="times"/>
        <result property="exposurePv" column="exposure_pv"/>
        <result property="watchPv" column="watch_pv"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectShortplayDyAdDataVo">
        select id, cur_date, app_id, app_name, tv_id, tv_name, seq, times, exposure_pv, watch_pv, gmt_create,
        gmt_modified from tb_shortplay_dy_ad_data
    </sql>

    <select id="selectShortplayDyAdDataList" parameterType="com.ruoyi.system.entity.dyAdData.ShortplayDyAdDataEntity"
            resultMap="ShortplayDyAdDataResult">
        <include refid="selectShortplayDyAdDataVo"/>
        <where>
            <if test="curDate != null ">and cur_date = #{curDate}</if>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
            <if test="appName != null  and appName != ''">and app_name like concat('%', #{appName}, '%')</if>
            <if test="tvId != null ">and tv_id = #{tvId}</if>
            <if test="tvName != null  and tvName != ''">and tv_name like concat('%', #{tvName}, '%')</if>
            <if test="seq != null ">and seq = #{seq}</if>
            <if test="times != null ">and times = #{times}</if>
            <if test="exposurePv != null ">and exposure_pv = #{exposurePv}</if>
            <if test="watchPv != null ">and watch_pv = #{watchPv}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayDyAdDataById" parameterType="Long" resultMap="ShortplayDyAdDataResult">
        <include refid="selectShortplayDyAdDataVo"/>
        where id = #{id}
    </select>

    <select id="selectIdBy" resultType="java.lang.Long">
        select id from tb_shortplay_dy_ad_data
        where cur_date = #{curDate}
        and app_id = #{appId}
        and tv_id = #{fTvId}
        and seq = #{series}
        and times = #{times}
    </select>

    <insert id="insertShortplayDyAdData" parameterType="com.ruoyi.system.entity.dyAdData.ShortplayDyAdDataEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_dy_ad_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="appName != null and appName != ''">app_name,</if>
            <if test="tvId != null">tv_id,</if>
            <if test="tvName != null and tvName != ''">tv_name,</if>
            <if test="seq != null">seq,</if>
            <if test="times != null">times,</if>
            <if test="exposurePv != null">exposure_pv,</if>
            <if test="watchPv != null">watch_pv,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="appName != null and appName != ''">#{appName},</if>
            <if test="tvId != null">#{tvId},</if>
            <if test="tvName != null and tvName != ''">#{tvName},</if>
            <if test="seq != null">#{seq},</if>
            <if test="times != null">#{times},</if>
            <if test="exposurePv != null">#{exposurePv},</if>
            <if test="watchPv != null">#{watchPv},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>
    <update id="addWatchPv">
        update tb_shortplay_dy_ad_data
        set watch_pv = watch_pv + 1
        where id = #{id}
    </update>

    <update id="addExposurePV">
        update tb_shortplay_dy_ad_data
        set exposure_pv = exposure_pv + 1
        where id = #{id}
    </update>

    <update id="updateShortplayDyAdData" parameterType="com.ruoyi.system.entity.dyAdData.ShortplayDyAdDataEntity">
        update tb_shortplay_dy_ad_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="appName != null and appName != ''">app_name = #{appName},</if>
            <if test="tvId != null">tv_id = #{tvId},</if>
            <if test="tvName != null and tvName != ''">tv_name = #{tvName},</if>
            <if test="seq != null">seq = #{seq},</if>
            <if test="times != null">times = #{times},</if>
            <if test="exposurePv != null">exposure_pv = #{exposurePv},</if>
            <if test="watchPv != null">watch_pv = #{watchPv},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayDyAdDataById" parameterType="Long">
        delete from tb_shortplay_dy_ad_data where id = #{id}
    </delete>

    <delete id="deleteShortplayDyAdDataByIds" parameterType="String">
        delete from tb_shortplay_dy_ad_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
