<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.slotdata.SlotHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.slotdata.SlotHourDataEntity" id="SlotHourDataResult">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="hour" column="hour"/>
        <result property="slotId" column="slot_id"/>
        <result property="configId" column="config_id"/>
        <result property="configItemId" column="config_item_id"/>
        <result property="pv" column="pv"/>
        <result property="uv" column="uv"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectSlotHourDataVo">
        select id, cur_date, hour, slot_id, config_id, config_item_id, pv, uv, gmt_create, gmt_modified
        from tb_slot_hour_data
    </sql>

    <select id="selectSlotHourDataList" parameterType="com.ruoyi.system.req.slot.SlotHourDataReq"
            resultMap="SlotHourDataResult">
        <include refid="selectSlotHourDataVo"/>
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="slotId != null ">and slot_id = #{slotId}</if>
            <if test="hour != null ">and hour = #{hour}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSlotHourDataById" parameterType="Long" resultMap="SlotHourDataResult">
        <include refid="selectSlotHourDataVo"/>
        where id = #{id}
    </select>
    <select id="selectSlotHourDataByDate" resultMap="SlotHourDataResult">
        SELECT cur_date,
               slot_id,
               config_id,
               config_item_id,
               SUM(pv) AS pv,
               SUM(uv) AS uv
        FROM tb_slot_hour_data
        WHERE cur_date = #{date}
        GROUP BY cur_date,
                 slot_id,
                 config_id,
                 config_item_id
    </select>
    <select id="selecDataList" resultType="com.ruoyi.system.entity.slotdata.SlotHourDataEntity"
            resultMap="SlotHourDataResult">
        <include refid="selectSlotHourDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="hour != null "> and hour = #{hour}</if>
            <if test="slotId != null "> and slot_id = #{slotId}</if>
            <if test="configId != null "> and config_id = #{configId}</if>
            <if test="configItemId != null "> and config_item_id = #{configItemId}</if>
            <if test="pv != null "> and pv = #{pv}</if>
            <if test="uv != null "> and uv = #{uv}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <insert id="insertSlotHourData" parameterType="com.ruoyi.system.entity.slotdata.SlotHourDataEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="hour != null">hour,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="configId != null">config_id,</if>
            <if test="configItemId != null">config_item_id,</if>
            <if test="pv != null">pv,</if>
            <if test="uv != null">uv,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="hour != null">#{hour},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="configId != null">#{configId},</if>
            <if test="configItemId != null">#{configItemId},</if>
            <if test="pv != null">#{pv},</if>
            <if test="uv != null">#{uv},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateSlotHourData" parameterType="com.ruoyi.system.entity.slotdata.SlotHourDataEntity">
        update tb_slot_hour_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="hour != null">hour = #{hour},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="configItemId != null">config_item_id = #{configItemId},</if>
            <if test="pv != null">pv = #{pv},</if>
            <if test="uv != null">uv = #{uv},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateSlotHourDataAdd"
            parameterType="com.ruoyi.system.entity.slotdata.SlotDataEntity">
        update tb_slot_hour_data
        set pv = pv + #{pv},
            uv = uv + #{uv}
        where id = #{id}
    </update>

    <delete id="deleteSlotHourDataById" parameterType="Long">
        delete
        from tb_slot_hour_data
        where id = #{id}
    </delete>

    <delete id="deleteSlotHourDataByIds" parameterType="String">
        delete from tb_slot_hour_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>