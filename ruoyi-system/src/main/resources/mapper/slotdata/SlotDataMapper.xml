<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.slotdata.SlotDataMapper">

    <resultMap type="com.ruoyi.system.entity.slotdata.SlotDataEntity" id="SlotDataResult">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="slotId" column="slot_id"/>
        <result property="configId" column="config_id"/>
        <result property="configItemId" column="config_item_id"/>
        <result property="pv" column="pv"/>
        <result property="uv" column="uv"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectSlotDataVo">
        select id,
               cur_date,
               slot_id,
               config_id,
               config_item_id,
               pv,
               uv,
               gmt_create,
               gmt_modified
        from tb_slot_data
    </sql>

    <select id="selectSlotDataList" parameterType="com.ruoyi.system.req.slot.SlotDataReq"
            resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="slotId != null ">and slot_id = #{slotId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSlotDataById" parameterType="Long" resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSlotData" parameterType="com.ruoyi.system.entity.slotdata.SlotDataEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into tb_slot_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="configId != null">config_id,</if>
            <if test="configItemId != null">config_item_id,</if>
            <if test="pv != null">pv,</if>
            <if test="uv != null">uv,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="configId != null">#{configId},</if>
            <if test="configItemId != null">#{configItemId},</if>
            <if test="pv != null">#{pv},</if>
            <if test="uv != null">#{uv},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateSlotData" parameterType="com.ruoyi.system.entity.slotdata.SlotDataEntity">
        update tb_slot_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="configItemId != null">config_item_id = #{configItemId},</if>
            <if test="pv != null">pv = #{pv},</if>
            <if test="uv != null">uv = #{uv},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>
    <insert id="updateSlotDataAdd" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.slotdata.SlotDataEntity">
        INSERT INTO tb_slot_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="slotId != null">slot_id ,</if>
            <if test="configId != null">config_id,</if>
            <if test="configItemId != null">config_item_id,</if>
            <if test="pv != null">pv,</if>
            <if test="uv != null">uv,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="configId != null">#{configId},</if>
            <if test="configItemId != null">#{configItemId},</if>
            <if test="pv != null">#{pv},</if>
            <if test="uv != null">#{uv},</if>
        </trim>
        on duplicate key update
        pv = values(pv) + pv,
        uv = values(uv) + uv
    </insert>
    <insert id="batchInsertOrUpdate">
        insert into
        tb_slot_data(`cur_date`, `slot_id`, `config_id`, `config_item_id`, `pv`, `uv`)
        values
        <foreach collection="list" separator="," item="entity">
            (
            #{entity.curDate},#{entity.slotId},#{entity.configId},#{entity.configItemId},#{entity.pv},#{entity.uv}
            )
        </foreach>
        on duplicate key update
        pv = VALUES(pv),
        uv = VALUES(uv)
    </insert>

    <delete id="deleteSlotDataById" parameterType="Long">
        delete
        from tb_slot_data
        where id = #{id}
    </delete>

    <delete id="deleteSlotDataByIds" parameterType="String">
        delete from tb_slot_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>