<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.video.VideoMapper">

    <resultMap type="com.ruoyi.system.entity.video.VideoEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="videoGroupId" column="video_group_id"/>
        <result property="mediaId" column="media_id"/>
        <result property="title" column="title"/>
        <result property="videoUrl" column="video_url"/>
        <result property="price" column="price"/>
        <result property="duration" column="duration"/>
        <result property="episode" column="episode"/>
        <result property="wxUploadStatus" column="wx_upload_status"/>
        <result property="wxAuditStatus" column="wx_audit_status"/>
        <result property="auditErrorMsg" column="audit_error_msg"/>
        <result property="uploadErrorMsg" column="upload_error_msg"/>
        <result property="tmeStatus" column="tme_status"/>
        <result property="tmeReason" column="tme_reason"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            video_group_id,
            media_id,
            duration,
            title,
            video_url,
            price,
            episode,
            wx_upload_status,
            wx_audit_status,
            audit_error_msg,
            upload_error_msg,
            tme_status,
            tme_reason,
            gmt_create,
            gmt_modified,
            is_deleted
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.video.VideoEntity">
        INSERT INTO tb_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="videoGroupId != null">
                video_group_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="videoUrl != null">
                video_url,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="episode != null">
                episode
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="videoGroupId != null">
                #{videoGroupId},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="videoUrl != null">
                #{videoUrl},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="episode != null">
                #{episode}
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_video(video_group_id,title,video_url,episode,duration)
        values
        <foreach collection="entities" item="item" separator=",">
            (#{item.videoGroupId},#{item.title},#{item.videoUrl},#{item.episode},#{item.duration})
        </foreach>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_video
        WHERE id = #{id}
    </delete>
    <update id="deleteByVideoGroupId">
        update tb_video set is_deleted = 1 where video_group_id = #{id}
    </update>

    <update id="updateById" parameterType="com.ruoyi.system.entity.video.VideoEntity">
        UPDATE tb_video
        <set>
            <if test="videoGroupId != null">
                video_group_id = #{videoGroupId},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="duration != null">
                duration = #{duration},
            </if>
            <if test="videoUrl != null">
                video_url = #{videoUrl},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="mediaId != null">
                media_id = #{mediaId},
            </if>
            <if test="episode != null">
                episode = #{episode},
            </if>
            <if test="wxUploadStatus != null">
                wx_upload_status = #{wxUploadStatus},
            </if>
            <if test="wxAuditStatus != null">
                wx_audit_status = #{wxAuditStatus},
            </if>
            <if test="uploadErrorMsg != null">
                upload_error_msg = #{uploadErrorMsg},
            </if>
            <if test="auditErrorMsg != null">
                audit_error_msg = #{auditErrorMsg},
            </if>
            <if test="tmeStatus != null">
                tme_status = #{tmeStatus},
            </if>
            <if test="tmeReason != null">
                tme_reason = #{tmeReason},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    <update id="batchUpdate">
        <foreach collection="entities" item="item" separator=";">
            UPDATE tb_video
            <set>
                <if test="item.mediaId != null">
                    media_id = #{item.mediaId},
                </if>
                <if test="item.wxUploadStatus != null">
                    wx_upload_status = #{item.wxUploadStatus},
                </if>
                <if test="item.wxAuditStatus != null">
                    wx_audit_status = #{item.wxAuditStatus},
                </if>
                <if test="item.uploadErrorMsg != null">
                    upload_error_msg = #{item.uploadErrorMsg},
                </if>
                <if test="item.auditErrorMsg != null">
                    audit_error_msg = #{item.auditErrorMsg},
                </if>
                <if test="item.tmeStatus != null">
                    tme_status = #{item.tmeStatus},
                </if>
                <if test="item.tmeReason != null">
                    tme_reason = #{item.tmeReason},
                </if>
            </set>
            WHERE id=#{item.id}
        </foreach>

    </update>
    <update id="batchUpdateByMediaId">
        <foreach collection="entities" item="item" separator=";">
            UPDATE tb_video
            <set>
                <if test="item.wxUploadStatus != null">
                    wx_upload_status = #{item.wxUploadStatus},
                </if>
                <if test="item.wxAuditStatus != null">
                    wx_audit_status = #{item.wxAuditStatus},
                </if>
                <if test="item.uploadErrorMsg != null">
                    upload_error_msg = #{item.uploadErrorMsg},
                </if>
                <if test="item.auditErrorMsg != null">
                    audit_error_msg = #{item.auditErrorMsg},
                </if>
            </set>
            WHERE media_id=#{item.mediaId}
        </foreach>
    </update>

    <update id="updateAuditStatusByVideoGroupId" parameterType="com.ruoyi.system.entity.video.VideoEntity">
        update tb_video set dy_audit_status = #{dyAuditStatus} where video_group_id = #{videoGroupId}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video
        WHERE id = #{id}
    </select>

    <select id="selectVideoCount" resultType="com.ruoyi.system.bo.playlet.VideoGroupEpisodeCountBo">
        SELECT video_group_id as videoGroupId ,count(*) as sum
        FROM tb_video
        <where>
            is_deleted = 0
            and video_group_id in
            <foreach collection="videoGroupIds" open="(" item="groupId" close=")" separator=",">
                #{groupId}
            </foreach>
        </where>
        group by video_group_id
    </select>
    <select id="selectListByVideoGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_video
        where video_group_id = #{videoGroupId}
        and is_deleted = 0
        order by episode asc
    </select>
    <select id="selectListByReq" resultMap="BaseResultMap"
            parameterType="com.ruoyi.system.req.playlet.video.VideoSearchReq">
        select
        <include refid="Base_Column_List"/>
        from tb_video
        <where>
            is_deleted = 0
            <if test="videoGroupId != null">
                and video_group_id = #{videoGroupId}
            </if>
            <if test="title != null and title != ''">
                and title like concat('%',#{title},'%')
            </if>
            <if test="episode != null">
                and episode = #{episode}
            </if>
        </where>
        order by gmt_create desc
    </select>
    <select id="selectListByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video
        WHERE id in
        <foreach collection="ids" separator="," open="(" item="id" close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectListByVideoGroupIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_video
        where video_group_id in
        <foreach collection="videoGroupIds" open="(" item="groupId" close=")" separator=",">
            #{groupId}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="selectByMediaId" resultType="com.ruoyi.system.entity.video.VideoEntity">
        select
        <include refid="Base_Column_List"/>
        from tb_video
        where media_id = #{mediaId}
        and is_deleted = 0
    </select>

</mapper>
