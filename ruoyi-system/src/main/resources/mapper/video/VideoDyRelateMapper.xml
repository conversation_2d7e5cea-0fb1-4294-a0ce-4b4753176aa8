<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.video.VideoDyRelateMapper">

    <resultMap type="com.ruoyi.system.entity.video.VideoDyRelateEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="appId" column="app_id"/>
            <result property="videoId" column="video_id"/>
            <result property="videoGroupId" column="video_group_id"/>
            <result property="dyOpenVideoId" column="dy_open_video_id"/>
            <result property="dyUploadStatus" column="dy_upload_status"/>
            <result property="dyAuditStatus" column="dy_audit_status"/>
            <result property="dyUploadErrorMsg" column="dy_upload_error_msg"/>
            <result property="dyAuditErrorMsg" column="dy_audit_error_msg"/>
            <result property="dyEpisodeId" column="dy_episode_id"/>
            <result property="dyCloudId" column="dy_cloud_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            app_id,
            video_id,
            video_group_id,
            dy_open_video_id,
            dy_upload_status,
            dy_audit_status,
            dy_upload_error_msg,
            dy_audit_error_msg,
            dy_episode_id,
            dy_cloud_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.video.VideoDyRelateEntity">
        INSERT INTO tb_video_dy_relate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                app_id,
            </if>
            <if test="videoGroupId != null">
                video_group_id,
            </if>
            <if test="videoId != null">
                video_id,
            </if>
            <if test="dyOpenVideoId != null">
                dy_open_video_id,
            </if>
            <if test="dyUploadStatus != null">
                dy_upload_status,
            </if>
            <if test="dyAuditStatus != null">
                dy_audit_status,
            </if>
            <if test="dyUploadErrorMsg != null">
                dy_upload_error_msg,
            </if>
            <if test="dyAuditErrorMsg != null">
                dy_audit_error_msg,
            </if>
            <if test="dyEpisodeId != null">
                dy_episode_id,
            </if>
            <if test="dyCloudId != null">
                dy_cloud_id,
            </if>
            <if test="gmtModified != null">
                gmt_modified
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                #{appId},
            </if>
            <if test="videoGroupId != null">
                #{videoGroupId},
            </if>
            <if test="videoId != null">
                #{videoId},
            </if>
            <if test="dyOpenVideoId != null">
                #{dyOpenVideoId},
            </if>
            <if test="dyUploadStatus != null">
                #{dyUploadStatus},
            </if>
            <if test="dyAuditStatus != null">
                #{dyAuditStatus},
            </if>
            <if test="dyUploadErrorMsg != null">
                #{dyUploadErrorMsg},
            </if>
            <if test="dyAuditErrorMsg != null">
                #{dyAuditErrorMsg},
            </if>
            <if test="dyEpisodeId != null">
                #{dyEpisodeId},
            </if>
            <if test="dyCloudId != null">
                #{dyCloudId},
            </if>
            <if test="gmtModified != null">
                #{gmtModified}
            </if>
        </trim>
    </insert>
    <insert id="batchInsertOrUpdate">
        insert into
        tb_video_dy_relate(`app_id`,`video_id`,`video_group_id`,`dy_open_video_id`,`dy_upload_status`,`dy_upload_error_msg`)
        values
        <foreach collection="list" separator="," item="entity">
            (#{entity.appId},#{entity.videoId},#{entity.videoGroupId},#{entity.dyOpenVideoId},#{entity.dyUploadStatus},#{entity.dyUploadErrorMsg})
        </foreach>
        ON DUPLICATE KEY UPDATE
        dy_open_video_id = values(dy_open_video_id),
        dy_upload_status = values(dy_upload_status),
        dy_upload_error_msg = values(dy_upload_error_msg),
        gmt_modified = now()
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_video_dy_relate WHERE id=#{id}
    </delete>
    <delete id="deleteByVideoId">
        DELETE FROM tb_video_dy_relate WHERE video_id=#{videoId}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.video.VideoDyRelateEntity">
        UPDATE tb_video_dy_relate
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="videoId != null">
                video_id = #{videoId},
            </if>
            <if test="dyOpenVideoId != null">
                dy_open_video_id = #{dyOpenVideoId},
            </if>
            <if test="dyUploadStatus != null">
                dy_upload_status = #{dyUploadStatus},
            </if>
            <if test="dyAuditStatus != null">
                dy_audit_status = #{dyAuditStatus},
            </if>
            <if test="dyUploadErrorMsg != null">
                dy_upload_error_msg = #{dyUploadErrorMsg},
            </if>
            <if test="dyAuditErrorMsg != null">
                dy_audit_error_msg = #{dyAuditErrorMsg},
            </if>
            <if test="dyEpisodeId != null">
                dy_episode_id = #{dyEpisodeId},
            </if>
            <if test="dyCloudId != null">
                dy_cloud_id = #{dyCloudId},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    <update id="batchUpdate">
        <foreach collection="list" separator=";" item="item">
            UPDATE tb_video_dy_relate
            <set>
                <if test="item.dyOpenVideoId != null">
                    dy_open_video_id = #{item.dyOpenVideoId},
                </if>
                <if test="item.dyUploadStatus != null">
                    dy_upload_status = #{item.dyUploadStatus},
                </if>
                <if test="item.dyAuditStatus != null">
                    dy_audit_status = #{item.dyAuditStatus},
                </if>
                <if test="item.dyUploadErrorMsg != null">
                    dy_upload_error_msg = #{item.dyUploadErrorMsg},
                </if>
                <if test="item.dyAuditErrorMsg != null">
                    dy_audit_error_msg = #{item.dyAuditErrorMsg},
                </if>
                <if test="item.dyEpisodeId != null">
                    dy_episode_id = #{item.dyEpisodeId},
                </if>
                <if test="item.dyCloudId != null">
                    dy_cloud_id = #{item.dyCloudId},
                </if>
            </set>
            WHERE video_id=#{item.videoId} and app_id=#{item.appId}
        </foreach>
    </update>
    <update id="updateDyAuditStatus">
        UPDATE tb_video_dy_relate
        SET
        dy_audit_status = #{auditStatus}
        WHERE video_group_id=#{videoGroupId} and app_id=#{appId}
    </update>
    <update id="updateByDyOpenId" parameterType="com.ruoyi.system.entity.video.VideoDyRelateEntity">
        update tb_video_dy_relate
        set
            dy_upload_status = #{dyUploadStatus},
            dy_cloud_id = #{dyCloudId},
            gmt_modified = now()
        where dy_open_video_id = #{dyOpenVideoId}
    </update>
    <update id="batchUpdateByOpenVideoId">
        <foreach collection="entities" item="item" separator=";">
            UPDATE tb_video_dy_relate
            <set>
                <if test="item.dyAuditStatus != null">
                    dy_audit_status = #{item.dyAuditStatus},
                </if>
                <if test="item.dyAuditErrorMsg != null">
                    dy_audit_error_msg = #{item.dyAuditErrorMsg},
                </if>
                <if test="item.dyUploadStatus != null">
                    dy_upload_status = #{item.dyUploadStatus},
                </if>
                <if test="item.dyUploadErrorMsg != null">
                    dy_upload_error_msg = #{item.dyUploadErrorMsg},
                </if>
                <if test="item.dyCloudId != null">
                    dy_cloud_id = #{item.dyCloudId},
                </if>
            </set>
            WHERE dy_open_video_id=#{item.dyOpenVideoId}
        </foreach>
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video_dy_relate
        WHERE id = #{id}
    </select>
    <select id="selectListByVideoGroupIdAndAppId"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from tb_video_dy_relate
        where video_group_id = #{videoGroupId}
        and app_id in
        <foreach collection="appIds" open="(" close=")" item="appId" separator=",">
           #{appId}
        </foreach>

    </select>
    <select id="selectListByVideoIds"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from tb_video_dy_relate
        where video_id in
        <foreach collection="videoIds" open="(" close=")" item="videoId" separator=",">
           #{videoId}
        </foreach>
    </select>
    <select id="selectListByDyUploadStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_video_dy_relate
        <where>
            dy_upload_status = #{status}
        </where>
    </select>
    <select id="selectByAppIdAndVideoId"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from tb_video_dy_relate
        where app_id = #{appId}
        and video_id = #{videoId}
    </select>

    <update id="updateByVideoId" parameterType="com.ruoyi.system.entity.video.VideoDyRelateEntity">
        UPDATE tb_video_dy_relate
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="dyOpenVideoId != null">
                dy_open_video_id = #{dyOpenVideoId},
            </if>
            <if test="dyUploadStatus != null">
                dy_upload_status = #{dyUploadStatus},
            </if>
            <if test="dyAuditStatus != null">
                dy_audit_status = #{dyAuditStatus},
            </if>
            <if test="dyUploadErrorMsg != null">
                dy_upload_error_msg = #{dyUploadErrorMsg},
            </if>
            <if test="dyAuditErrorMsg != null">
                dy_audit_error_msg = #{dyAuditErrorMsg},
            </if>
            <if test="dyEpisodeId != null">
                dy_episode_id = #{dyEpisodeId},
            </if>
            <if test="dyCloudId != null">
                dy_cloud_id = #{dyCloudId},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
        </set>
        WHERE video_id=#{videoId}
    </update>
</mapper>