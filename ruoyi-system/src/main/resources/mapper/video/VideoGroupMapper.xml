<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.video.VideoGroupMapper">

    <resultMap type="com.ruoyi.system.entity.video.VideoGroupEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="dramaId" column="drama_id"/>
        <result property="sspAccountId" column="ssp_account_id"/>
        <result property="img" column="img"/>
        <result property="gifImg" column="gif_img"/>
        <result property="landscapeImg" column="landscape_img"/>
        <result property="status" column="status"/>
        <result property="classifyId" column="classify_id"/>
        <result property="ishot" column="ishot"/>
        <result property="give" column="give"/>
        <result property="forward" column="forward"/>
        <result property="views" column="views"/>
        <result property="episodeSum" column="episode_sum"/>
        <result property="updateEpisode" column="update_episode"/>
        <result property="startChargeEpisode" column="start_charge_episode"/>
        <result property="story" column="story"/>
        <result property="price" column="price"/>
        <result property="wxAuditStatus" column="wx_audit_status"/>
        <result property="auditErrorMsg" column="audit_error_msg"/>
        <result property="producer" column="producer"/>
        <result property="material" column="material"/>
        <result property="registrationNumber" column="registration_number"/>
        <result property="publishLicense" column="publish_license"/>
        <result property="publishLicenseImg" column="publish_license_img"/>
        <result property="publishLicenseImg" column="publish_license_img"/>
        <result property="platform" column="platform"/>
        <result property="operator" column="operator"/>
        <result property="actorList" column="actor_list"/>
        <result property="sort" column="sort"/>
        <result property="recommend" column="recommend"/>
        <result property="duration" column="duration"/>
        <result property="directors" column="directors"/>
        <result property="producers" column="producers"/>
        <result property="productionCost" column="production_cost"/>
        <result property="costDistributionUri" column="cost_distribution_uri"/>
        <result property="costCommitmentLetterMaterialUri" column="cost_commitment_letter_material_uri"/>
        <result property="publishTime" column="publish_time"/>
        <result property="tags" column="tags"/>
        <result property="tmeCategory" column="tme_category"/>
        <result property="tmeStatus" column="tme_status"/>
        <result property="tmeReason" column="tme_reason"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            title,
            drama_id,
            ssp_account_id,
            img,
            gif_img,
            landscape_img,
            `status`,
            classify_id,
            ishot,
            give,
            forward,
            views,
            episode_sum,
            update_episode,
            start_charge_episode,
            story,
            price,
            wx_audit_status,
            audit_error_msg,
            producer,
            publish_license,
            publish_license_img,
            registration_number,
            material,
            platform,
            operator,
            actor_list,
            sort,
            recommend,
            duration,
            directors,
            producers,
            production_cost,
            cost_distribution_uri,
            cost_commitment_letter_material_uri,
            publish_time,
            tags,
            tme_category,
            tme_status,
            tme_reason,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.video.VideoGroupEntity">
        INSERT INTO tb_video_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">
                title,
            </if>
            <if test="sspAccountId != null">
                ssp_account_id,
            </if>
            <if test="img != null">
                img,
            </if>
            <if test="gifImg != null">
                gif_img,
            </if>
            <if test="landscapeImg != null">
                landscape_img,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="classifyId != null">
                classify_id,
            </if>
            <if test="ishot != null">
                ishot,
            </if>
            <if test="give != null">
                give,
            </if>
            <if test="forward != null">
                forward,
            </if>
            <if test="views != null">
                views,
            </if>
            <if test="episodeSum != null">
                episode_sum,
            </if>
            <if test="startChargeEpisode != null">
                start_charge_episode,
            </if>
            <if test="story != null">
                story,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="producer != null">
                producer,
            </if>
            <if test="material != null">
                material,
            </if>
            <if test="registrationNumber != null">
                registration_number,
            </if>
            <if test="publishLicense != null">
                publish_license,
            </if>
            <if test="publishLicenseImg != null">
                publish_license_img,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="actorList != null">
                actor_list,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="recommend != null">
                recommend,
            </if>
            <if test="duration != null">
                duration,
            </if>
            <if test="directors != null">
                directors,
            </if>
            <if test="producers != null">
                producers,
            </if>
            <if test="productionCost != null">
                production_cost,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="costDistributionUri != null">
                cost_distribution_uri,
            </if>
            <if test="costCommitmentLetterMaterialUri != null">
                cost_commitment_letter_material_uri,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="tags != null">
                tags,
            </if>
            <if test="tmeCategory != null">
                tme_category,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">
                #{title},
            </if>
            <if test="sspAccountId != null">
                #{sspAccountId},
            </if>
            <if test="img != null">
                #{img},
            </if>
            <if test="gifImg != null">
                #{gifImg},
            </if>
            <if test="landscapeImg != null">
                #{landscapeImg},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="classifyId != null">
                #{classifyId},
            </if>
            <if test="ishot != null">
                #{ishot},
            </if>
            <if test="give != null">
                #{give},
            </if>
            <if test="forward != null">
                #{forward},
            </if>
            <if test="views != null">
                #{views},
            </if>
            <if test="episodeSum != null">
                #{episodeSum},
            </if>
            <if test="startChargeEpisode != null">
                #{startChargeEpisode},
            </if>
            <if test="story != null">
                #{story},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
            <if test="producer != null">
                #{producer},
            </if>
            <if test="material != null">
                #{material},
            </if>
            <if test="registrationNumber != null">
                #{registrationNumber},
            </if>
            <if test="publishLicense != null">
                #{publishLicense},
            </if>
            <if test="publishLicenseImg != null">
                #{publishLicenseImg},
            </if>
            <if test="platform != null">
                #{platform},
            </if>
            <if test="actorList != null">
                #{actorList},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="recommend != null">
                #{recommend},
            </if>
            <if test="duration != null">
                #{duration},
            </if>
            <if test="directors != null">
                #{directors},
            </if>
            <if test="producers != null">
                #{producers},
            </if>
            <if test="productionCost != null">
                #{productionCost},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="costDistributionUri != null">
                #{costDistributionUri},
            </if>
            <if test="costCommitmentLetterMaterialUri != null">
                #{costCommitmentLetterMaterialUri},
            </if>
            <if test="publishTime != null">
                #{publishTime},
            </if>
            <if test="tags != null">
                #{tags},
            </if>
            <if test="tmeCategory != null">
                #{tmeCategory},
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        update tb_video_group set is_deleted = 1 where id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.video.VideoGroupEntity">
        UPDATE tb_video_group
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="dramaId != null">
                drama_id = #{dramaId},
            </if>
            <if test="sspAccountId != null">
                ssp_account_id = #{sspAccountId},
            </if>
            <if test="img != null">
                img = #{img},
            </if>
            <if test="gifImg!=null">
                gif_img = #{gifImg},
            </if>
            <if test="landscapeImg != null">
                landscape_img = #{landscapeImg},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="classifyId != null">
                classify_id = #{classifyId},
            </if>
            <if test="ishot != null">
                ishot = #{ishot},
            </if>
            <if test="give != null">
                give = #{give},
            </if>
            <if test="forward != null">
                forward = #{forward},
            </if>
            <if test="views != null">
                views = #{views},
            </if>
            <if test="episodeSum != null">
                episode_sum = #{episodeSum},
            </if>
            <if test="updateEpisode != null">
                update_episode = #{updateEpisode},
            </if>
            <if test="startChargeEpisode != null">
                start_charge_episode = #{startChargeEpisode},
            </if>
            <if test="story != null">
                story = #{story},
            </if>
            <if test="producer != null">
                producer = #{producer},
            </if>
            <if test="publishLicense != null">
                publish_license = #{publishLicense},
            </if>
            <if test="publishLicenseImg != null">
                publish_license_img = #{publishLicenseImg},
            </if>
            <if test="registrationNumber != null">
                registration_number = #{registrationNumber},
            </if>
            <if test="material != null">
                material = #{material},
            </if>
            <if test="wxAuditStatus != null">
                wx_audit_status = #{wxAuditStatus},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="platform != null">
                platform = #{platform},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
            <if test="actorList != null">
                actor_list = #{actorList},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="recommend != null">
                recommend = #{recommend},
            </if>
            <if test="duration != null">
                duration = #{duration},
            </if>
            <if test="directors != null">
                directors = #{directors},
            </if>
            <if test="producers != null">
                producers = #{producers},
            </if>
            <if test="productionCost != null">
                production_cost = #{productionCost},
            </if>
            <if test="costDistributionUri != null">
                cost_distribution_uri=#{costDistributionUri},
            </if>
            <if test="costCommitmentLetterMaterialUri != null">
                cost_commitment_letter_material_uri= #{costCommitmentLetterMaterialUri},
            </if>
            <if test="publishTime != null">
                publish_time = #{publishTime},
            </if>
            <if test="tags != null">
                tags = #{tags},
            </if>
            <if test="tmeCategory != null">
                tme_category = #{tmeCategory},
            </if>
            <if test="tmeStatus != null">
                tme_status = #{tmeStatus},
            </if>
            <if test="tmeReason != null">
                tme_reason = #{tmeReason},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    <update id="batchUpdate">
        <foreach collection="entities" item="item" separator=";">
            update tb_video_group
            <set>
                <if test="item.wxAuditStatus != null">
                    wx_audit_status = #{item.wxAuditStatus},
                </if>
                <if test="item.auditErrorMsg != null">
                    audit_error_msg = #{item.auditErrorMsg},
                </if>

            </set>
            where id = #{item.id}
        </foreach>
    </update>
    <update id="batchPublish">
        update tb_video_group set status = #{status} where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video_group
        WHERE id = #{id}
    </select>

    <select id="selectListByReq" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video_group
        <where>
            is_deleted = 0
            <if test="req.id != null">
                AND id = #{req.id}
            </if>
            <if test="req.title != null and req.title != ''">
                and title like concat('%',#{req.title},'%')
            </if>
            <if test="req.sspAccountId != null">
                AND ssp_account_id = #{req.sspAccountId}
            </if>
            <if test="req.status != null">
                and status = #{req.status}
            </if>
            <if test="req.wxAuditStatus != null">
                and wx_audit_status = #{req.wxAuditStatus}
            </if>
        </where>
        <choose>
            <when test="req.sortField != null and req.sortType != null and req.sortField != '' and req.sortType != ''">
                order by ${req.sortField} ${req.sortType}
            </when>
            <otherwise>
                order by gmt_create desc
            </otherwise>
        </choose>

    </select>
    <select id="selectListByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video_group
        <where>
            is_deleted = 0
            AND id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        order by gmt_create desc
    </select>
    <select id="selectAllAuditTask" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from tb_video_group
            where is_deleted = 0 and wx_audit_status = #{status}
    </select>

    <select id="selectByDramaId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_video_group where drama_id = #{dramaId} limit 1
    </select>
</mapper>
