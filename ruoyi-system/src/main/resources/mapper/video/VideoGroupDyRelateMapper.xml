<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.video.VideoGroupDyRelateMapper">

    <resultMap type="com.ruoyi.system.entity.video.VideoGroupDyRelateEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="appId" column="app_id"/>
            <result property="videoGroupId" column="video_group_id"/>
            <result property="dyAlbumId" column="dy_album_id"/>
            <result property="dyVersion" column="dy_version"/>
            <result property="dyAuditStatus" column="dy_audit_status"/>
            <result property="dyAuditErrorMsg" column="dy_audit_error_msg"/>
            <result property="dyOnlineStatus" column="dy_online_status"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            app_id,
            video_group_id,
            dy_album_id,
            dy_version,
            dy_audit_status,
            dy_online_status,
            dy_audit_error_msg,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.video.VideoGroupDyRelateEntity">
        INSERT INTO tb_video_group_dy_relate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                app_id,
            </if>
            <if test="videoGroupId != null">
                video_group_id,
            </if>
            <if test="dyAlbumId != null">
                dy_album_id,
            </if>
            <if test="dyVersion != null">
                dy_version,
            </if>
            <if test="dyAuditStatus != null">
                dy_audit_status,
            </if>
            <if test="dyAuditErrorMsg != null">
                dy_audit_error_msg,
            </if>
            <if test="gmtModified != null">
                gmt_modified
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                #{appId},
            </if>
            <if test="videoGroupId != null">
                #{videoGroupId},
            </if>
            <if test="dyAlbumId != null">
                #{dyAlbumId},
            </if>
            <if test="dyVersion != null">
                #{dyVersion},
            </if>
            <if test="dyAuditStatus != null">
                #{dyAuditStatus},
            </if>
            <if test="dyAuditErrorMsg != null">
                #{dyAuditErrorMsg},
            </if>
            <if test="gmtModified != null">
                #{gmtModified}
            </if>
        </trim>
    </insert>
    <insert id="batchInsertOrUpdate">
        insert into tb_video_group_dy_relate(`app_id`,`video_group_id`,`dy_album_id`)
        values
        <foreach collection="list" separator="," item="entity">
            (#{entity.appId},#{entity.videoGroupId},#{entity.dyAlbumId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        gmt_modified = now()
    </insert>

    <select id="selectDyAllAuditTask" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_video_group_dy_relate
        where dy_audit_status = #{status}
    </select>

    <delete id="deleteById">
        DELETE FROM tb_video_group_dy_relate WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.video.VideoGroupDyRelateEntity">
        UPDATE tb_video_group_dy_relate
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="videoGroupId != null">
                video_group_id = #{videoGroupId},
            </if>
            <if test="dyAlbumId != null">
                dy_album_id = #{dyAlbumId},
            </if>
            <if test="dyVersion != null">
                dy_version = #{dyVersion},
            </if>
            <if test="dyAuditStatus != null">
                dy_audit_status = #{dyAuditStatus},
            </if>
            <if test="dyAuditErrorMsg != null">
                dy_audit_error_msg = #{dyAuditErrorMsg},
            </if>
            <if test="dyOnlineStatus != null">
                dy_online_status = #{dyOnlineStatus},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video_group_dy_relate
        WHERE id = #{id}
    </select>
    <select id="selectListByGroupIdAndAppIds"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from tb_video_group_dy_relate
        where video_group_id = #{videoGroupId}
        and app_id in
        <foreach collection="appIds" close=")" open="(" item="appId" separator=",">
            #{appId}
        </foreach>
    </select>
    <select id="selectByGroupIdAndAppId"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from tb_video_group_dy_relate
        where video_group_id = #{videoGroupId}
        and app_id = #{appId}
    </select>
    <select id="selectListByGroupIds"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from tb_video_group_dy_relate
        where video_group_id in
        <foreach collection="videoGroupIds" close=")" open="(" item="videoGroupId" separator=",">
            #{videoGroupId}
        </foreach>
    </select>
    <select id="selectByDyAlbumId"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_video_group_dy_relate
        where dy_album_id = #{albumId}
        limit 1
    </select>

</mapper>