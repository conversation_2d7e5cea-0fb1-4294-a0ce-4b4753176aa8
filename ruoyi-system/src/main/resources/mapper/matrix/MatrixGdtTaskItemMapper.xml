<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.matrix.MatrixGdtTaskItemMapper">

    <resultMap type="com.ruoyi.system.entity.matrix.MatrixGdtTaskItemEntity" id="MatrixGdtTaskItemResult">
            <result property="id"    column="id"    />
            <result property="taskId"    column="task_id"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="advertisementName"    column="advertisement_name"    />
            <result property="status"    column="status"    />
            <result property="reason"    column="reason"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectMatrixGdtTaskItemVo">
        select id, task_id, advertisement_id, advertisement_name, status, reason, gmt_create, gmt_modified from tb_matrix_gdt_task_item
    </sql>

    <select id="selectMatrixGdtTaskItemList" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtTaskItemEntity" resultMap="MatrixGdtTaskItemResult">
        <include refid="selectMatrixGdtTaskItemVo"/>
        <where>
                        <if test="taskId != null "> and task_id = #{taskId}</if>
                        <if test="advertisementId != null "> and advertisement_id = #{advertisementId}</if>
                        <if test="advertisementName != null  and advertisementName != ''"> and advertisement_name like concat('%', #{advertisementName}, '%')</if>
                        <if test="status != null "> and status = #{status}</if>
                        <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectMatrixGdtTaskItemById" parameterType="Long" resultMap="MatrixGdtTaskItemResult">
            <include refid="selectMatrixGdtTaskItemVo"/>
            where id = #{id}
    </select>
    <select id="selectListByTaskIds" resultMap="MatrixGdtTaskItemResult">
        <include refid="selectMatrixGdtTaskItemVo"/>
        where task_id in
            <foreach collection="taskIds" separator="," close=")" item="item" open="(" >
                #{item}
            </foreach>
    </select>

    <insert id="insertMatrixGdtTaskItem" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtTaskItemEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_matrix_gdt_task_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="taskId != null">task_id,</if>
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name,</if>
                    <if test="status != null">status,</if>
                    <if test="reason != null and reason != ''">reason,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="taskId != null">#{taskId},</if>
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">#{advertisementName},</if>
                    <if test="status != null">#{status},</if>
                    <if test="reason != null and reason != ''">#{reason},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into tb_matrix_gdt_task_item(`task_id`,`advertisement_id`,`advertisement_name`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.taskId},#{item.advertisementId},#{item.advertisementName})
        </foreach>
    </insert>

    <update id="updateMatrixGdtTaskItem" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtTaskItemEntity">
        update tb_matrix_gdt_task_item
        <trim prefix="SET" suffixOverrides=",">
                    <if test="taskId != null">task_id = #{taskId},</if>
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name = #{advertisementName},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="reason != null and reason != ''">reason = #{reason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMatrixGdtTaskItemById" parameterType="Long">
        delete from tb_matrix_gdt_task_item where id = #{id}
    </delete>

    <delete id="deleteMatrixGdtTaskItemByIds" parameterType="String">
        delete from tb_matrix_gdt_task_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>