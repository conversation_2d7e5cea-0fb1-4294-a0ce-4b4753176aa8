<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.matrix.MatrixGdtMaterialRelateMapper">

    <resultMap type="com.ruoyi.system.entity.matrix.MatrixGdtMaterialRelateEntity" id="MatrixGdtMaterialRelateResult">
            <result property="id"    column="id"    />
            <result property="materialId"    column="material_id"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="gdtMaterialId"    column="gdt_material_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectMatrixGdtMaterialRelateVo">
        select id, material_id, advertisement_id, gdt_material_id, gmt_create, gmt_modified from tb_matrix_gdt_material_relate
    </sql>

    <select id="selectMatrixGdtMaterialRelateList" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtMaterialRelateEntity" resultMap="MatrixGdtMaterialRelateResult">
        <include refid="selectMatrixGdtMaterialRelateVo"/>
        <where>
                        <if test="materialId != null "> and material_id = #{materialId}</if>
                        <if test="advertisementId != null "> and advertisement_id = #{advertisementId}</if>
                        <if test="gdtMaterialId != null  and gdtMaterialId != ''"> and gdt_material_id = #{gdtMaterialId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectMatrixGdtMaterialRelateById" parameterType="Long" resultMap="MatrixGdtMaterialRelateResult">
            <include refid="selectMatrixGdtMaterialRelateVo"/>
            where id = #{id}
    </select>
    <select id="selectByAdvertisementIdAndImageIds"
            resultMap="MatrixGdtMaterialRelateResult">
        <include refid="selectMatrixGdtMaterialRelateVo"/>
        where advertisement_id = #{advertisementId}
        and gdt_material_id in
        <foreach collection="imageIds" item="imageId" open="(" close=")" separator=",">
            #{imageId}
        </foreach>
    </select>
    <select id="selectByAdvertisementIdsAndImageIds"
            resultMap="MatrixGdtMaterialRelateResult">
        <include refid="selectMatrixGdtMaterialRelateVo"/>
        where advertisement_id in
        <foreach collection="advertisementIds" item="advertisementId" open="(" close=")" separator=",">
            #{advertisementId}
        </foreach>
        and material_id in
        <foreach collection="imageIds" item="materialId" open="(" close=")" separator=",">
            #{materialId}
        </foreach>
    </select>

    <insert id="insertMatrixGdtMaterialRelate" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtMaterialRelateEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_matrix_gdt_material_relate
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="materialId != null">material_id,</if>
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="gdtMaterialId != null and gdtMaterialId != ''">gdt_material_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="materialId != null">#{materialId},</if>
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="gdtMaterialId != null and gdtMaterialId != ''">#{gdtMaterialId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into tb_matrix_gdt_material_relate(`material_id`, `advertisement_id`, `gdt_material_id`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.materialId},#{item.advertisementId},#{item.gdtMaterialId})
        </foreach>
    </insert>

    <update id="updateMatrixGdtMaterialRelate" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtMaterialRelateEntity">
        update tb_matrix_gdt_material_relate
        <trim prefix="SET" suffixOverrides=",">
                    <if test="materialId != null">material_id = #{materialId},</if>
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="gdtMaterialId != null and gdtMaterialId != ''">gdt_material_id = #{gdtMaterialId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMatrixGdtMaterialRelateById" parameterType="Long">
        delete from tb_matrix_gdt_material_relate where id = #{id}
    </delete>

    <delete id="deleteMatrixGdtMaterialRelateByIds" parameterType="String">
        delete from tb_matrix_gdt_material_relate where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>