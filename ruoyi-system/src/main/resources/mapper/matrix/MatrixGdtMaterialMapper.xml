<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.matrix.MatrixGdtMaterialMapper">

    <resultMap type="com.ruoyi.system.entity.matrix.MatrixGdtMaterialEntity" id="MatrixGdtMaterialResult">
            <result property="id"    column="id"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="gdtMaterialId"    column="gdt_material_id"    />
            <result property="materialMd5"    column="material_md5"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectMatrixGdtMaterialVo">
        select id, advertisement_id, gdt_material_id,material_md5, gmt_create, gmt_modified from tb_matrix_gdt_material
    </sql>

    <select id="selectMatrixGdtMaterialList" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtMaterialEntity" resultMap="MatrixGdtMaterialResult">
        <include refid="selectMatrixGdtMaterialVo"/>
        <where>
                        <if test="advertisementId != null "> and advertisement_id = #{advertisementId}</if>
                        <if test="gdtMaterialId != null  and gdtMaterialId != ''"> and gdt_material_id = #{gdtMaterialId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectMatrixGdtMaterialById" parameterType="Long" resultMap="MatrixGdtMaterialResult">
            <include refid="selectMatrixGdtMaterialVo"/>
            where id = #{id}
    </select>
    <select id="selectByAdvertisementIdAndImageIds"
            resultMap="MatrixGdtMaterialResult">
        <include refid="selectMatrixGdtMaterialVo"/>
        where advertisement_id = #{advertisementId}
        and gdt_material_id in
        <foreach item="imageId" collection="imageIds" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </select>

    <insert id="insertMatrixGdtMaterial" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtMaterialEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_matrix_gdt_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="gdtMaterialId != null and gdtMaterialId != ''">gdt_material_id,</if>
                    <if test="materialMd5 != null and materialMd5 != ''">material_md5,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="gdtMaterialId != null and gdtMaterialId != ''">#{gdtMaterialId},</if>
                    <if test="materialMd5 != null and materialMd5 != ''">#{materialMd5},</if>
        </trim>
    </insert>

    <update id="updateMatrixGdtMaterial" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtMaterialEntity">
        update tb_matrix_gdt_material
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="gdtMaterialId != null and gdtMaterialId != ''">gdt_material_id = #{gdtMaterialId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMatrixGdtMaterialById" parameterType="Long">
        delete from tb_matrix_gdt_material where id = #{id}
    </delete>

    <delete id="deleteMatrixGdtMaterialByIds" parameterType="String">
        delete from tb_matrix_gdt_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>