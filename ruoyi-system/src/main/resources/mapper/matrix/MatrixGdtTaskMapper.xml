<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.matrix.MatrixGdtTaskMapper">

    <resultMap type="com.ruoyi.system.entity.matrix.MatrixGdtTaskEntity" id="MatrixGdtTaskResult">
            <result property="id"    column="id"    />
            <result property="accountId"    column="account_id"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="advertisementName"    column="advertisement_name"    />
            <result property="adgroupId"    column="adgroup_id"    />
            <result property="adgroupName"    column="adgroup_name"    />
            <result property="taskInfo"    column="task_info"    />
            <result property="status"    column="status"    />
            <result property="reason"    column="reason"    />
            <result property="operator"    column="operator"    />
            <result property="endTime"    column="end_time"    />
            <result property="startTime"    column="start_time"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectMatrixGdtTaskVo">
        select id, account_id, advertisement_id, operator,advertisement_name,end_time,start_time, adgroup_id, adgroup_name, task_info, status, reason, gmt_create, gmt_modified from tb_matrix_gdt_task
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtTaskEntity" resultMap="MatrixGdtTaskResult">
        <include refid="selectMatrixGdtTaskVo"/>
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="advertisementId != null "> and advertisement_id = #{advertisementId}</if>
            <if test="advertisementName != null  and advertisementName != ''"> and advertisement_name like concat('%', #{advertisementName}, '%')</if>
            <if test="adgroupId != null "> and adgroup_id = #{adgroupId}</if>
            <if test="adgroupName != null  and adgroupName != ''"> and adgroup_name like concat('%', #{adgroupName}, '%')</if>
            <if test="taskInfo != null  and taskInfo != ''"> and task_info = #{taskInfo}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="operator != null  and reason != ''"> and operator = #{operator}</if>
        </where>
        order by id desc
    </select>

    <select id="selectMatrixGdtTaskById" parameterType="Long" resultMap="MatrixGdtTaskResult">
            <include refid="selectMatrixGdtTaskVo"/>
            where id = #{id}
    </select>
    <select id="selectListByStatus" resultType="com.ruoyi.system.entity.matrix.MatrixGdtTaskEntity">
        <include refid="selectMatrixGdtTaskVo"/>
        <where>
            <if test="status != null "> status = #{status}</if>
        </where>
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtTaskEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_matrix_gdt_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">account_id,</if>
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name,</if>
                    <if test="adgroupId != null">adgroup_id,</if>
                    <if test="adgroupName != null and adgroupName != ''">adgroup_name,</if>
                    <if test="taskInfo != null">task_info,</if>
                    <if test="status != null">status,</if>
                    <if test="reason != null and reason != ''">reason,</if>
                    <if test="operator != null and operator != ''">operator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">#{accountId},</if>
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">#{advertisementName},</if>
                    <if test="adgroupId != null">#{adgroupId},</if>
                    <if test="adgroupName != null and adgroupName != ''">#{adgroupName},</if>
                    <if test="taskInfo != null">#{taskInfo},</if>
                    <if test="status != null">#{status},</if>
                    <if test="reason != null and reason != ''">#{reason},</if>
                    <if test="operator != null and operator != ''">#{operator},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.matrix.MatrixGdtTaskEntity">
        update tb_matrix_gdt_task
        <trim prefix="SET" suffixOverrides=",">
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name = #{advertisementName},</if>
                    <if test="adgroupId != null">adgroup_id = #{adgroupId},</if>
                    <if test="adgroupName != null and adgroupName != ''">adgroup_name = #{adgroupName},</if>
                    <if test="taskInfo != null">task_info = #{taskInfo},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="reason != null and reason != ''">reason = #{reason},</if>
                    <if test="startTime != null">start_time = #{startTime},</if>
                    <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>
