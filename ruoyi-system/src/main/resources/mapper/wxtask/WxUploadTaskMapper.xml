<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.wxtask.WxUploadTaskMapper">

    <resultMap type="com.ruoyi.system.entity.wxtask.WxUploadTaskEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="taskId" column="task_id"/>
            <result property="videoId" column="video_id"/>
            <result property="isDeleted" column="is_deleted"/>
        <result property="appId" column="app_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            task_id,
            video_id,
            is_deleted,
            app_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.wxtask.WxUploadTaskEntity">
        INSERT INTO tb_wx_upload_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="videoId != null">
                video_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="appId != null and appId != ''">
                app_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="videoId != null">
                #{videoId},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="appId != null and appId != ''">
                #{appId},
            </if>
        </trim>
        on duplicate key update
        task_id = values(task_id),
        is_deleted = 0
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_wx_upload_task WHERE id=#{id}
    </delete>
    <delete id="deleteByIds">
        update tb_wx_upload_task set is_deleted = 1 where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.wxtask.WxUploadTaskEntity">
        UPDATE tb_wx_upload_task
        <set>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="videoId != null">
                video_id = #{videoId},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_wx_upload_task
        WHERE id = #{id}
    </select>
    <select id="selectUnSuccessTaskListByAppId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_wx_upload_task
        WHERE is_deleted = 0 and app_id = #{appId}
    </select>

</mapper>
