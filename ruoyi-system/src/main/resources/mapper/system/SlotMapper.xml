<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.system.SlotMapper">

    <resultMap type="com.ruoyi.system.entity.system.SlotEntity" id="SlotResult">
            <result property="id"    column="id"    />
            <result property="slotKey"    column="slot_key"    />
            <result property="slotName"    column="slot_name"    />
            <result property="status"    column="status"    />
            <result property="redirectValue"    column="redirect_value"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotVo">
        select id, slot_key, slot_name, status, redirect_value, gmt_create, gmt_modified from tb_slot
    </sql>

    <select id="selectSlotList" parameterType="com.ruoyi.system.entity.system.SlotEntity" resultMap="SlotResult">
        select id, slot_key, slot_name, status, gmt_create, gmt_modified from tb_slot
        <where>
            <if test="slotKey != null  and slotKey != ''"> and slot_key = #{slotKey}</if>
            <if test="slotName != null  and slotName != ''"> and slot_name like concat('%', #{slotName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="redirectValue != null  and redirectValue != ''"> and redirect_value = #{redirectValue}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSlotById" parameterType="Long" resultMap="SlotResult">
            <include refid="selectSlotVo"/>
            where id = #{id}
    </select>

    <select id="selectByKey" parameterType="String" resultMap="SlotResult">
        <include refid="selectSlotVo"/>
        where slot_key = #{key}
    </select>

    <insert id="insertSlot" parameterType="com.ruoyi.system.entity.system.SlotEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="slotKey != null and slotKey != ''">slot_key,</if>
                    <if test="slotName != null and slotName != ''">slot_name,</if>
                    <if test="redirectValue != null">redirect_value,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="slotKey != null and slotKey != ''">#{slotKey},</if>
                    <if test="slotName != null and slotName != ''">#{slotName},</if>
                    <if test="redirectValue != null">#{redirectValue},</if>
        </trim>
    </insert>

    <update id="updateSlot" parameterType="com.ruoyi.system.entity.system.SlotEntity">
        update tb_slot
        <trim prefix="SET" suffixOverrides=",">
                    <if test="slotName != null and slotName != ''">slot_name = #{slotName},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="redirectValue != null">redirect_value = #{redirectValue},</if>
        </trim>
        where id = #{id}
    </update>
    <select id="selectByIds" resultMap="SlotResult">
        <include refid="selectSlotVo"/>
        where id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
