<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.OceanEngineProjectMapper">

    <resultMap type="com.ruoyi.system.entity.project.OceanEngineProjectEntity" id="OceanEngineProjectResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="projectId" column="project_id"/>
        <result property="appId" column="app_id"/>
        <result property="tvId" column="tv_id"/>
        <result property="microPromotionType" column="micro_promotion_type"/>
        <result property="projectName" column="project_name"/>
        <result property="projectCreateTime" column="project_create_time"/>
        <result property="projectModifyTime" column="project_modify_time"/>
        <result property="projectStatus" column="project_status"/>
        <result property="statusFirst" column="status_first"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectOceanEngineProjectVo">
        select id, advertiser_id, project_id, app_id, tv_id, micro_promotion_type, project_name, project_create_time,
        project_modify_time, project_status, status_first, gmt_create, gmt_modified from tb_ocean_engine_project
    </sql>

    <select id="selectOceanEngineProjectList" parameterType="com.ruoyi.system.entity.project.OceanEngineProjectEntity"
            resultMap="OceanEngineProjectResult">
        <include refid="selectOceanEngineProjectVo"/>
        <where>
            <if test="advertiserId != null ">and advertiser_id = #{advertiserId}</if>
            <if test="projectId != null ">and project_id = #{projectId}</if>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
            <if test="tvId != null  and tvId != ''">and tv_id = #{tvId}</if>
            <if test="microPromotionType != null  and microPromotionType != ''">and micro_promotion_type =
                #{microPromotionType}
            </if>
            <if test="projectName != null  and projectName != ''">and project_name = #{projectName}</if>
            <if test="projectCreateTime != null ">and project_create_time = #{projectCreateTime}</if>
            <if test="projectModifyTime != null ">and project_modify_time = #{projectModifyTime}</if>
            <if test="projectStatus != null  and projectStatus != ''">and project_status = #{projectStatus}</if>
            <if test="statusFirst != null  and statusFirst != ''">and status_first = #{statusFirst}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectOceanEngineProjectById" parameterType="Long" resultMap="OceanEngineProjectResult">
        <include refid="selectOceanEngineProjectVo"/>
        where id = #{id}
    </select>

    <select id="selectEmptyAppIdProject" resultType="com.ruoyi.system.entity.project.OceanEngineProjectEntity">
        <include refid="selectOceanEngineProjectVo"/>
        where app_id = "" and advertiser_id = #{advertiserId}
    </select>

    <select id="selectByProjectId" resultType="com.ruoyi.system.entity.project.OceanEngineProjectEntity">
        <include refid="selectOceanEngineProjectVo"/>
        <where>
            project_id in
            <foreach collection="cdpProjectId" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>


    <insert id="insertOceanEngineProject" parameterType="com.ruoyi.system.entity.project.OceanEngineProjectEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_ocean_engine_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="tvId != null">tv_id,</if>
            <if test="microPromotionType != null">micro_promotion_type,</if>
            <if test="projectName != null">project_name,</if>
            <if test="projectCreateTime != null">project_create_time,</if>
            <if test="projectModifyTime != null">project_modify_time,</if>
            <if test="projectStatus != null">project_status,</if>
            <if test="statusFirst != null">status_first,</if>
            <if test="gmtCreate != null">gmt_create,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="tvId != null">#{tvId},</if>
            <if test="microPromotionType != null">#{microPromotionType},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="projectCreateTime != null">#{projectCreateTime},</if>
            <if test="projectModifyTime != null">#{projectModifyTime},</if>
            <if test="projectStatus != null">#{projectStatus},</if>
            <if test="statusFirst != null">#{statusFirst},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
        </trim>
    </insert>

    <insert id="insertOrUpdateBatch">
        insert into tb_ocean_engine_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].projectId != null">project_id,</if>
            <if test="list[0].microPromotionType != null">micro_promotion_type,</if>
            <if test="list[0].projectName != null">project_name,</if>
            <if test="list[0].projectCreateTime != null">project_create_time,</if>
            <if test="list[0].projectModifyTime != null">project_modify_time,</if>
            <if test="list[0].projectStatus != null">project_status,</if>
            <if test="list[0].statusFirst != null">status_first,</if>
        </trim>
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.advertiserId != null">#{item.advertiserId},</if>
                <if test="item.projectId != null">#{item.projectId},</if>
                <if test="item.microPromotionType != null">#{item.microPromotionType},</if>
                <if test="item.projectName != null">#{item.projectName},</if>
                <if test="item.projectCreateTime != null">#{item.projectCreateTime},</if>
                <if test="item.projectModifyTime != null">#{item.projectModifyTime},</if>
                <if test="item.projectStatus != null">#{item.projectStatus},</if>
                <if test="item.statusFirst != null">#{item.statusFirst},</if>
            </trim>
        </foreach>
        on duplicate key update
        micro_promotion_type = values(micro_promotion_type),
        project_name = values(project_name),
        project_create_time = values(project_create_time),
        project_modify_time = values(project_modify_time),
        project_status = values(project_status),
        status_first = values(status_first)
    </insert>

    <update id="updateOceanEngineProject" parameterType="com.ruoyi.system.entity.project.OceanEngineProjectEntity">
        update tb_ocean_engine_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="tvId != null">tv_id = #{tvId},</if>
            <if test="microPromotionType != null">micro_promotion_type = #{microPromotionType},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="projectCreateTime != null">project_create_time = #{projectCreateTime},</if>
            <if test="projectModifyTime != null">project_modify_time = #{projectModifyTime},</if>
            <if test="projectStatus != null">project_status = #{projectStatus},</if>
            <if test="statusFirst != null">status_first = #{statusFirst},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOceanEngineProjectById" parameterType="Long">
        delete from tb_ocean_engine_project where id = #{id}
    </delete>

    <delete id="deleteOceanEngineProjectByIds" parameterType="String">
        delete from tb_ocean_engine_project where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
