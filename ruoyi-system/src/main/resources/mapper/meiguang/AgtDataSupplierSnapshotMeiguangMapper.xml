<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.meiguang.AgtDataSupplierSnapshotMeiguangMapper">

    <resultMap type="com.ruoyi.system.entity.meiguang.AgtDataSupplierSnapshotMeiguangEntity"
               id="AgtDataSupplierSnapshotMeiguangResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="memberId" column="member_id"/>
        <result property="agentId" column="agent_id"/>
        <result property="appName" column="app_name"/>
        <result property="gzhId" column="gzh_id"/>
        <result property="gzhUserId" column="gzh_user_id"/>
        <result property="payDate" column="pay_date"/>
        <result property="regDate" column="reg_date"/>
        <result property="createDate" column="create_date"/>
        <result property="payNotifyAmount" column="pay_notify_amount"/>
        <result property="linkName" column="link_name"/>
        <result property="linkId" column="link_id"/>
        <result property="movieId" column="movie_id"/>
        <result property="movieName" column="movie_name"/>
        <result property="maOpenid" column="ma_openid"/>
        <result property="status" column="status"/>
        <result property="orderCreateTime" column="order_create_time"/>
        <result property="deviceType" column="device_type"/>
        <result property="rechargeChannel" column="recharge_channel"/>
        <result property="adId" column="ad_id"/>
        <result property="platformId" column="platform_id"/>
        <result property="platformName" column="platform_name"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotMeiguangVo">
        select id, order_id, member_id, agent_id, app_name, gzh_id, gzh_user_id, pay_date, reg_date, create_date,
        pay_notify_amount, link_name, link_id, movie_id, movie_name, ma_openid, status, order_create_time, device_type,
        recharge_channel, ad_id, platform_id, platform_name, advertiser_id, gmt_create, gmt_modified from
        tb_agt_data_supplier_snapshot_meiguang
    </sql>

    <select id="selectAgtDataSupplierSnapshotMeiguangList"
            parameterType="com.ruoyi.system.entity.meiguang.AgtDataSupplierSnapshotMeiguangEntity"
            resultMap="AgtDataSupplierSnapshotMeiguangResult">
        <include refid="selectAgtDataSupplierSnapshotMeiguangVo"/>
        <where>
            <if test="orderId != null  and orderId != ''">and order_id = #{orderId}</if>
            <if test="memberId != null  and memberId != ''">and member_id = #{memberId}</if>
            <if test="agentId != null  and agentId != ''">and agent_id = #{agentId}</if>
            <if test="appName != null  and appName != ''">and app_name like concat('%', #{appName}, '%')</if>
            <if test="gzhId != null  and gzhId != ''">and gzh_id = #{gzhId}</if>
            <if test="gzhUserId != null  and gzhUserId != ''">and gzh_user_id = #{gzhUserId}</if>
            <if test="payDate != null ">and pay_date = #{payDate}</if>
            <if test="regDate != null ">and reg_date = #{regDate}</if>
            <if test="createDate != null ">and create_date = #{createDate}</if>
            <if test="payNotifyAmount != null ">and pay_notify_amount = #{payNotifyAmount}</if>
            <if test="linkName != null  and linkName != ''">and link_name like concat('%', #{linkName}, '%')</if>
            <if test="linkId != null  and linkId != ''">and link_id = #{linkId}</if>
            <if test="movieId != null  and movieId != ''">and movie_id = #{movieId}</if>
            <if test="movieName != null  and movieName != ''">and movie_name like concat('%', #{movieName}, '%')</if>
            <if test="maOpenid != null  and maOpenid != ''">and ma_openid = #{maOpenid}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="orderCreateTime != null ">and order_create_time = #{orderCreateTime}</if>
            <if test="deviceType != null ">and device_type = #{deviceType}</if>
            <if test="rechargeChannel != null ">and recharge_channel = #{rechargeChannel}</if>
            <if test="adId != null  and adId != ''">and ad_id = #{adId}</if>
            <if test="platformId != null  and platformId != ''">and platform_id = #{platformId}</if>
            <if test="platformName != null  and platformName != ''">and platform_name like concat('%', #{platformName},
                '%')
            </if>
            <if test="advertiserId != null  and advertiserId != ''">and advertiser_id = #{advertiserId}</if>
        </where>
    </select>

    <select id="selectListByDateAndHour" resultMap="AgtDataSupplierSnapshotMeiguangResult">
        <include refid="selectAgtDataSupplierSnapshotMeiguangVo"/>
        <where>
            <if test="date != null ">and pay_date &gt;= #{date} and date(pay_date) = #{date}</if>
            <if test="hour != null ">and hour(pay_date) = #{hour}</if>
        </where>
    </select>

    <select id="selectNewestLinkNameList" resultMap="AgtDataSupplierSnapshotMeiguangResult">
        SELECT m.link_id, m.link_name
        FROM tb_agt_data_supplier_snapshot_meiguang m
        INNER JOIN (SELECT link_id, MAX(pay_date) as pay_date
        FROM tb_agt_data_supplier_snapshot_meiguang
        WHERE pay_date >= curdate()
        GROUP BY link_id) as t
        ON m.pay_date = t.pay_date AND m.link_id = t.link_id
        WHERE m.pay_date >= curdate()
    </select>

    <select id="selectAgtDataSupplierSnapshotMeiguangById" parameterType="Long"
            resultMap="AgtDataSupplierSnapshotMeiguangResult">
        <include refid="selectAgtDataSupplierSnapshotMeiguangVo"/>
        where id = #{id}
    </select>

    <select id="selectPayCount" resultType="java.lang.Integer">
        SELECT
        (SELECT COUNT(DISTINCT member_id)
        FROM tb_agt_data_supplier_snapshot_meiguang
        WHERE date(pay_date) = #{dateReq}
        and link_name like concat(#{adId}, '%')
        and hour(pay_date) &lt;= #{hourReq})
        -
        (SELECT COUNT(DISTINCT member_id)
        FROM tb_agt_data_supplier_snapshot_meiguang
        WHERE date(pay_date) = #{dateReq}
        and link_name like concat(#{adId}, '%')
        and hour(pay_date) &lt;= #{hourReq} - 1) AS pay_count
    </select>

    <select id="selectOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*) from tb_agt_data_supplier_snapshot_meiguang
        where date(pay_date) = #{date}
        and hour(pay_date) = #{hour}
        and link_name like concat(#{advertiserId}, '%')
    </select>
    <select id="selectIncomeByDay"
            resultType="com.ruoyi.system.entity.meiguang.AgtDataSupplierSnapshotMeiguangEntity">
        SELECT
        sum( pay_notify_amount ) AS pay_notify_amount,
        DATE ( pay_date ) as pay_date
        FROM
        tb_agt_data_supplier_snapshot_meiguang
        WHERE
        DATE (pay_date) BETWEEN #{start} AND #{end}
        GROUP BY
        DATE (pay_date)
    </select>

    <insert id="batchInsert">
        insert ignore into tb_agt_data_supplier_snapshot_meiguang(`order_id`,`member_id`,`agent_id`,`app_name`,`gzh_id`,
        `gzh_user_id`,`pay_date`,`reg_date`,`create_date`,`pay_notify_amount`,`link_name`,`link_id`,`movie_id`,
        `movie_name`,`ma_openid`,`status`,`order_create_time`,`device_type`,`recharge_channel`,`ad_id`,`platform_id`,
        `platform_name`,`advertiser_id`)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.orderId}, #{record.memberId}, #{record.agentId}, #{record.appName}, #{record.gzhId},
            #{record.gzhUserId}, #{record.payDate}, #{record.regDate}, #{record.createDate}, #{record.payNotifyAmount},
            #{record.linkName}, #{record.linkId}, #{record.movieId},
            #{record.movieName}, #{record.maOpenid}, #{record.status}, #{record.orderCreateTime}, #{record.deviceType},
            #{record.rechargeChannel}, #{record.adId}, #{record.platformId},
            #{record.platformName}, #{record.advertiserId}
            )
        </foreach>
    </insert>

    <insert id="insert" parameterType="com.ruoyi.system.entity.meiguang.AgtDataSupplierSnapshotMeiguangEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_agt_data_supplier_snapshot_meiguang
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null and orderId != ''">order_id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="appName != null">app_name,</if>
            <if test="gzhId != null">gzh_id,</if>
            <if test="gzhUserId != null">gzh_user_id,</if>
            <if test="payDate != null">pay_date,</if>
            <if test="regDate != null">reg_date,</if>
            <if test="createDate != null">create_date,</if>
            <if test="payNotifyAmount != null">pay_notify_amount,</if>
            <if test="linkName != null">link_name,</if>
            <if test="linkId != null">link_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="movieName != null">movie_name,</if>
            <if test="maOpenid != null">ma_openid,</if>
            <if test="status != null">status,</if>
            <if test="orderCreateTime != null">order_create_time,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="rechargeChannel != null">recharge_channel,</if>
            <if test="adId != null">ad_id,</if>
            <if test="platformId != null">platform_id,</if>
            <if test="platformName != null">platform_name,</if>
            <if test="advertiserId != null">advertiser_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null and orderId != ''">#{orderId},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="appName != null">#{appName},</if>
            <if test="gzhId != null">#{gzhId},</if>
            <if test="gzhUserId != null">#{gzhUserId},</if>
            <if test="payDate != null">#{payDate},</if>
            <if test="regDate != null">#{regDate},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="payNotifyAmount != null">#{payNotifyAmount},</if>
            <if test="linkName != null">#{linkName},</if>
            <if test="linkId != null">#{linkId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="movieName != null">#{movieName},</if>
            <if test="maOpenid != null">#{maOpenid},</if>
            <if test="status != null">#{status},</if>
            <if test="orderCreateTime != null">#{orderCreateTime},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="rechargeChannel != null">#{rechargeChannel},</if>
            <if test="adId != null">#{adId},</if>
            <if test="platformId != null">#{platformId},</if>
            <if test="platformName != null">#{platformName},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.meiguang.AgtDataSupplierSnapshotMeiguangEntity">
        update tb_agt_data_supplier_snapshot_meiguang
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null and orderId != ''">order_id = #{orderId},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="gzhId != null">gzh_id = #{gzhId},</if>
            <if test="gzhUserId != null">gzh_user_id = #{gzhUserId},</if>
            <if test="payDate != null">pay_date = #{payDate},</if>
            <if test="regDate != null">reg_date = #{regDate},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="payNotifyAmount != null">pay_notify_amount = #{payNotifyAmount},</if>
            <if test="linkName != null">link_name = #{linkName},</if>
            <if test="linkId != null">link_id = #{linkId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="movieName != null">movie_name = #{movieName},</if>
            <if test="maOpenid != null">ma_openid = #{maOpenid},</if>
            <if test="status != null">status = #{status},</if>
            <if test="orderCreateTime != null">order_create_time = #{orderCreateTime},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="rechargeChannel != null">recharge_channel = #{rechargeChannel},</if>
            <if test="adId != null">ad_id = #{adId},</if>
            <if test="platformId != null">platform_id = #{platformId},</if>
            <if test="platformName != null">platform_name = #{platformName},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
