<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.hotcake.HotcakeDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.hotcake.HotcakeDayDataEntity" id="HotcakeDayDataResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="appId"    column="app_id"    />
            <result property="channelCode"    column="channel_code"    />
            <result property="registerPv"    column="register_pv"    />
            <result property="registerUv"    column="register_uv"    />
            <result property="loginPv"    column="login_pv"    />
            <result property="loginUv"    column="login_uv"    />
            <result property="retentionDay1Uv"    column="retention_day1_uv"    />
            <result property="splashExposurePv"    column="splash_exposure_pv"    />
            <result property="splashExposureUv"    column="splash_exposure_uv"    />
            <result property="splashClickPv"    column="splash_click_pv"    />
            <result property="splashClickUv"    column="splash_click_uv"    />
            <result property="interstitialExposurePv"    column="interstitial_exposure_pv"    />
            <result property="interstitialExposureUv"    column="interstitial_exposure_uv"    />
            <result property="interstitialClickPv"    column="interstitial_click_pv"    />
            <result property="interstitialClickUv"    column="interstitial_click_uv"    />
            <result property="feedPageExposurePv"    column="feed_page_exposure_pv"    />
            <result property="feedPageExposureUv"    column="feed_page_exposure_uv"    />
            <result property="feedPageClickPv"    column="feed_page_click_pv"    />
            <result property="feedPageClickUv"    column="feed_page_click_uv"    />
            <result property="playPageExposurePv"    column="play_page_exposure_pv"    />
            <result property="playPageExposureUv"    column="play_page_exposure_uv"    />
            <result property="playPageClickPv"    column="play_page_click_pv"    />
            <result property="playPageClickUv"    column="play_page_click_uv"    />
            <result property="videoUnlockExposurePv"    column="video_unlock_exposure_pv"    />
            <result property="videoUnlockExposureUv"    column="video_unlock_exposure_uv"    />
            <result property="videoUnlockClickPv"    column="video_unlock_click_pv"    />
            <result property="videoUnlockClickUv"    column="video_unlock_click_uv"    />
            <result property="leftRetExposurePv"    column="left_ret_exposure_pv"    />
            <result property="leftRetExposureUv"    column="left_ret_exposure_uv"    />
            <result property="leftRetClickPv"    column="left_ret_click_pv"    />
            <result property="leftRetClickUv"    column="left_ret_click_uv"    />
            <result property="physicalRetExposurePv"    column="physical_ret_exposure_pv"    />
            <result property="physicalRetExposureUv"    column="physical_ret_exposure_uv"    />
            <result property="physicalRetClickPv"    column="physical_ret_click_pv"    />
            <result property="physicalRetClickUv"    column="physical_ret_click_uv"    />
            <result property="closeExposurePv"    column="close_exposure_pv"    />
            <result property="closeExposureUv"    column="close_exposure_uv"    />
            <result property="closeClickPv"    column="close_click_pv"    />
            <result property="closeClickUv"    column="close_click_uv"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectHotcakeDayDataVo">
        select id, cur_date, app_id, channel_code,
               register_pv, register_uv, login_pv, login_uv, retention_day1_uv,
               splash_exposure_pv, splash_exposure_uv,
               splash_click_pv, splash_click_uv,
               interstitial_exposure_pv, interstitial_exposure_uv,
               interstitial_click_pv, interstitial_click_uv,
               feed_page_exposure_pv, feed_page_exposure_uv,
               feed_page_click_pv, feed_page_click_uv,
               play_page_exposure_pv, play_page_exposure_uv,
               play_page_click_pv, play_page_click_uv,
               video_unlock_exposure_pv, video_unlock_exposure_uv,
               video_unlock_click_pv, video_unlock_click_uv,
               left_ret_exposure_pv, left_ret_exposure_uv,
               left_ret_click_pv, left_ret_click_uv,
               physical_ret_exposure_pv, physical_ret_exposure_uv,
               physical_ret_click_pv, physical_ret_click_uv,
               close_exposure_pv, close_exposure_uv,
               close_click_pv, close_click_uv,
               gmt_create, gmt_modified
        from tb_hotcake_day_data
    </sql>

    <select id="selectBy" resultMap="HotcakeDayDataResult">
        <include refid="selectHotcakeDayDataVo"/>
        where cur_date = #{curDate} and app_id = #{appId} and channel_code = #{channelCode}
    </select>

    <select id="selectHotcakeDayDataList" parameterType="com.ruoyi.system.req.hotcake.HotcakeDayDataListReq" resultMap="HotcakeDayDataResult">
        select cur_date, app_id,
            sum(register_pv) as register_pv, sum(register_uv) as register_uv,
            sum(login_pv) as login_pv,  sum(login_uv) as login_uv, sum(retention_day1_uv) as retention_day1_uv,
            sum(splash_exposure_pv) as splash_exposure_pv, sum(splash_exposure_uv) as splash_exposure_uv,
            sum(splash_click_pv) as splash_click_pv, sum(splash_click_uv) as splash_click_uv,
            sum(interstitial_exposure_pv) as interstitial_exposure_pv, sum(interstitial_exposure_uv) as interstitial_exposure_uv,
            sum(interstitial_click_pv) as interstitial_click_pv, sum(interstitial_click_uv) as interstitial_click_uv,
            sum(feed_page_exposure_pv) as feed_page_exposure_pv, sum(feed_page_exposure_uv) as feed_page_exposure_uv,
            sum(feed_page_click_pv) as feed_page_click_pv, sum(feed_page_click_uv) as feed_page_click_uv,
            sum(play_page_exposure_pv) as play_page_exposure_pv, sum(play_page_exposure_uv) as play_page_exposure_uv,
            sum(play_page_click_pv) as play_page_click_pv, sum(play_page_click_uv) as play_page_click_uv,
            sum(video_unlock_exposure_pv) as video_unlock_exposure_pv, sum(video_unlock_exposure_uv) as video_unlock_exposure_uv,
            sum(video_unlock_click_pv) as video_unlock_click_pv, sum(video_unlock_click_uv) as video_unlock_click_uv,
            sum(left_ret_exposure_pv) as left_ret_exposure_pv, sum(left_ret_exposure_uv) as left_ret_exposure_uv,
            sum(left_ret_click_pv) as left_ret_click_pv, sum(left_ret_click_uv) as left_ret_click_uv,
            sum(physical_ret_exposure_pv) as physical_ret_exposure_pv, sum(physical_ret_exposure_uv) as physical_ret_exposure_uv,
            sum(physical_ret_click_pv) as physical_ret_click_pv, sum(physical_ret_click_uv) as physical_ret_click_uv,
            sum(close_exposure_pv) as close_exposure_pv, sum(close_exposure_uv) as close_exposure_uv,
            sum(close_click_pv) as close_click_pv, sum(close_click_uv) as close_click_uv
        from tb_hotcake_day_data
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="appSearch != null and appSearch != ''">
                and app_id in (
                    select app_id from tb_hotcake_miniapp where app_name like concat('%', #{appSearch}, '%') or app_id = #{appSearch}
                )
            </if>
        </where>
        group by cur_date, app_id
        order by cur_date desc, register_pv desc, login_pv desc
    </select>

    <select id="selectHotcakeDayDataById" parameterType="Long" resultMap="HotcakeDayDataResult">
            <include refid="selectHotcakeDayDataVo"/>
            where id = #{id}
    </select>

    <insert id="insertHotcakeDayData" parameterType="com.ruoyi.system.entity.hotcake.HotcakeDayDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_hotcake_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">cur_date,</if>
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="channelCode != null and channelCode != ''">channel_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">#{curDate},</if>
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
        </trim>
    </insert>

    <update id="updateHotcakeDayData" parameterType="com.ruoyi.system.req.hotcake.HotcakeDayDataUpdateReq">
        update tb_hotcake_day_data
        <trim prefix="SET" suffixOverrides=",">
                <if test="registerPvAdd != null">register_pv = register_pv + #{registerPvAdd},</if>
                <if test="registerUvAdd != null">register_uv = register_uv + #{registerUvAdd},</if>
                <if test="loginPvAdd != null">login_pv = login_pv + #{loginPvAdd},</if>
                <if test="loginUvAdd != null">login_uv = login_uv + #{loginUvAdd},</if>
                <if test="retentionDay1UvAdd != null">retention_day1_uv = retention_day1_uv + #{retentionDay1UvAdd},</if>
                <if test="splashExposurePvAdd != null">splash_exposure_pv = splash_exposure_pv + #{splashExposurePvAdd},</if>
                <if test="splashExposureUvAdd != null">splash_exposure_uv = splash_exposure_uv + #{splashExposureUvAdd},</if>
                <if test="splashClickPvAdd != null">splash_click_pv = splash_click_pv + #{splashClickPvAdd},</if>
                <if test="splashClickUvAdd != null">splash_click_uv = splash_click_uv + #{splashClickUvAdd},</if>
                <if test="interstitialExposurePvAdd != null">interstitial_exposure_pv = interstitial_exposure_pv + #{interstitialExposurePvAdd},</if>
                <if test="interstitialExposureUvAdd != null">interstitial_exposure_uv = interstitial_exposure_uv + #{interstitialExposureUvAdd},</if>
                <if test="interstitialClickPvAdd != null">interstitial_click_pv = interstitial_click_pv + #{interstitialClickPvAdd},</if>
                <if test="interstitialClickUvAdd != null">interstitial_click_uv = interstitial_click_uv + #{interstitialClickUvAdd},</if>
                <if test="feedPageExposurePvAdd != null">feed_page_exposure_pv = feed_page_exposure_pv + #{feedPageExposurePvAdd},</if>
                <if test="feedPageExposureUvAdd != null">feed_page_exposure_uv = feed_page_exposure_uv + #{feedPageExposureUvAdd},</if>
                <if test="feedPageClickPvAdd != null">feed_page_click_pv = feed_page_click_pv + #{feedPageClickPvAdd},</if>
                <if test="feedPageClickUvAdd != null">feed_page_click_uv = feed_page_click_uv + #{feedPageClickUvAdd},</if>
                <if test="playPageExposurePvAdd != null">play_page_exposure_pv = play_page_exposure_pv + #{playPageExposurePvAdd},</if>
                <if test="playPageExposureUvAdd != null">play_page_exposure_uv = play_page_exposure_uv + #{playPageExposureUvAdd},</if>
                <if test="playPageClickPvAdd != null">play_page_click_pv = play_page_click_pv + #{playPageClickPvAdd},</if>
                <if test="playPageClickUvAdd != null">play_page_click_uv = play_page_click_uv + #{playPageClickUvAdd},</if>
                <if test="videoUnlockExposurePvAdd != null">video_unlock_exposure_pv = video_unlock_exposure_pv + #{videoUnlockExposurePvAdd},</if>
                <if test="videoUnlockExposureUvAdd != null">video_unlock_exposure_uv = video_unlock_exposure_uv + #{videoUnlockExposureUvAdd},</if>
                <if test="videoUnlockClickPvAdd != null">video_unlock_click_pv = video_unlock_click_pv + #{videoUnlockClickPvAdd},</if>
                <if test="videoUnlockClickUvAdd != null">video_unlock_click_uv = video_unlock_click_uv + #{videoUnlockClickUvAdd},</if>
                <if test="leftRetExposurePvAdd != null">left_ret_exposure_pv = left_ret_exposure_pv + #{leftRetExposurePvAdd},</if>
                <if test="leftRetExposureUvAdd != null">left_ret_exposure_uv = left_ret_exposure_uv + #{leftRetExposureUvAdd},</if>
                <if test="leftRetClickPvAdd != null">left_ret_click_pv = left_ret_click_pv + #{leftRetClickPvAdd},</if>
                <if test="leftRetClickUvAdd != null">left_ret_click_uv = left_ret_click_uv + #{leftRetClickUvAdd},</if>
                <if test="physicalRetExposurePvAdd != null">physical_ret_exposure_pv = physical_ret_exposure_pv + #{physicalRetExposurePvAdd},</if>
                <if test="physicalRetExposureUvAdd != null">physical_ret_exposure_uv = physical_ret_exposure_uv + #{physicalRetExposureUvAdd},</if>
                <if test="physicalRetClickPvAdd != null">physical_ret_click_pv = physical_ret_click_pv + #{physicalRetClickPvAdd},</if>
                <if test="physicalRetClickUvAdd != null">physical_ret_click_uv = physical_ret_click_uv + #{physicalRetClickUvAdd},</if>
                <if test="closeExposurePvAdd != null">close_exposure_pv = close_exposure_pv + #{closeExposurePvAdd},</if>
                <if test="closeExposureUvAdd != null">close_exposure_uv = close_exposure_uv + #{closeExposureUvAdd},</if>
                <if test="closeClickPvAdd != null">close_click_pv = close_click_pv + #{closeClickPvAdd},</if>
                <if test="closeClickUvAdd != null">close_click_uv = close_click_uv + #{closeClickUvAdd},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
