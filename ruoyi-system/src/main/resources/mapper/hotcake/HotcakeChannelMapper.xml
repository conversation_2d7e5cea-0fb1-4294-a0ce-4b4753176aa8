<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.hotcake.HotcakeChannelMapper">

    <resultMap type="com.ruoyi.system.entity.hotcake.HotcakeChannelEntity" id="HotcakeChannelResult">
            <result property="id"    column="id"    />
            <result property="promotionName"    column="promotion_name"    />
            <result property="promotionUrl"    column="promotion_url"    />
            <result property="channelCode"    column="channel_code"    />
            <result property="videoId"    column="video_id"    />
            <result property="freeAd"    column="free_ad"    />
            <result property="shareIpu"    column="share_ipu"    />
            <result property="clickAdIpu"    column="click_ad_ipu"    />
            <result property="feedPageVideoIds" column="feed_page_video_ids"    />
            <result property="playPageVideoIds" column="play_page_video_ids"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectHotcakeChannelVo">
        select id, promotion_name, promotion_url, channel_code,free_ad,share_ipu,click_ad_ipu, video_id, feed_page_video_ids, play_page_video_ids, gmt_create, gmt_modified from tb_hotcake_channel
    </sql>

    <select id="selectByChanelCode" parameterType="String" resultMap="HotcakeChannelResult">
        <include refid="selectHotcakeChannelVo"/>
        where channel_code = #{channelCode}
        limit 1
    </select>

    <select id="selectHotcakeChannelList" parameterType="com.ruoyi.system.entity.hotcake.HotcakeChannelEntity" resultMap="HotcakeChannelResult">
        <include refid="selectHotcakeChannelVo"/>
        <where>
                        <if test="promotionName != null  and promotionName != ''"> and promotion_name like concat('%', #{promotionName}, '%')</if>
                        <if test="promotionUrl != null  and promotionUrl != ''"> and promotion_url = #{promotionUrl}</if>
                        <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
                        <if test="videoId != null "> and video_id = #{videoId}</if>
                        <if test="feedPageVideoIds != null  and feedPageVideoIds != ''"> and feed_page_video_ids = #{feedPageVideoIds}</if>
                        <if test="playPageVideoIds != null  and playPageVideoIds != ''"> and play_page_video_ids = #{playPageVideoIds}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectHotcakeChannelById" parameterType="Long" resultMap="HotcakeChannelResult">
            <include refid="selectHotcakeChannelVo"/>
            where id = #{id}
    </select>

    <insert id="insertHotcakeChannel" parameterType="com.ruoyi.system.entity.hotcake.HotcakeChannelEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_hotcake_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="promotionName != null and promotionName != ''">promotion_name,</if>
                    <if test="promotionUrl != null and promotionUrl != ''">promotion_url,</if>
                    <if test="channelCode != null and channelCode != ''">channel_code,</if>
                    <if test="videoId != null">video_id,</if>
                    <if test="freeAd != null">free_ad,</if>
                    <if test="shareIpu != null">share_ipu,</if>
                    <if test="clickAdIpu != null">click_ad_ipu,</if>
                    <if test="feedPageVideoIds != null and feedPageVideoIds != ''">feed_page_video_ids,</if>
                    <if test="playPageVideoIds != null and playPageVideoIds != ''">play_page_video_ids,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="promotionName != null and promotionName != ''">#{promotionName},</if>
                    <if test="promotionUrl != null and promotionUrl != ''">#{promotionUrl},</if>
                    <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
                    <if test="videoId != null">#{videoId},</if>
                    <if test="freeAd != null">#{freeAd},</if>
                    <if test="shareIpu != null">#{shareIpu},</if>
                    <if test="clickAdIpu != null">#{clickAdIpu},</if>
                    <if test="feedPageVideoIds != null and feedPageVideoIds != ''">#{feedPageVideoIds},</if>
                    <if test="playPageVideoIds != null and playPageVideoIds != ''">#{playPageVideoIds},</if>
        </trim>
    </insert>

    <update id="updateHotcakeChannel" parameterType="com.ruoyi.system.entity.hotcake.HotcakeChannelEntity">
        update tb_hotcake_channel
        <trim prefix="SET" suffixOverrides=",">
                <if test="promotionName != null and promotionName != ''">promotion_name = #{promotionName},</if>
                <if test="promotionUrl != null and promotionUrl != ''">promotion_url = #{promotionUrl},</if>
                <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
                <if test="videoId != null">video_id = #{videoId},</if>
                <if test="freeAd != null">free_ad = #{freeAd},</if>
                <if test="shareIpu != null">share_ipu = #{shareIpu},</if>
                <if test="clickAdIpu != null">click_ad_ipu = #{clickAdIpu},</if>
                <if test="feedPageVideoIds != null and feedPageVideoIds != ''">feed_page_video_ids = #{feedPageVideoIds},</if>
                <if test="playPageVideoIds != null and playPageVideoIds != ''">play_page_video_ids = #{playPageVideoIds},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
