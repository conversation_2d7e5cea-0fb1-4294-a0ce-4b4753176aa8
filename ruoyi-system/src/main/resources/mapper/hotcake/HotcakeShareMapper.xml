<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.hotcake.HotcakeShareMapper">

    <resultMap type="com.ruoyi.system.entity.hotcake.HotcakeShareEntity" id="HotcakeShareResult">
            <result property="id"    column="id"    />
            <result property="userId"    column="user_id"    />
            <result property="sharedUserId"    column="shared_user_id"    />
            <result property="sourceVideoId"    column="source_video_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
    </resultMap>

    <sql id="selectHotcakeShareVo">
        select id, user_id, shared_user_id, source_video_id, gmt_create from tb_hotcake_share
    </sql>

    <select id="selectHotcakeShareList" parameterType="com.ruoyi.system.entity.hotcake.HotcakeShareEntity" resultMap="HotcakeShareResult">
        <include refid="selectHotcakeShareVo"/>
        <where>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="sharedUserId != null "> and shared_user_id = #{sharedUserId}</if>
                        <if test="sourceVideoId != null "> and source_video_id = #{sourceVideoId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
        </where>
        order by id desc
    </select>

    <select id="selectHotcakeShareById" parameterType="Long" resultMap="HotcakeShareResult">
            <include refid="selectHotcakeShareVo"/>
            where id = #{id}
    </select>
    <select id="countByUserIdAndDate" resultType="java.lang.Integer">
        select count(1) from tb_hotcake_share where user_id = #{userId} and gmt_create >= #{date}
    </select>

    <insert id="insertHotcakeShare" parameterType="com.ruoyi.system.entity.hotcake.HotcakeShareEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_hotcake_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,</if>
                    <if test="sharedUserId != null">shared_user_id,</if>
                    <if test="sourceVideoId != null">source_video_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},</if>
                    <if test="sharedUserId != null">#{sharedUserId},</if>
                    <if test="sourceVideoId != null">#{sourceVideoId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into tb_hotcake_share(user_id, shared_user_id, source_video_id)
        values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (#{item.userId},#{item.sharedUserId},#{item.sourceVideoId})
        </foreach>
    </insert>

    <update id="updateHotcakeShare" parameterType="com.ruoyi.system.entity.hotcake.HotcakeShareEntity">
        update tb_hotcake_share
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="sharedUserId != null">shared_user_id = #{sharedUserId},</if>
                    <if test="sourceVideoId != null">source_video_id = #{sourceVideoId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHotcakeShareById" parameterType="Long">
        delete from tb_hotcake_share where id = #{id}
    </delete>

    <delete id="deleteHotcakeShareByIds" parameterType="String">
        delete from tb_hotcake_share where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>