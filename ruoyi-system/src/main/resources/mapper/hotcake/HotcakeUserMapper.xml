<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.hotcake.HotcakeUserMapper">

    <resultMap type="com.ruoyi.system.entity.hotcake.HotcakeUserEntity" id="HotcakeUserResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="openid"    column="openid"    />
            <result property="nickname"    column="nickname"    />
            <result property="avatar"    column="avatar"    />
            <result property="phone"    column="phone"    />
            <result property="channelCode"    column="channel_code"    />
            <result property="osType"    column="os_type"    />
            <result property="source"    column="source"    />
            <result property="shareUserId"    column="share_user_id"    />
            <result property="registerDate"    column="register_date"    />
            <result property="lastLoginTime"    column="last_login_time"    />
            <result property="lastLoginIp"    column="last_login_ip"    />
            <result property="status"    column="status"    />
            <result property="pathParamStr"    column="path_param_str"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectHotcakeUserVo">
        select id, app_id, openid, nickname, avatar,source, share_user_id,phone, channel_code, os_type, register_date, last_login_time, last_login_ip, status, path_param_str, gmt_create from tb_hotcake_user
    </sql>

    <select id="selectHotcakeUserList" parameterType="com.ruoyi.system.entity.hotcake.HotcakeUserEntity" resultMap="HotcakeUserResult">
        <include refid="selectHotcakeUserVo"/>
        <where>
                        <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
                        <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
                        <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
                        <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
                        <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
                        <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
                        <if test="osType != null "> and os_type = #{osType}</if>
                        <if test="registerDate != null "> and register_date = #{registerDate}</if>
                        <if test="lastLoginTime != null "> and last_login_time = #{lastLoginTime}</if>
                        <if test="lastLoginIp != null  and lastLoginIp != ''"> and last_login_ip = #{lastLoginIp}</if>
                        <if test="status != null "> and status = #{status}</if>
                        <if test="pathParamStr != null  and pathParamStr != ''"> and path_param_str = #{pathParamStr}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectById" parameterType="Long" resultMap="HotcakeUserResult">
            <include refid="selectHotcakeUserVo"/>
            where id = #{id}
    </select>

    <select id="selectByOpenId" parameterType="String" resultMap="HotcakeUserResult">
        <include refid="selectHotcakeUserVo"/>
        where openid = #{openId}
    </select>

    <insert id="insertHotcakeUser" parameterType="com.ruoyi.system.entity.hotcake.HotcakeUserEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_hotcake_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="openid != null and openid != ''">openid,</if>
                    <if test="nickname != null and nickname != ''">nickname,</if>
                    <if test="avatar != null and avatar != ''">avatar,</if>
                    <if test="phone != null and phone != ''">phone,</if>
                    <if test="channelCode != null and channelCode != ''">channel_code,</if>
                    <if test="osType != null">os_type,</if>
                    <if test="source != null">`source`,</if>
                    <if test="shareUserId != null">share_user_id,</if>
                    <if test="registerDate != null">register_date,</if>
                    <if test="lastLoginTime != null">last_login_time,</if>
                    <if test="lastLoginIp != null and lastLoginIp != ''">last_login_ip,</if>
                    <if test="status != null">status,</if>
                    <if test="pathParamStr != null and pathParamStr != ''">path_param_str,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="openid != null and openid != ''">#{openid},</if>
                    <if test="nickname != null and nickname != ''">#{nickname},</if>
                    <if test="avatar != null and avatar != ''">#{avatar},</if>
                    <if test="phone != null and phone != ''">#{phone},</if>
                    <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
                    <if test="osType != null">#{osType},</if>
                    <if test="source != null">#{source},</if>
                    <if test="shareUserId != null">#{shareUserId},</if>
                    <if test="registerDate != null">#{registerDate},</if>
                    <if test="lastLoginTime != null">#{lastLoginTime},</if>
                    <if test="lastLoginIp != null and lastLoginIp != ''">#{lastLoginIp},</if>
                    <if test="status != null">#{status},</if>
                    <if test="pathParamStr != null and pathParamStr != ''">#{pathParamStr},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateHotcakeUser" parameterType="com.ruoyi.system.entity.hotcake.HotcakeUserEntity">
        update tb_hotcake_user
        <trim prefix="SET" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="openid != null and openid != ''">openid = #{openid},</if>
                    <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
                    <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
                    <if test="phone != null and phone != ''">phone = #{phone},</if>
                    <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
                    <if test="osType != null">os_type = #{osType},</if>
                    <if test="source != null">`source` = #{source},</if>
                    <if test="shareUserId != null">share_user_id = #{shareUserId},</if>
                    <if test="registerDate != null">register_date = #{registerDate},</if>
                    <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
                    <if test="lastLoginIp != null and lastLoginIp != ''">last_login_ip = #{lastLoginIp},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="pathParamStr != null and pathParamStr != ''">path_param_str = #{pathParamStr},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
