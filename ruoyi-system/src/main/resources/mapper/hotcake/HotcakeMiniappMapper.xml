<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.hotcake.HotcakeMiniappMapper">

    <resultMap type="com.ruoyi.system.entity.hotcake.HotcakeMiniappEntity" id="HotcakeMiniappResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="appSecret"    column="app_secret"    />
            <result property="appName"    column="app_name"    />
            <result property="basicConfig"    column="basic_config"    />
            <result property="slotConfig"    column="slot_config"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectHotcakeMiniappVo">
        select id, app_id, app_secret, app_name, basic_config, slot_config, gmt_create, gmt_modified from tb_hotcake_miniapp
    </sql>

    <select id="selectByAppId" parameterType="String" resultMap="HotcakeMiniappResult">
        <include refid="selectHotcakeMiniappVo"/>
        where app_id = #{appId}
    </select>

    <select id="selectHotcakeMiniappList" parameterType="com.ruoyi.system.req.hotcake.HotcakeMiniappListReq" resultMap="HotcakeMiniappResult">
        select id, app_id, app_secret, app_name, gmt_create, gmt_modified
        from tb_hotcake_miniapp
        <where>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
            <if test="appSearch != null  and appSearch != ''"> and (app_name like concat('%', #{appSearch}, '%') or app_id = #{appSearch})</if>
        </where>
        order by id desc
    </select>

    <select id="selectHotcakeMiniappById" parameterType="Long" resultMap="HotcakeMiniappResult">
            <include refid="selectHotcakeMiniappVo"/>
            where id = #{id}
    </select>

    <insert id="insertHotcakeMiniapp" parameterType="com.ruoyi.system.entity.hotcake.HotcakeMiniappEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_hotcake_miniapp
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="appId != null and appId != ''">app_id,</if>
                <if test="appSecret != null and appSecret != ''">app_secret,</if>
                <if test="appName != null and appName != ''">app_name,</if>
                <if test="basicConfig != null">basic_config,</if>
                <if test="slotConfig != null">slot_config,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="appId != null and appId != ''">#{appId},</if>
                <if test="appName != null and appName != ''">#{appName},</if>
                <if test="basicConfig != null">#{basicConfig},</if>
                <if test="slotConfig != null">#{slotConfig},</if>
        </trim>
    </insert>

    <update id="updateHotcakeMiniapp" parameterType="com.ruoyi.system.entity.hotcake.HotcakeMiniappEntity">
        update tb_hotcake_miniapp
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="appSecret != null and appSecret != ''">app_secret = #{appSecret},</if>
            <if test="appName != null and appName != ''">app_name = #{appName},</if>
            <if test="basicConfig != null">basic_config = #{basicConfig},</if>
            <if test="slotConfig != null">slot_config = #{slotConfig},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
