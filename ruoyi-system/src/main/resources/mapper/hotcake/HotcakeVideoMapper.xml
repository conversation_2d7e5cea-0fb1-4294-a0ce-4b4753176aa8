<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.hotcake.HotcakeVideoMapper">

    <resultMap type="com.ruoyi.system.entity.hotcake.HotcakeVideoEntity" id="HotcakeVideoResult">
            <result property="id"    column="id"    />
            <result property="title"    column="title"    />
            <result property="coverImg"    column="cover_img"    />
            <result property="videoUrl"    column="video_url"    />
            <result property="tags"    column="tags"    />
            <result property="weight"    column="weight"    />
            <result property="authorAvatar"    column="author_avatar"    />
            <result property="authorNickname"    column="author_nickname"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectHotcakeVideoVo">
        select id, title, cover_img, video_url, tags, weight, author_avatar, author_nickname, gmt_create, gmt_modified from tb_hotcake_video
    </sql>

    <select id="selectListForWeb" parameterType="com.ruoyi.system.req.hotcake.HotcakeWebVideoListReq" resultMap="HotcakeVideoResult">
        <include refid="selectHotcakeVideoVo"/>
        <where>
            tags not like '%下线%'
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="tagSearch != null and tagSearch != ''">and concat(ifnull(tags, ','), tags, ',') REGEXP #{tagSearch}</if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="excludeIds != null and excludeIds.size() > 0">
                and id not in
                <foreach collection="excludeIds" open="(" close=")" item="excludeId" separator=",">
                    #{excludeId}
                </foreach>
            </if>
        </where>
        ORDER BY
        CASE
            WHEN tags like '%常规%' THEN 1
            ELSE 10
        END DESC, id desc
    </select>

    <select id="selectHotcakeVideoList" parameterType="com.ruoyi.system.req.hotcake.HotcakeVideoListReq" resultMap="HotcakeVideoResult">
        <include refid="selectHotcakeVideoVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="tagSearch != null and tagSearch != ''">and concat(ifnull(tags, ','), tags, ',') REGEXP #{tagSearch}</if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectById" parameterType="Long" resultMap="HotcakeVideoResult">
            <include refid="selectHotcakeVideoVo"/>
            where id = #{id}
    </select>

    <insert id="insertHotcakeVideo" parameterType="com.ruoyi.system.entity.hotcake.HotcakeVideoEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_hotcake_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="title != null and title != ''">title,</if>
                    <if test="coverImg != null and coverImg != ''">cover_img,</if>
                    <if test="videoUrl != null and videoUrl != ''">video_url,</if>
                    <if test="tags != null and tags != ''">tags,</if>
                    <if test="weight != null">weight,</if>
                    <if test="authorAvatar != null and authorAvatar != ''">author_avatar,</if>
                    <if test="authorNickname != null and authorNickname != ''">author_nickname,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="title != null and title != ''">#{title},</if>
                    <if test="coverImg != null and coverImg != ''">#{coverImg},</if>
                    <if test="videoUrl != null and videoUrl != ''">#{videoUrl},</if>
                    <if test="tags != null and tags != ''">#{tags},</if>
                    <if test="weight != null">#{weight},</if>
                    <if test="authorAvatar != null and authorAvatar != ''">#{authorAvatar},</if>
                    <if test="authorNickname != null and authorNickname != ''">#{authorNickname},</if>
        </trim>
    </insert>

    <update id="updateHotcakeVideo" parameterType="com.ruoyi.system.entity.hotcake.HotcakeVideoEntity">
        update tb_hotcake_video
        <trim prefix="SET" suffixOverrides=",">
                    <if test="title != null and title != ''">title = #{title},</if>
                    <if test="coverImg != null and coverImg != ''">cover_img = #{coverImg},</if>
                    <if test="videoUrl != null and videoUrl != ''">video_url = #{videoUrl},</if>
                    <if test="tags != null and tags != ''">tags = #{tags},</if>
                    <if test="weight != null">weight = #{weight},</if>
                    <if test="authorAvatar != null and authorAvatar != ''">author_avatar = #{authorAvatar},</if>
                    <if test="authorNickname != null and authorNickname != ''">author_nickname = #{authorNickname},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
