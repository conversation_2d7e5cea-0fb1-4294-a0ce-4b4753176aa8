<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fundStat.AgtAdvertiserFundDailyStatMapper">

    <resultMap type="com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity" id="AgtAdvertiserFundDailyStatResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="date"    column="date"    />
            <result property="balance"    column="balance"    />
            <result property="grantBalance"    column="grant_balance"    />
            <result property="nonGrantBalance"    column="non_grant_balance"    />
            <result property="cashCost"    column="cash_cost"    />
            <result property="cost"    column="cost"    />
            <result property="frozen"    column="frozen"    />
            <result property="income"    column="income"    />
            <result property="rewardCost"    column="reward_cost"    />
            <result property="sharedWalletCost"    column="shared_wallet_cost"    />
            <result property="companyWalletCost"    column="company_wallet_cost"    />
            <result property="transferIn"    column="transfer_in"    />
            <result property="transferOut"    column="transfer_out"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtAdvertiserFundDailyStatVo">
        select id, advertiser_id, date, balance, grant_balance, non_grant_balance, cash_cost, cost, frozen, income, reward_cost, shared_wallet_cost, company_wallet_cost, transfer_in, transfer_out, gmt_create, gmt_modified from tb_agt_advertiser_fund_daily_stat
    </sql>

    <select id="selectAgtAdvertiserFundDailyStatList" parameterType="com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity" resultMap="AgtAdvertiserFundDailyStatResult">
        <include refid="selectAgtAdvertiserFundDailyStatVo"/>
        <where>
                        <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
                        <if test="date != null "> and date = #{date}</if>
                        <if test="balance != null "> and balance = #{balance}</if>
                        <if test="grantBalance != null "> and grant_balance = #{grantBalance}</if>
                        <if test="nonGrantBalance != null "> and non_grant_balance = #{nonGrantBalance}</if>
                        <if test="cashCost != null "> and cash_cost = #{cashCost}</if>
                        <if test="cost != null "> and cost = #{cost}</if>
                        <if test="frozen != null "> and frozen = #{frozen}</if>
                        <if test="income != null "> and income = #{income}</if>
                        <if test="rewardCost != null "> and reward_cost = #{rewardCost}</if>
                        <if test="sharedWalletCost != null "> and shared_wallet_cost = #{sharedWalletCost}</if>
                        <if test="companyWalletCost != null "> and company_wallet_cost = #{companyWalletCost}</if>
                        <if test="transferIn != null "> and transfer_in = #{transferIn}</if>
                        <if test="transferOut != null "> and transfer_out = #{transferOut}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtAdvertiserFundDailyStatById" parameterType="Long" resultMap="AgtAdvertiserFundDailyStatResult">
            <include refid="selectAgtAdvertiserFundDailyStatVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtAdvertiserFundDailyStat" parameterType="com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_advertiser_fund_daily_stat
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
                    <if test="date != null">date,</if>
                    <if test="balance != null">balance,</if>
                    <if test="grantBalance != null">grant_balance,</if>
                    <if test="nonGrantBalance != null">non_grant_balance,</if>
                    <if test="cashCost != null">cash_cost,</if>
                    <if test="cost != null">cost,</if>
                    <if test="frozen != null">frozen,</if>
                    <if test="income != null">income,</if>
                    <if test="rewardCost != null">reward_cost,</if>
                    <if test="sharedWalletCost != null">shared_wallet_cost,</if>
                    <if test="companyWalletCost != null">company_wallet_cost,</if>
                    <if test="transferIn != null">transfer_in,</if>
                    <if test="transferOut != null">transfer_out,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
                    <if test="date != null">#{date},</if>
                    <if test="balance != null">#{balance},</if>
                    <if test="grantBalance != null">#{grantBalance},</if>
                    <if test="nonGrantBalance != null">#{nonGrantBalance},</if>
                    <if test="cashCost != null">#{cashCost},</if>
                    <if test="cost != null">#{cost},</if>
                    <if test="frozen != null">#{frozen},</if>
                    <if test="income != null">#{income},</if>
                    <if test="rewardCost != null">#{rewardCost},</if>
                    <if test="sharedWalletCost != null">#{sharedWalletCost},</if>
                    <if test="companyWalletCost != null">#{companyWalletCost},</if>
                    <if test="transferIn != null">#{transferIn},</if>
                    <if test="transferOut != null">#{transferOut},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtAdvertiserFundDailyStat" parameterType="com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity">
        update tb_agt_advertiser_fund_daily_stat
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
                    <if test="date != null">date = #{date},</if>
                    <if test="balance != null">balance = #{balance},</if>
                    <if test="grantBalance != null">grant_balance = #{grantBalance},</if>
                    <if test="nonGrantBalance != null">non_grant_balance = #{nonGrantBalance},</if>
                    <if test="cashCost != null">cash_cost = #{cashCost},</if>
                    <if test="cost != null">cost = #{cost},</if>
                    <if test="frozen != null">frozen = #{frozen},</if>
                    <if test="income != null">income = #{income},</if>
                    <if test="rewardCost != null">reward_cost = #{rewardCost},</if>
                    <if test="sharedWalletCost != null">shared_wallet_cost = #{sharedWalletCost},</if>
                    <if test="companyWalletCost != null">company_wallet_cost = #{companyWalletCost},</if>
                    <if test="transferIn != null">transfer_in = #{transferIn},</if>
                    <if test="transferOut != null">transfer_out = #{transferOut},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtAdvertiserFundDailyStatById" parameterType="Long">
        delete from tb_agt_advertiser_fund_daily_stat where id = #{id}
    </delete>

    <delete id="deleteAgtAdvertiserFundDailyStatByIds" parameterType="String">
        delete from tb_agt_advertiser_fund_daily_stat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>