<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fundStat.AgtAdvertiserFundDailyStatSelfMapper">

    <resultMap type="com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity"
               id="AgtAdvertiserFundDailyStatResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="date" column="date"/>
        <result property="balance" column="balance"/>
        <result property="grantBalance" column="grant_balance"/>
        <result property="nonGrantBalance" column="non_grant_balance"/>
        <result property="cashCost" column="cash_cost"/>
        <result property="cost" column="cost"/>
        <result property="frozen" column="frozen"/>
        <result property="income" column="income"/>
        <result property="rewardCost" column="reward_cost"/>
        <result property="sharedWalletCost" column="shared_wallet_cost"/>
        <result property="companyWalletCost" column="company_wallet_cost"/>
        <result property="transferIn" column="transfer_in"/>
        <result property="transferOut" column="transfer_out"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectAgtAdvertiserFundDailyStatVo">
        select id, advertiser_id, date, balance, grant_balance, non_grant_balance, cash_cost, cost, frozen, income,
        reward_cost, shared_wallet_cost, company_wallet_cost, transfer_in, transfer_out, gmt_create, gmt_modified from
        tb_agt_advertiser_fund_daily_stat
    </sql>


    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_advertiser_fund_daily_stat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].date != null">date,</if>
            <if test="list[0].balance != null">balance,</if>
            <if test="list[0].grantBalance != null">grant_balance,</if>
            <if test="list[0].nonGrantBalance != null">non_grant_balance,</if>
            <if test="list[0].cashCost != null">cash_cost,</if>
            <if test="list[0].cost != null">cost,</if>
            <if test="list[0].frozen != null">frozen,</if>
            <if test="list[0].income != null">income,</if>
            <if test="list[0].rewardCost != null">reward_cost,</if>
            <if test="list[0].sharedWalletCost != null">shared_wallet_cost,</if>
            <if test="list[0].companyWalletCost != null">company_wallet_cost,</if>
            <if test="list[0].transferIn != null">transfer_in,</if>
            <if test="list[0].transferOut != null">transfer_out,</if>
            <if test="list[0].gmtCreate != null">gmt_create,</if>
            <if test="list[0].gmtModified != null">gmt_modified,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.date != null">#{entity.date},</if>
                <if test="entity.balance != null">#{entity.balance},</if>
                <if test="entity.grantBalance != null">#{entity.grantBalance},</if>
                <if test="entity.nonGrantBalance != null">#{entity.nonGrantBalance},</if>
                <if test="entity.cashCost != null">#{entity.cashCost},</if>
                <if test="entity.cost != null">#{entity.cost},</if>
                <if test="entity.frozen != null">#{entity.frozen},</if>
                <if test="entity.income != null">#{entity.income},</if>
                <if test="entity.rewardCost != null">#{entity.rewardCost},</if>
                <if test="entity.sharedWalletCost != null">#{entity.sharedWalletCost},</if>
                <if test="entity.companyWalletCost != null">#{entity.companyWalletCost},</if>
                <if test="entity.transferIn != null">#{entity.transferIn},</if>
                <if test="entity.transferOut != null">#{entity.transferOut},</if>
                <if test="entity.gmtCreate != null">#{entity.gmtCreate},</if>
                <if test="entity.gmtModified != null">#{entity.gmtModified},</if>
            </trim>
        </foreach>
        on duplicate key update
        balance = VALUES(balance),
        grant_balance = VALUES(grant_balance),
        non_grant_balance = VALUES(non_grant_balance),
        cash_cost = VALUES(cash_cost),
        cost = VALUES(cost),
        frozen = VALUES(frozen),
        income = VALUES(income),
        reward_cost = VALUES(reward_cost),
        shared_wallet_cost = VALUES(shared_wallet_cost),
        company_wallet_cost = VALUES(company_wallet_cost),
        transfer_in = VALUES(transfer_in),
        transfer_out = VALUES(transfer_out)
    </insert>
    <select id="selectFundDailyStatList"
            resultType="com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity">
        SELECT
        SUM( balance ) AS balance,
        SUM( grant_balance ) AS grant_balance,
        SUM( reward_cost ) AS reward_cost ,
        Date(date) as date
        FROM
        tb_agt_advertiser_fund_daily_stat
        WHERE
        date BETWEEN #{startDate} AND #{endDate}
        GROUP BY date
    </select>

</mapper>