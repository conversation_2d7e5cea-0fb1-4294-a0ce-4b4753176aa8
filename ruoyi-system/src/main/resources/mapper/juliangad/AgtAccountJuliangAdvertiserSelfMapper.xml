<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserSelfMapper">

    <resultMap type="com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity"
               id="AgtAccountJuliangAdvertiserResult">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="advertiserName" column="advertiser_name"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="supplierNameShort" column="supplier_name_short"/>
        <result property="playletName" column="playlet_name"/>
        <result property="adOthers" column="ad_others"/>
        <result property="platformDyWx" column="platform_dy_wx"/>
        <result property="remarks" column="remarks"/>
        <result property="regStatus" column="reg_status"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>


    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        insert into
        tb_agt_account_juliang_advertiser(`account_id`, `account_name`, `advertiser_id`,`advertiser_name`,
        `user_nickname`,
        `playlet_name`, `supplier_name_short`, `ad_others`,`platform_dy_wx`, `reg_status`, `remarks`)
        values
        <foreach collection="list" separator="," item="entity">
            (
            #{entity.accountId},#{entity.accountName},#{entity.advertiserId},#{entity.advertiserName},
            #{entity.userNickname},#{entity.playletName},#{entity.supplierNameShort},#{entity.adOthers}
            ,#{entity.platformDyWx},#{entity.regStatus},#{entity.remarks}
            )
        </foreach>
        on duplicate key update
        account_id = VALUES(account_id),
        account_name = VALUES(account_name),
        advertiser_id = VALUES(advertiser_id),
        advertiser_name = VALUES(advertiser_name),
        user_nickname = VALUES(user_nickname),
        playlet_name = VALUES(playlet_name),
        supplier_name_short = VALUES(supplier_name_short),
        ad_others = VALUES(ad_others),
        platform_dy_wx = VALUES(platform_dy_wx),
        reg_status = VALUES(reg_status),
        remarks = VALUES(remarks)
    </insert>


    <select id="getAdvertiserToken" resultType="java.lang.String">
        SELECT access_token FROM tb_agt_account_juliang WHERE account_id=(SELECT account_id FROM
        tb_agt_account_juliang_advertiser WHERE advertiser_id=#{advertiserId})
    </select>

    <select id="getAdvertiserTokenEntity" resultType="com.ruoyi.system.entity.juliangad.AdvertiserTokenEntity">
        SELECT
        ad.account_id,
        ad.advertiser_id,
        ac.access_token
        FROM
        tb_agt_account_juliang_advertiser AS ad
        LEFT JOIN tb_agt_account_juliang AS ac ON ad.account_id = ac.account_id WHERE advertiser_id=#{advertiserId}
    </select>

</mapper>