<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserMapper">

    <resultMap type="com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity" id="AgtAccountJuliangAdvertiserResult">
            <result property="id"    column="id"    />
            <result property="accountId"    column="account_id"    />
            <result property="accountName"    column="account_name"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="advertiserName"    column="advertiser_name"    />
            <result property="userNickname"    column="user_nickname"    />
            <result property="userRealname"    column="user_realname"    />
            <result property="supplierNameShort"    column="supplier_name_short"    />
            <result property="playletName"    column="playlet_name"    />
            <result property="adOthers"    column="ad_others"    />
            <result property="platformDyWx"    column="platform_dy_wx"    />
            <result property="remarks"    column="remarks"    />
            <result property="regStatus"    column="reg_status"    />
            <result property="costStatus"    column="cost_status"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtAccountJuliangAdvertiserVo">
        select id, account_id, account_name, advertiser_id, advertiser_name, user_nickname, user_realname, supplier_name_short, playlet_name, ad_others, platform_dy_wx, remarks, reg_status, cost_status, gmt_create, gmt_modified from tb_agt_account_juliang_advertiser
    </sql>

    <select id="selectAgtAccountJuliangAdvertiserList" parameterType="com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity" resultMap="AgtAccountJuliangAdvertiserResult">
        <include refid="selectAgtAccountJuliangAdvertiserVo"/>
        <where>
                        <if test="accountId != null "> and account_id = #{accountId}</if>
                        <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
                        <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
                        <if test="advertiserName != null  and advertiserName != ''"> and advertiser_name like concat('%', #{advertiserName}, '%')</if>
                        <if test="userNickname != null  and userNickname != ''"> and user_nickname like concat('%', #{userNickname}, '%')</if>
                        <if test="userRealname != null  and userRealname != ''"> and user_realname like concat('%', #{userRealname}, '%')</if>
                        <if test="supplierNameShort != null  and supplierNameShort != ''"> and supplier_name_short = #{supplierNameShort}</if>
                        <if test="playletName != null  and playletName != ''"> and playlet_name like concat('%', #{playletName}, '%')</if>
                        <if test="adOthers != null  and adOthers != ''"> and ad_others = #{adOthers}</if>
                        <if test="platformDyWx != null "> and platform_dy_wx = #{platformDyWx}</if>
                        <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
                        <if test="regStatus != null "> and reg_status = #{regStatus}</if>
                        <if test="costStatus != null "> and cost_status = #{costStatus}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtAccountJuliangAdvertiserById" parameterType="String" resultMap="AgtAccountJuliangAdvertiserResult">
            <include refid="selectAgtAccountJuliangAdvertiserVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtAccountJuliangAdvertiser" parameterType="com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_account_juliang_advertiser
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">account_id,</if>
                    <if test="accountName != null">account_name,</if>
                    <if test="advertiserId != null">advertiser_id,</if>
                    <if test="advertiserName != null and advertiserName != ''">advertiser_name,</if>
                    <if test="userNickname != null and userNickname != ''">user_nickname,</if>
                    <if test="userRealname != null">user_realname,</if>
                    <if test="supplierNameShort != null and supplierNameShort != ''">supplier_name_short,</if>
                    <if test="playletName != null">playlet_name,</if>
                    <if test="adOthers != null">ad_others,</if>
                    <if test="platformDyWx != null">platform_dy_wx,</if>
                    <if test="remarks != null">remarks,</if>
                    <if test="regStatus != null">reg_status,</if>
                    <if test="costStatus != null">cost_status,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">#{accountId},</if>
                    <if test="accountName != null">#{accountName},</if>
                    <if test="advertiserId != null">#{advertiserId},</if>
                    <if test="advertiserName != null and advertiserName != ''">#{advertiserName},</if>
                    <if test="userNickname != null and userNickname != ''">#{userNickname},</if>
                    <if test="userRealname != null">#{userRealname},</if>
                    <if test="supplierNameShort != null and supplierNameShort != ''">#{supplierNameShort},</if>
                    <if test="playletName != null">#{playletName},</if>
                    <if test="adOthers != null">#{adOthers},</if>
                    <if test="platformDyWx != null">#{platformDyWx},</if>
                    <if test="remarks != null">#{remarks},</if>
                    <if test="regStatus != null">#{regStatus},</if>
                    <if test="costStatus != null">#{costStatus},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtAccountJuliangAdvertiser" parameterType="com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity">
        update tb_agt_account_juliang_advertiser
        <trim prefix="SET" suffixOverrides=",">
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="accountName != null">account_name = #{accountName},</if>
                    <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
                    <if test="advertiserName != null and advertiserName != ''">advertiser_name = #{advertiserName},</if>
                    <if test="userNickname != null and userNickname != ''">user_nickname = #{userNickname},</if>
                    <if test="userRealname != null">user_realname = #{userRealname},</if>
                    <if test="supplierNameShort != null and supplierNameShort != ''">supplier_name_short = #{supplierNameShort},</if>
                    <if test="playletName != null">playlet_name = #{playletName},</if>
                    <if test="adOthers != null">ad_others = #{adOthers},</if>
                    <if test="platformDyWx != null">platform_dy_wx = #{platformDyWx},</if>
                    <if test="remarks != null">remarks = #{remarks},</if>
                    <if test="regStatus != null">reg_status = #{regStatus},</if>
                    <if test="costStatus != null">cost_status = #{costStatus},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtAccountJuliangAdvertiserById" parameterType="String">
        delete from tb_agt_account_juliang_advertiser where id = #{id}
    </delete>

    <delete id="deleteAgtAccountJuliangAdvertiserByIds" parameterType="String">
        delete from tb_agt_account_juliang_advertiser where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectByAdvertiserName" parameterType="String" resultMap="AgtAccountJuliangAdvertiserResult">
        <include refid="selectAgtAccountJuliangAdvertiserVo"/>
        where advertiser_name like concat('%', #{advertiserName}, '%')
    </select>
</mapper>
