<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.download.DownloadCenterTaskMapper">

    <resultMap type="com.ruoyi.system.entity.download.DownloadCenterTaskEntity" id="DownloadCenterTaskResult">
            <result property="id"    column="id"    />
            <result property="taskName"    column="task_name"    />
            <result property="taskStatus"    column="task_status"    />
            <result property="fileUrl"    column="file_url"    />
            <result property="operatorId"    column="operator_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectDownloadCenterTaskVo">
        select id, task_name, task_status, file_url, operator_id, gmt_create, gmt_modified from tb_download_center_task
    </sql>

    <select id="selectDownloadCenterTaskList" parameterType="com.ruoyi.system.entity.download.DownloadCenterTaskEntity" resultMap="DownloadCenterTaskResult">
        <include refid="selectDownloadCenterTaskVo"/>
        <where>
                        <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
                        <if test="taskStatus != null "> and task_status = #{taskStatus}</if>
                        <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
                        <if test="operatorId != null "> and operator_id = #{operatorId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectDownloadCenterTaskById" parameterType="Integer" resultMap="DownloadCenterTaskResult">
            <include refid="selectDownloadCenterTaskVo"/>
            where id = #{id}
    </select>

    <insert id="insertDownloadCenterTask" parameterType="com.ruoyi.system.entity.download.DownloadCenterTaskEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_download_center_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="taskName != null">task_name,</if>
                    <if test="taskStatus != null">task_status,</if>
                    <if test="fileUrl != null">file_url,</if>
                    <if test="operatorId != null">operator_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="taskName != null">#{taskName},</if>
                    <if test="taskStatus != null">#{taskStatus},</if>
                    <if test="fileUrl != null">#{fileUrl},</if>
                    <if test="operatorId != null">#{operatorId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateDownloadCenterTask" parameterType="com.ruoyi.system.entity.download.DownloadCenterTaskEntity">
        update tb_download_center_task
        <trim prefix="SET" suffixOverrides=",">
                    <if test="taskName != null">task_name = #{taskName},</if>
                    <if test="taskStatus != null">task_status = #{taskStatus},</if>
                    <if test="fileUrl != null">file_url = #{fileUrl},</if>
                    <if test="operatorId != null">operator_id = #{operatorId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDownloadCenterTaskById" parameterType="Integer">
        delete from tb_download_center_task where id = #{id}
    </delete>

    <delete id="deleteDownloadCenterTaskByIds" parameterType="String">
        delete from tb_download_center_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>