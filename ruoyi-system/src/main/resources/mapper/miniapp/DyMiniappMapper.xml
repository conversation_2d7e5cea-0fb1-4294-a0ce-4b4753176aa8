<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.miniapp.DyMiniappMapper">

    <resultMap type="com.ruoyi.system.entity.miniapp.DyMiniappEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="appId" column="app_id"/>
            <result property="title" column="title"/>
            <result property="clientKey" column="client_key"/>
            <result property="clientSecret" column="client_secret"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            app_id,
            title,
            client_key,
            client_secret,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.miniapp.DyMiniappEntity">
        INSERT INTO tb_dy_miniapp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                app_id,
            </if>
            <if test="clientKey != null">
                client_key,
            </if>
            <if test="clientSecret != null">
                client_secret,
            </if>
            <if test="gmtModified != null">
                gmt_modified
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                #{appId},
            </if>
            <if test="clientKey != null">
                #{clientKey},
            </if>
            <if test="clientSecret != null">
                #{clientSecret},
            </if>
            <if test="gmtModified != null">
                #{gmtModified}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_dy_miniapp WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.miniapp.DyMiniappEntity">
        UPDATE tb_dy_miniapp
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="clientKey != null">
                client_key = #{clientKey},
            </if>
            <if test="clientSecret != null">
                client_secret = #{clientSecret},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_dy_miniapp
        WHERE id = #{id}
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_dy_miniapp
    </select>
    <select id="selectByAppId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_dy_miniapp
        where app_id = #{appId}
    </select>
    <select id="selectAppIdList" resultType="java.lang.String">
        select app_id
        from tb_dy_miniapp
        where is_deleted = 0
    </select>

</mapper>