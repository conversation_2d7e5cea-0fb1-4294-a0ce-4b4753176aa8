<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.material.AgtDataMaterialMapper">

    <resultMap type="com.ruoyi.system.entity.material.AgtDataMaterialEntity" id="AgtDataMaterialResult">
        <result property="id" column="id"/>
        <result property="materialId" column="material_id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="advertiserName" column="advertiser_name"/>
        <result property="supplierNameShort" column="supplier_name_short"/>
        <result property="imgUrl" column="img_url"/>
        <result property="imageMode" column="image_mode"/>
        <result property="adOthers" column="ad_others"/>
        <result property="platformDyWx" column="platform_dy_wx"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="userRealname" column="user_realname"/>
        <result property="adPlatformMaterialName" column="ad_platform_material_name"/>
        <result property="videoEditor" column="video_editor"/>
        <result property="playletName" column="playlet_name"/>
        <result property="materialCreateTime" column="material_create_time"/>
        <result property="videoCreationType" column="video_creation_type"/>
        <result property="serialNo" column="serial_no"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectAgtDataMaterialVo">
        select id, material_id, advertiser_id, advertiser_name, supplier_name_short, img_url, image_mode, ad_others,
        platform_dy_wx, user_nickname, user_realname, ad_platform_material_name, video_editor, playlet_name,
        material_create_time, video_creation_type, serial_no, gmt_create, gmt_modified from tb_agt_data_material
    </sql>

    <select id="selectAgtDataMaterialList" parameterType="com.ruoyi.system.entity.material.AgtDataMaterialEntity"
            resultMap="AgtDataMaterialResult">
        <include refid="selectAgtDataMaterialVo"/>
        <where>
            <if test="materialId != null  and materialId != ''">and material_id = #{materialId}</if>
            <if test="advertiserId != null  and advertiserId != ''">and advertiser_id = #{advertiserId}</if>
            <if test="advertiserName != null  and advertiserName != ''">and advertiser_name like concat('%',
                #{advertiserName}, '%')
            </if>
            <if test="supplierNameShort != null  and supplierNameShort != ''">and supplier_name_short =
                #{supplierNameShort}
            </if>
            <if test="imgUrl != null  and imgUrl != ''">and img_url = #{imgUrl}</if>
            <if test="imageMode != null  and imageMode != ''">and image_mode = #{imageMode}</if>
            <if test="adOthers != null  and adOthers != ''">and ad_others = #{adOthers}</if>
            <if test="platformDyWx != null ">and platform_dy_wx = #{platformDyWx}</if>
            <if test="userNickname != null  and userNickname != ''">and user_nickname like concat('%', #{userNickname},
                '%')
            </if>
            <if test="userRealname != null  and userRealname != ''">and user_realname like concat('%', #{userRealname},
                '%')
            </if>
            <if test="adPlatformMaterialName != null  and adPlatformMaterialName != ''">and ad_platform_material_name
                like concat('%', #{adPlatformMaterialName}, '%')
            </if>
            <if test="videoEditor != null  and videoEditor != ''">and video_editor = #{videoEditor}</if>
            <if test="playletName != null  and playletName != ''">and playlet_name like concat('%', #{playletName},
                '%')
            </if>
            <if test="materialCreateTime != null  and materialCreateTime != ''">and material_create_time =
                #{materialCreateTime}
            </if>
            <if test="videoCreationType != null  and videoCreationType != ''">and video_creation_type =
                #{videoCreationType}
            </if>
            <if test="serialNo != null  and serialNo != ''">and serial_no = #{serialNo}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataMaterialById" parameterType="Long" resultMap="AgtDataMaterialResult">
        <include refid="selectAgtDataMaterialVo"/>
        where id = #{id}
    </select>

    <insert id="insertAgtDataMaterial" parameterType="com.ruoyi.system.entity.material.AgtDataMaterialEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_data_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialId != null and materialId != ''">material_id,</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
            <if test="advertiserName != null">advertiser_name,</if>
            <if test="supplierNameShort != null">supplier_name_short,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="imageMode != null">image_mode,</if>
            <if test="adOthers != null">ad_others,</if>
            <if test="platformDyWx != null">platform_dy_wx,</if>
            <if test="userNickname != null">user_nickname,</if>
            <if test="userRealname != null">user_realname,</if>
            <if test="adPlatformMaterialName != null">ad_platform_material_name,</if>
            <if test="videoEditor != null">video_editor,</if>
            <if test="playletName != null">playlet_name,</if>
            <if test="materialCreateTime != null">material_create_time,</if>
            <if test="videoCreationType != null">video_creation_type,</if>
            <if test="serialNo != null">serial_no,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialId != null and materialId != ''">#{materialId},</if>
            <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
            <if test="advertiserName != null">#{advertiserName},</if>
            <if test="supplierNameShort != null">#{supplierNameShort},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="imageMode != null">#{imageMode},</if>
            <if test="adOthers != null">#{adOthers},</if>
            <if test="platformDyWx != null">#{platformDyWx},</if>
            <if test="userNickname != null">#{userNickname},</if>
            <if test="userRealname != null">#{userRealname},</if>
            <if test="adPlatformMaterialName != null">#{adPlatformMaterialName},</if>
            <if test="videoEditor != null">#{videoEditor},</if>
            <if test="playletName != null">#{playletName},</if>
            <if test="materialCreateTime != null">#{materialCreateTime},</if>
            <if test="videoCreationType != null">#{videoCreationType},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataMaterial" parameterType="com.ruoyi.system.entity.material.AgtDataMaterialEntity">
        update tb_agt_data_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialId != null and materialId != ''">material_id = #{materialId},</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
            <if test="advertiserName != null">advertiser_name = #{advertiserName},</if>
            <if test="supplierNameShort != null">supplier_name_short = #{supplierNameShort},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="imageMode != null">image_mode = #{imageMode},</if>
            <if test="adOthers != null">ad_others = #{adOthers},</if>
            <if test="platformDyWx != null">platform_dy_wx = #{platformDyWx},</if>
            <if test="userNickname != null">user_nickname = #{userNickname},</if>
            <if test="userRealname != null">user_realname = #{userRealname},</if>
            <if test="adPlatformMaterialName != null">ad_platform_material_name = #{adPlatformMaterialName},</if>
            <if test="videoEditor != null">video_editor = #{videoEditor},</if>
            <if test="playletName != null">playlet_name = #{playletName},</if>
            <if test="materialCreateTime != null">material_create_time = #{materialCreateTime},</if>
            <if test="videoCreationType != null">video_creation_type = #{videoCreationType},</if>
            <if test="serialNo != null">serial_no = #{serialNo},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataMaterialById" parameterType="Long">
        delete from tb_agt_data_material where id = #{id}
    </delete>

    <delete id="deleteAgtDataMaterialByIds" parameterType="String">
        delete from tb_agt_data_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>