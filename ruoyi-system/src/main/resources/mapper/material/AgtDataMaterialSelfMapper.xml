<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.material.AgtDataMaterialSelfMapper">

    <resultMap type="com.ruoyi.system.entity.material.AgtDataMaterialEntity" id="AgtDataMaterialResult">
        <result property="id" column="id"/>
        <result property="materialId" column="material_id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="advertiserName" column="advertiser_name"/>
        <result property="supplierNameShort" column="supplier_name_short"/>
        <result property="imgUrl" column="img_url"/>
        <result property="imageMode" column="image_mode"/>
        <result property="adOthers" column="ad_others"/>
        <result property="platformDyWx" column="platform_dy_wx"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="userRealname" column="user_realname"/>
        <result property="adPlatformMaterialName" column="ad_platform_material_name"/>
        <result property="videoEditor" column="video_editor"/>
        <result property="playletName" column="playlet_name"/>
        <result property="materialCreateTime" column="material_create_time"/>
        <result property="videoCreationType" column="video_creation_type"/>
        <result property="serialNo" column="serial_no"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <resultMap id="MaterialDetailResultMap" type="com.ruoyi.system.vo.playlet.roiall.MaterialDetail">
        <result property="advertiserId" column="advertiser_id"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="userRealname" column="user_realname"/>
        <result property="supplierNameShort" column="supplier_name_short"/>
        <result property="adOthers" column="ad_others"/>
        <result property="platformDyWx" column="platform_dy_wx"/>
        <result property="materialId" column="material_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="imgUrl" column="img_url"/>
        <result property="imageMode" column="image_mode"/>
        <result property="adPlatformMaterialName" column="ad_platform_material_name"/>
        <result property="videoEditor" column="video_editor"/>
        <result property="playletName" column="playlet_name"/>
        <result property="materialCreateTime" column="material_create_time"/>
        <result property="videoCreationType" column="video_creation_type"/>
        <result property="serialNo" column="serial_no"/>
        <result property="statCost" column="stat_cost"/>
        <result property="showCnt" column="show_cnt"/>
        <result property="cpmPlatform" column="cpm_platform"/>
        <result property="clickCnt" column="click_cnt"/>
        <result property="cpcPlatform" column="cpc_platform"/>
        <result property="ctr" column="ctr"/>
        <result property="convertCnt" column="convert_cnt"/>
        <result property="conversionRate" column="conversion_rate"/>
        <result property="playDuration3s" column="play_duration_3s"/>
        <result property="playDuration3sRate" column="play_duration_3s_rate"/>
        <result property="validPlay" column="valid_play"/>
        <result property="totalPlay" column="total_play"/>
        <result property="validPlayRate" column="valid_play_rate"/>
        <result property="playOverRate" column="play_over_rate"/>
        <result property="playOverCnt" column="play_over_cnt"/>
        <result property="activeCnt" column="active_cnt"/>
        <result property="activeCost" column="active_cost"/>
        <result property="payRatio" column="pay_ratio"/>
        <result property="dyLike" column="dy_like"/>
        <result property="dyShare" column="dy_share"/>
        <result property="dyComment" column="dy_comment"/>
        <result property="dyFollow" column="dy_follow"/>
        <result property="dislikeCnt" column="dislike_cnt"/>
        <result property="reportCnt" column="report_cnt"/>
        <result property="dyHomeVisited" column="dy_home_visited"/>
    </resultMap>

    <sql id="selectAgtDataMaterialVo">
        select id, material_id, advertiser_id, advertiser_name, supplier_name_short,img_url, image_mode, ad_others,
        platform_dy_wx, user_nickname, user_realname, ad_platform_material_name, video_editor, playlet_name,
        material_create_time, video_creation_type, serial_no from tb_agt_data_material
    </sql>

    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_data_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].materialId != null">material_id,</if>
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].advertiserName != null">advertiser_name,</if>
            <if test="list[0].supplierNameShort != null">supplier_name_short,</if>
            <if test="list[0].imgUrl != null">img_url,</if>
            <if test="list[0].imageMode != null">image_mode,</if>
            <if test="list[0].adOthers != null">ad_others,</if>
            <if test="list[0].platformDyWx != null">platform_dy_wx,</if>
            <if test="list[0].userNickname != null">user_nickname,</if>
            <if test="list[0].userRealname != null">user_realname,</if>
            <if test="list[0].adPlatformMaterialName != null">ad_platform_material_name,</if>
            <if test="list[0].videoEditor != null">video_editor,</if>
            <if test="list[0].playletName != null">playlet_name,</if>
            <if test="list[0].materialCreateTime != null">material_create_time,</if>
            <if test="list[0].videoCreationType != null">video_creation_type,</if>
            <if test="list[0].serialNo != null">serial_no,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.materialId != null">#{entity.materialId},</if>
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.advertiserName != null">#{entity.advertiserName},</if>
                <if test="entity.supplierNameShort != null">#{entity.supplierNameShort},</if>
                <if test="entity.imgUrl != null">#{entity.imgUrl},</if>
                <if test="entity.imageMode != null">#{entity.imageMode},</if>
                <if test="entity.adOthers != null">#{entity.adOthers},</if>
                <if test="entity.platformDyWx != null">#{entity.platformDyWx},</if>
                <if test="entity.userNickname != null">#{entity.userNickname},</if>
                <if test="entity.userRealname != null">#{entity.userRealname},</if>
                <if test="entity.adPlatformMaterialName != null">#{entity.adPlatformMaterialName},</if>
                <if test="entity.videoEditor != null">#{entity.videoEditor},</if>
                <if test="entity.playletName != null">#{entity.playletName},</if>
                <if test="entity.materialCreateTime != null">#{entity.materialCreateTime},</if>
                <if test="entity.videoCreationType != null">#{entity.videoCreationType},</if>
                <if test="entity.serialNo != null">#{entity.serialNo},</if>
            </trim>
        </foreach>
        on duplicate key update
        advertiser_name = VALUES(advertiser_name),
        supplier_name_short = VALUES(supplier_name_short),
        img_url = VALUES(img_url),
        image_mode = VALUES(image_mode),
        ad_others = VALUES(ad_others),
        platform_dy_wx = VALUES(platform_dy_wx),
        user_nickname = VALUES(user_nickname),
        user_realname = VALUES(user_realname),
        ad_platform_material_name = VALUES(ad_platform_material_name),
        video_editor = VALUES(video_editor),
        playlet_name = VALUES(playlet_name),
        material_create_time = VALUES(material_create_time),
        video_creation_type = VALUES(video_creation_type),
        serial_no = VALUES(serial_no)
    </insert>


    <select id="selectMaterial" resultMap="MaterialDetailResultMap">
        SELECT
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                <choose>
                    <when test="column == 'cur_date'">
                        metrics.${column}
                    </when>
                    <when test="column == 'advertiser_id'">
                        material.${column}, material.advertiser_name
                    </when>
                    <otherwise>
                        material.${column}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        SUM(stat_cost) AS stat_cost,
        SUM(show_cnt) AS show_cnt,
        SUM(click_cnt) AS click_cnt,
        SUM(convert_cnt) AS convert_cnt,
        SUM(play_duration_3s) AS play_duration_3s,
        SUM(valid_play) AS valid_play,
        SUM(total_play) AS total_play,
        SUM(play_over_cnt) AS play_over_cnt,
        SUM(dy_like) AS dy_like,
        SUM(dy_share) AS dy_share,
        SUM(dy_comment) AS dy_comment,
        SUM(dy_follow) AS dy_follow,
        SUM(dislike_cnt) AS dislike_cnt,
        SUM(report_cnt) AS report_cnt,
        SUM(dy_home_visited) AS dy_home_visited,
        SUM(active_cnt) AS active_cnt,
        ROUND( CASE WHEN SUM(show_cnt) = 0 THEN 0 ELSE (SUM(stat_cost)*1000) / SUM(show_cnt) END, 4) AS cpm_platform,
        ROUND( CASE WHEN SUM(show_cnt) = 0 THEN 0 ELSE SUM(click_cnt) / SUM(show_cnt) END, 4) AS ctr,
        ROUND( CASE WHEN SUM(show_cnt) = 0 THEN 0 ELSE SUM(play_duration_3s) / SUM(show_cnt) END, 4) AS
        play_duration_3s_rate,
        ROUND( CASE WHEN SUM(show_cnt) = 0 THEN 0 ELSE SUM(valid_play) / SUM(show_cnt) END, 4) AS valid_play_rate,
        ROUND( CASE WHEN SUM(click_cnt) = 0 THEN 0 ELSE SUM(stat_cost) / SUM(click_cnt) END, 4) AS cpc_platform,
        ROUND( CASE WHEN SUM(click_cnt) = 0 THEN 0 ELSE SUM(convert_cnt) / SUM(click_cnt) END, 4) AS conversion_rate,
        ROUND( CASE WHEN SUM(total_play) = 0 THEN 0 ELSE SUM(play_over_cnt) / SUM(total_play) END, 4) AS play_over_rate,
        ROUND( CASE WHEN SUM(active_cnt) = 0 THEN 0 ELSE SUM(stat_cost) / SUM(active_cnt) END, 4) AS active_cost,
        ROUND( CASE WHEN SUM(active_cnt) = 0 THEN 0 ELSE SUM(convert_cnt) / SUM(active_cnt) END, 4) AS pay_ratio
        FROM (
        <include refid="selectAgtDataMaterialVo"/>
        <where>
            user_nickname != ""
            <if test="filterAdvertiserId != null and filterAdvertiserId.size() > 0">
                AND advertiser_id IN
                <foreach item="item" index="index" collection="filterAdvertiserId" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="filterSupplierName != null and filterSupplierName.size() > 0">
                AND supplier_name_short IN
                <foreach item="item" index="index" collection="filterSupplierName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="filterUserNickname != null and filterUserNickname.size() > 0">
                AND user_nickname IN
                <foreach item="item" index="index" collection="filterUserNickname" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="filterVideoEditor != null and filterVideoEditor.size() > 0">
                AND video_editor IN
                <foreach item="item" index="index" collection="filterVideoEditor" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="filterMaterialName != null">
                AND ad_platform_material_name like concat('%', #{filterMaterialName}, '%')
            </if>
        </where>
        ) AS material
        left join tb_agt_data_material_metrics AS metrics
        ON material.material_id = metrics.material_id AND material.advertiser_id = metrics.advertiser_id
        WHERE cur_date BETWEEN #{filterStartDate} AND #{filterEndDate}
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                <choose>
                    <when test="column == 'cur_date'">
                        ${column}
                    </when>
                    <when test="column == 'advertiser_id'">
                        ${column},material.advertiser_name
                    </when>
                    <otherwise>
                        ${column}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        <if test="orderBy == null or orderBy.size == 0">
            order by
            <if test="dimension != null and dimension.contains('cur_date')">
                cur_date desc,
            </if>
            stat_cost desc
        </if>
        <if test="orderBy != null and orderBy.size > 0">
            order by
            <foreach collection="orderBy" item="column" separator=",">
                ${column.field} ${column.type}
            </foreach>
        </if>


    </select>

    <select id="selectMaterialIdAndAdPlatformMaterialName"
            resultType="com.ruoyi.system.entity.material.AgtDataMaterialEntity">
        SELECT
        t1.material_id,
        t1.img_url,
        t1.ad_platform_material_name
        FROM
        tb_agt_data_material t1
        INNER JOIN ( SELECT material_id, MAX( gmt_modified ) AS max_update_time FROM tb_agt_data_material GROUP BY
        material_id ) t2 ON t1.material_id = t2.material_id
        AND t1.gmt_modified = t2.max_update_time limit 1000;
    </select>

    <select id="selectMaterialName" resultType="com.ruoyi.system.entity.material.AgtDataMaterialEntity">
        SELECT material_id,ad_platform_material_name,img_url,gmt_modified
        from tb_agt_data_material
        WHERE material_id=#{materialId}
        ORDER BY gmt_modified desc limit 1
    </select>
</mapper>