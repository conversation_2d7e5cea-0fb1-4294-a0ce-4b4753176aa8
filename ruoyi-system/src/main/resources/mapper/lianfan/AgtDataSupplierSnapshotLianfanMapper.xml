<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.lianfan.AgtDataSupplierSnapshotLianfanMapper">

    <resultMap type="com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity" id="AgtDataSupplierSnapshotLianfanResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="curHour"    column="cur_hour"    />
            <result property="orderId"    column="order_id"    />
            <result property="orderStatus"    column="order_status"    />
            <result property="productType"    column="product_type"    />
            <result property="createTimeOrder"    column="create_time_order"    />
            <result property="chargeAmount"    column="charge_amount"    />
            <result property="userId"    column="user_id"    />
            <result property="externalOrderId"    column="external_order_id"    />
            <result property="linkId"    column="link_id"    />
            <result property="linkUsername"    column="link_username"    />
            <result property="playletCode"    column="playlet_code"    />
            <result property="playletName"    column="playlet_name"    />
            <result property="miniAppId"    column="mini_app_id"    />
            <result property="miniAppName"    column="mini_app_name"    />
            <result property="refundOrderId"    column="refund_order_id"    />
            <result property="linkDt"    column="link_dt"    />
            <result property="linkSourceChannel"    column="link_source_channel"    />
            <result property="paymentModel"    column="payment_model"    />
            <result property="appType"    column="app_type"    />
            <result property="projectId"    column="project_id"    />
            <result property="promotionId"    column="promotion_id"    />
            <result property="registerTime"    column="register_time"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gtmModified"    column="gtm_modified"    />
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotLianfanVo">
        select id, advertiser_id, cur_date, cur_hour, order_id, order_status, product_type, create_time_order, charge_amount, user_id, external_order_id, link_id, link_username, playlet_code, playlet_name, mini_app_id, mini_app_name, refund_order_id, link_dt, link_source_channel, payment_model, app_type, project_id, promotion_id, register_time, gmt_create, gtm_modified from tb_agt_data_supplier_snapshot_lianfan
    </sql>

    <select id="selectAgtDataSupplierSnapshotLianfanList" parameterType="com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity" resultMap="AgtDataSupplierSnapshotLianfanResult">
        <include refid="selectAgtDataSupplierSnapshotLianfanVo"/>
        <where>
                        <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="curHour != null "> and cur_hour = #{curHour}</if>
                        <if test="orderId != null "> and order_id = #{orderId}</if>
                        <if test="orderStatus != null "> and order_status = #{orderStatus}</if>
                        <if test="productType != null "> and product_type = #{productType}</if>
                        <if test="createTimeOrder != null "> and create_time_order = #{createTimeOrder}</if>
                        <if test="chargeAmount != null "> and charge_amount = #{chargeAmount}</if>
                        <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                        <if test="externalOrderId != null  and externalOrderId != ''"> and external_order_id = #{externalOrderId}</if>
                        <if test="linkId != null "> and link_id = #{linkId}</if>
                        <if test="linkUsername != null  and linkUsername != ''"> and link_username like concat('%', #{linkUsername}, '%')</if>
                        <if test="playletCode != null  and playletCode != ''"> and playlet_code = #{playletCode}</if>
                        <if test="playletName != null  and playletName != ''"> and playlet_name like concat('%', #{playletName}, '%')</if>
                        <if test="miniAppId != null  and miniAppId != ''"> and mini_app_id = #{miniAppId}</if>
                        <if test="miniAppName != null  and miniAppName != ''"> and mini_app_name like concat('%', #{miniAppName}, '%')</if>
                        <if test="refundOrderId != null "> and refund_order_id = #{refundOrderId}</if>
                        <if test="linkDt != null "> and link_dt = #{linkDt}</if>
                        <if test="linkSourceChannel != null  and linkSourceChannel != ''"> and link_source_channel = #{linkSourceChannel}</if>
                        <if test="paymentModel != null  and paymentModel != ''"> and payment_model = #{paymentModel}</if>
                        <if test="appType != null  and appType != ''"> and app_type = #{appType}</if>
                        <if test="projectId != null  and projectId != ''"> and project_id = #{projectId}</if>
                        <if test="promotionId != null  and promotionId != ''"> and promotion_id = #{promotionId}</if>
                        <if test="registerTime != null "> and register_time = #{registerTime}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gtmModified != null "> and gtm_modified = #{gtmModified}</if>
        </where>
    </select>

    <select id="selectAgtDataSupplierSnapshotLianfanById" parameterType="Long" resultMap="AgtDataSupplierSnapshotLianfanResult">
            <include refid="selectAgtDataSupplierSnapshotLianfanVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataSupplierSnapshotLianfan" parameterType="com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity">
        insert into tb_agt_data_supplier_snapshot_lianfan
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="advertiserId != null">advertiser_id,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="curHour != null">cur_hour,</if>
                    <if test="orderId != null">order_id,</if>
                    <if test="orderStatus != null">order_status,</if>
                    <if test="productType != null">product_type,</if>
                    <if test="createTimeOrder != null">create_time_order,</if>
                    <if test="chargeAmount != null">charge_amount,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="externalOrderId != null">external_order_id,</if>
                    <if test="linkId != null">link_id,</if>
                    <if test="linkUsername != null">link_username,</if>
                    <if test="playletCode != null">playlet_code,</if>
                    <if test="playletName != null">playlet_name,</if>
                    <if test="miniAppId != null">mini_app_id,</if>
                    <if test="miniAppName != null">mini_app_name,</if>
                    <if test="refundOrderId != null">refund_order_id,</if>
                    <if test="linkDt != null">link_dt,</if>
                    <if test="linkSourceChannel != null">link_source_channel,</if>
                    <if test="paymentModel != null">payment_model,</if>
                    <if test="appType != null">app_type,</if>
                    <if test="projectId != null">project_id,</if>
                    <if test="promotionId != null">promotion_id,</if>
                    <if test="registerTime != null">register_time,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gtmModified != null">gtm_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="advertiserId != null">#{advertiserId},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="curHour != null">#{curHour},</if>
                    <if test="orderId != null">#{orderId},</if>
                    <if test="orderStatus != null">#{orderStatus},</if>
                    <if test="productType != null">#{productType},</if>
                    <if test="createTimeOrder != null">#{createTimeOrder},</if>
                    <if test="chargeAmount != null">#{chargeAmount},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="externalOrderId != null">#{externalOrderId},</if>
                    <if test="linkId != null">#{linkId},</if>
                    <if test="linkUsername != null">#{linkUsername},</if>
                    <if test="playletCode != null">#{playletCode},</if>
                    <if test="playletName != null">#{playletName},</if>
                    <if test="miniAppId != null">#{miniAppId},</if>
                    <if test="miniAppName != null">#{miniAppName},</if>
                    <if test="refundOrderId != null">#{refundOrderId},</if>
                    <if test="linkDt != null">#{linkDt},</if>
                    <if test="linkSourceChannel != null">#{linkSourceChannel},</if>
                    <if test="paymentModel != null">#{paymentModel},</if>
                    <if test="appType != null">#{appType},</if>
                    <if test="projectId != null">#{projectId},</if>
                    <if test="promotionId != null">#{promotionId},</if>
                    <if test="registerTime != null">#{registerTime},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gtmModified != null">#{gtmModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataSupplierSnapshotLianfan" parameterType="com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity">
        update tb_agt_data_supplier_snapshot_lianfan
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="curHour != null">cur_hour = #{curHour},</if>
                    <if test="orderId != null">order_id = #{orderId},</if>
                    <if test="orderStatus != null">order_status = #{orderStatus},</if>
                    <if test="productType != null">product_type = #{productType},</if>
                    <if test="createTimeOrder != null">create_time_order = #{createTimeOrder},</if>
                    <if test="chargeAmount != null">charge_amount = #{chargeAmount},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="externalOrderId != null">external_order_id = #{externalOrderId},</if>
                    <if test="linkId != null">link_id = #{linkId},</if>
                    <if test="linkUsername != null">link_username = #{linkUsername},</if>
                    <if test="playletCode != null">playlet_code = #{playletCode},</if>
                    <if test="playletName != null">playlet_name = #{playletName},</if>
                    <if test="miniAppId != null">mini_app_id = #{miniAppId},</if>
                    <if test="miniAppName != null">mini_app_name = #{miniAppName},</if>
                    <if test="refundOrderId != null">refund_order_id = #{refundOrderId},</if>
                    <if test="linkDt != null">link_dt = #{linkDt},</if>
                    <if test="linkSourceChannel != null">link_source_channel = #{linkSourceChannel},</if>
                    <if test="paymentModel != null">payment_model = #{paymentModel},</if>
                    <if test="appType != null">app_type = #{appType},</if>
                    <if test="projectId != null">project_id = #{projectId},</if>
                    <if test="promotionId != null">promotion_id = #{promotionId},</if>
                    <if test="registerTime != null">register_time = #{registerTime},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gtmModified != null">gtm_modified = #{gtmModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataSupplierSnapshotLianfanById" parameterType="Long">
        delete from tb_agt_data_supplier_snapshot_lianfan where id = #{id}
    </delete>

    <delete id="deleteAgtDataSupplierSnapshotLianfanByIds" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_lianfan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>