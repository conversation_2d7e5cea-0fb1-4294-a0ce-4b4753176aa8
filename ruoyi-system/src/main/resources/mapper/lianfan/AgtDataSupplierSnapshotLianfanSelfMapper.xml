<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.lianfan.AgtDataSupplierSnapshotLianfanSelfMapper">

    <resultMap type="com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity"
               id="AgtDataSupplierSnapshotLianfanResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="orderId" column="order_id"/>
        <result property="orderStatus" column="order_status"/>
        <result property="productType" column="product_type"/>
        <result property="createTimeOrder" column="create_time_order"/>
        <result property="chargeAmount" column="charge_amount"/>
        <result property="userId" column="user_id"/>
        <result property="externalOrderId" column="external_order_id"/>
        <result property="linkId" column="link_id"/>
        <result property="linkUsername" column="link_username"/>
        <result property="playletCode" column="playlet_code"/>
        <result property="playletName" column="playlet_name"/>
        <result property="miniAppId" column="mini_app_id"/>
        <result property="miniAppName" column="mini_app_name"/>
        <result property="refundOrderId" column="refund_order_id"/>
        <result property="linkDt" column="link_dt"/>
        <result property="linkSourceChannel" column="link_source_channel"/>
        <result property="paymentModel" column="payment_model"/>
        <result property="appType" column="app_type"/>
        <result property="projectId" column="project_id"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="registerTime" column="register_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gtmModified" column="gtm_modified"/>
    </resultMap>

    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_data_supplier_snapshot_lianfan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].orderId != null">order_id,</if>
            <if test="list[0].orderStatus != null">order_status,</if>
            <if test="list[0].productType != null">product_type,</if>
            <if test="list[0].createTimeOrder != null">create_time_order,</if>
            <if test="list[0].chargeAmount != null">charge_amount,</if>
            <if test="list[0].userId != null">user_id,</if>
            <if test="list[0].externalOrderId != null">external_order_id,</if>
            <if test="list[0].linkId != null">link_id,</if>
            <if test="list[0].linkUsername != null">link_username,</if>
            <if test="list[0].playletCode != null">playlet_code,</if>
            <if test="list[0].playletName != null">playlet_name,</if>
            <if test="list[0].miniAppId != null">mini_app_id,</if>
            <if test="list[0].miniAppName != null">mini_app_name,</if>
            <if test="list[0].refundOrderId != null">refund_order_id,</if>
            <if test="list[0].linkDt != null">link_dt,</if>
            <if test="list[0].linkSourceChannel != null">link_source_channel,</if>
            <if test="list[0].paymentModel != null">payment_model,</if>
            <if test="list[0].appType != null">app_type,</if>
            <if test="list[0].projectId != null">project_id,</if>
            <if test="list[0].promotionId != null">promotion_id,</if>
            <if test="list[0].registerTime != null">register_time,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.curDate != null">#{entity.curDate},</if>
                <if test="entity.curHour != null">#{entity.curHour},</if>
                <if test="entity.orderId != null">#{entity.orderId},</if>
                <if test="entity.orderStatus != null">#{entity.orderStatus},</if>
                <if test="entity.productType != null">#{entity.productType},</if>
                <if test="entity.createTimeOrder != null">#{entity.createTimeOrder},</if>
                <if test="entity.chargeAmount != null">#{entity.chargeAmount},</if>
                <if test="entity.userId != null">#{entity.userId},</if>
                <if test="entity.externalOrderId != null">#{entity.externalOrderId},</if>
                <if test="entity.linkId != null">#{entity.linkId},</if>
                <if test="entity.linkUsername != null">#{entity.linkUsername},</if>
                <if test="entity.playletCode != null">#{entity.playletCode},</if>
                <if test="entity.playletName != null">#{entity.playletName},</if>
                <if test="entity.miniAppId != null">#{entity.miniAppId},</if>
                <if test="entity.miniAppName != null">#{entity.miniAppName},</if>
                <if test="entity.refundOrderId != null">#{entity.refundOrderId},</if>
                <if test="entity.linkDt != null">#{entity.linkDt},</if>
                <if test="entity.linkSourceChannel != null">#{entity.linkSourceChannel},</if>
                <if test="entity.paymentModel != null">#{entity.paymentModel},</if>
                <if test="entity.appType != null">#{entity.appType},</if>
                <if test="entity.projectId != null">#{entity.projectId},</if>
                <if test="entity.promotionId != null">#{entity.promotionId},</if>
                <if test="entity.registerTime != null">#{entity.registerTime},</if>
            </trim>
        </foreach>
        on duplicate key update
        advertiser_id = VALUES(advertiser_id),
        cur_date = VALUES(cur_date),
        cur_hour = VALUES(cur_hour)
    </insert>

    <select id="selectPayCount" resultType="java.lang.Integer">
        SELECT
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_lianfan
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq})
        -
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_lianfan
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq} - 1) AS pay_count
    </select>

    <select id="selectOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_agt_data_supplier_snapshot_lianfan WHERE advertiser_id=#{adId} and cur_date=#{dateReq}
        and cur_hour=#{hourReq}
    </select>
    <select id="selectIncomeByDay"
            resultType="com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity">
        SELECT
        sum( charge_amount ) AS charge_amount,
        cur_date
        FROM
        tb_agt_data_supplier_snapshot_lianfan
        WHERE
        cur_date BETWEEN #{start} and #{end}
        GROUP BY cur_date
    </select>

</mapper>