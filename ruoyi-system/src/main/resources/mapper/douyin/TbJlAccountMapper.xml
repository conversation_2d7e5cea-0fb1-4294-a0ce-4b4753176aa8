<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.TbJlAccountMapper">
    
    <resultMap type="TbJlAccount" id="TbJlAccountResult">
        <result property="id"    column="id"    />
        <result property="accountId"    column="account_id"    />
        <result property="accountName"    column="account_name"    />
        <result property="appId"    column="app_id"    />
        <result property="appSecret"    column="app_secret"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbJlAccountVo">
        select id, account_id, account_name, app_id, app_secret, gmt_create, gmt_modified from tb_jl_account
    </sql>

    <select id="selectTbJlAccountList" parameterType="TbJlAccount" resultMap="TbJlAccountResult">
        <include refid="selectTbJlAccountVo"/>
        <where>  
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="appSecret != null  and appSecret != ''"> and app_secret = #{appSecret}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>
    
    <select id="selectTbJlAccountById" parameterType="String" resultMap="TbJlAccountResult">
        <include refid="selectTbJlAccountVo"/>
        where id = #{id}
    </select>
    <select id="selectSecretByAppId" resultType="java.lang.String">
        select app_secret from tb_jl_account where app_id = #{appId} limit 1
    </select>

    <insert id="insertTbJlAccount" parameterType="TbJlAccount" useGeneratedKeys="true" keyProperty="id">
        insert into tb_jl_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="appSecret != null and appSecret != ''">app_secret,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="appSecret != null and appSecret != ''">#{appSecret},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>

    <update id="updateTbJlAccount" parameterType="TbJlAccount">
        update tb_jl_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="appSecret != null and appSecret != ''">app_secret = #{appSecret},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbJlAccountById" parameterType="String">
        delete from tb_jl_account where id = #{id}
    </delete>

    <delete id="deleteTbJlAccountByIds" parameterType="String">
        delete from tb_jl_account where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>