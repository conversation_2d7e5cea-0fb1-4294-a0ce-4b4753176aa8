<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.TbJlAccountAdvertiserMapper">
    
    <resultMap type="com.ruoyi.system.entity.douyin.TbJlAccountAdvertiser" id="TbJlAccountAdvertiserResult">
        <result property="id"    column="id"    />
        <result property="accountId"    column="account_id"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="advertiserName"    column="advertiser_name"    />
        <result property="remark"    column="remark"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbJlAccountAdvertiserVo">
        select id, account_id, advertiser_id, advertiser_name, remark, gmt_create, gmt_modified from tb_jl_account_advertiser
    </sql>

    <select id="selectTbJlAccountAdvertiserList" parameterType="com.ruoyi.system.entity.douyin.TbJlAccountAdvertiser" resultMap="TbJlAccountAdvertiserResult">
        <include refid="selectTbJlAccountAdvertiserVo"/>
        <where>  
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
            <if test="advertiserName != null  and advertiserName != ''"> and advertiser_name like concat('%', #{advertiserName}, '%')</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>
    
    <select id="selectTbJlAccountAdvertiserById" parameterType="String" resultMap="TbJlAccountAdvertiserResult">
        <include refid="selectTbJlAccountAdvertiserVo"/>
        where id = #{id}
    </select>
    <select id="selectListByPage" resultMap="TbJlAccountAdvertiserResult">
        <include refid="selectTbJlAccountAdvertiserVo"/>
        <where>
            <if test="lastId != null">
                id &lt; #{lastId}
            </if>
        </where>
        order by id desc
        limit #{pageSize}
    </select>
    <select id="selectListByAdvertiserIds" resultMap="TbJlAccountAdvertiserResult">
        <include refid="selectTbJlAccountAdvertiserVo"/>
        <where>
            advertiser_id in
            <foreach collection="advertiserIds" item="advertiser" open="(" separator="," close=")">
                #{advertiser}
            </foreach>
        </where>
    </select>

    <insert id="insertTbJlAccountAdvertiser" parameterType="com.ruoyi.system.entity.douyin.TbJlAccountAdvertiser" useGeneratedKeys="true" keyProperty="id">
        insert into tb_jl_account_advertiser
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
            <if test="advertiserName != null and advertiserName != ''">advertiser_name,</if>
            <if test="remark != null">remark,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
            <if test="advertiserName != null and advertiserName != ''">#{advertiserName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>
    <insert id="batchInsertOrUpdate">
        insert into
        tb_jl_account_advertiser(`account_id`, `advertiser_id`, `advertiser_name`, `remark`)
        values
        <foreach collection="list" separator="," item="entity">
            (#{entity.accountId},#{entity.advertiserId},#{entity.advertiserName},#{entity.remark})
        </foreach>
        ON DUPLICATE KEY UPDATE
        advertiser_name = values(advertiser_name),
        remark = values(remark)
    </insert>

    <update id="updateTbJlAccountAdvertiser" parameterType="com.ruoyi.system.entity.douyin.TbJlAccountAdvertiser">
        update tb_jl_account_advertiser
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
            <if test="advertiserName != null and advertiserName != ''">advertiser_name = #{advertiserName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbJlAccountAdvertiserById" parameterType="String">
        delete from tb_jl_account_advertiser where id = #{id}
    </delete>

    <delete id="deleteTbJlAccountAdvertiserByIds" parameterType="String">
        delete from tb_jl_account_advertiser where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>