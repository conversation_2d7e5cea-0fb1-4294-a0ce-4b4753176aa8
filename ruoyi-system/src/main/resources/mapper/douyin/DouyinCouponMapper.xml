<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.DouyinCouponMapper">
    
    <resultMap type="com.ruoyi.system.entity.douyin.DouyinCoupon" id="DouyinCouponResult">
        <result property="id"    column="id"    />
        <result property="couponId"    column="coupon_id"    />
        <result property="appId"    column="app_id"    />
        <result property="openId"    column="open_id"    />
        <result property="couponStatus"    column="coupon_status"    />
        <result property="receiveTime"    column="receive_time"    />
        <result property="merchantMetaNo"    column="merchant_meta_no"    />
        <result property="validBeginTime"    column="valid_begin_time"    />
        <result property="validEndTime"    column="valid_end_time"    />
        <result property="talentOpenId"    column="talent_open_id"    />
        <result property="talentAccount"    column="talent_account"    />
        <result property="unionId"    column="union_id"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectDouyinCouponVo">
        select id, coupon_id, app_id, open_id, coupon_status, receive_time, merchant_meta_no, valid_begin_time, valid_end_time, talent_open_id, talent_account, union_id, gmt_create, gmt_modified from tb_douyin_coupon
    </sql>

    <select id="selectDouyinCouponList" parameterType="com.ruoyi.system.entity.douyin.DouyinCoupon" resultMap="DouyinCouponResult">
        <include refid="selectDouyinCouponVo"/>
        <where>  
            <if test="couponId != null  and couponId != ''"> and coupon_id = #{couponId}</if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="couponStatus != null "> and coupon_status = #{couponStatus}</if>
            <if test="receiveTime != null "> and receive_time = #{receiveTime}</if>
            <if test="merchantMetaNo != null  and merchantMetaNo != ''"> and merchant_meta_no = #{merchantMetaNo}</if>
            <if test="validBeginTime != null "> and valid_begin_time = #{validBeginTime}</if>
            <if test="validEndTime != null "> and valid_end_time = #{validEndTime}</if>
            <if test="talentOpenId != null  and talentOpenId != ''"> and talent_open_id = #{talentOpenId}</if>
            <if test="talentAccount != null  and talentAccount != ''"> and talent_account = #{talentAccount}</if>
            <if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>
    
    <select id="selectDouyinCouponById" parameterType="String" resultMap="DouyinCouponResult">
        <include refid="selectDouyinCouponVo"/>
        where id = #{id}
    </select>
    <select id="selectByCouponId" resultMap="DouyinCouponResult">
        <include refid="selectDouyinCouponVo"/>
        where coupon_id = #{couponId}
    </select>
    <select id="selectListByOpenId" resultMap="DouyinCouponResult">
        <include refid="selectDouyinCouponVo"/>
        where open_id = #{openId}
        order by coupon_status asc
    </select>

    <insert id="insertDouyinCoupon" parameterType="com.ruoyi.system.entity.douyin.DouyinCoupon" useGeneratedKeys="true" keyProperty="id">
        insert into tb_douyin_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="couponId != null and couponId != ''">coupon_id,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="couponStatus != null">coupon_status,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="merchantMetaNo != null and merchantMetaNo != ''">merchant_meta_no,</if>
            <if test="validBeginTime != null">valid_begin_time,</if>
            <if test="validEndTime != null">valid_end_time,</if>
            <if test="talentOpenId != null and talentOpenId != ''">talent_open_id,</if>
            <if test="talentAccount != null and talentAccount != ''">talent_account,</if>
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="couponId != null and couponId != ''">#{couponId},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="couponStatus != null">#{couponStatus},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="merchantMetaNo != null and merchantMetaNo != ''">#{merchantMetaNo},</if>
            <if test="validBeginTime != null">#{validBeginTime},</if>
            <if test="validEndTime != null">#{validEndTime},</if>
            <if test="talentOpenId != null and talentOpenId != ''">#{talentOpenId},</if>
            <if test="talentAccount != null and talentAccount != ''">#{talentAccount},</if>
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>

    <update id="updateDouyinCoupon" parameterType="com.ruoyi.system.entity.douyin.DouyinCoupon">
        update tb_douyin_coupon
        <trim prefix="SET" suffixOverrides=",">
            <if test="couponId != null and couponId != ''">coupon_id = #{couponId},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="openId != null and openId != ''">open_id = #{openId},</if>
            <if test="couponStatus != null">coupon_status = #{couponStatus},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="merchantMetaNo != null and merchantMetaNo != ''">merchant_meta_no = #{merchantMetaNo},</if>
            <if test="validBeginTime != null">valid_begin_time = #{validBeginTime},</if>
            <if test="validEndTime != null">valid_end_time = #{validEndTime},</if>
            <if test="talentOpenId != null and talentOpenId != ''">talent_open_id = #{talentOpenId},</if>
            <if test="talentAccount != null and talentAccount != ''">talent_account = #{talentAccount},</if>
            <if test="unionId != null and unionId != ''">union_id = #{unionId},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDouyinCouponById" parameterType="String">
        delete from tb_douyin_coupon where id = #{id}
    </delete>

    <delete id="deleteDouyinCouponByIds" parameterType="String">
        delete from tb_douyin_coupon where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>