<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.DouyinActiveUserNumMapper">

    <resultMap type="com.ruoyi.system.entity.douyin.DouyinActiveUserNumEntity" id="DouyinActiveUserNumResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="mpId"    column="mp_id"    />
            <result property="dayUserNum"    column="day_user_num"    />
            <result property="hourUserNum"    column="hour_user_num"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectDouyinActiveUserNumVo">
        select id, cur_date, mp_id, day_user_num, hour_user_num, gmt_create, gmt_modified from tb_douyin_active_user_num
    </sql>

    <select id="selectDouyinActiveUserNumList" parameterType="com.ruoyi.system.entity.douyin.DouyinActiveUserNumEntity" resultMap="DouyinActiveUserNumResult">
        <include refid="selectDouyinActiveUserNumVo"/>
        <where>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="mpId != null  and mpId != ''"> and mp_id = #{mpId}</if>
                        <if test="dayUserNum != null "> and day_user_num = #{dayUserNum}</if>
                        <if test="hourUserNum != null  and hourUserNum != ''"> and hour_user_num = #{hourUserNum}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectDouyinActiveUserNumById" parameterType="Long" resultMap="DouyinActiveUserNumResult">
            <include refid="selectDouyinActiveUserNumVo"/>
            where id = #{id}
    </select>
    <select id="selectDouyinActiveUserByAppidAndDate"
            resultType="com.ruoyi.system.entity.douyin.DouyinActiveUserNumEntity">
        SELECT
            a.mp_id,
            a.cur_date,
            a.day_user_num,
            a.hour_user_num
        FROM
            tb_douyin_active_user_num AS a
        WHERE
            a.mp_id IN
                <foreach item="item" collection="appidList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            AND a.cur_date BETWEEN #{startDate} AND #{endDate}
    </select>

    <insert id="insertDouyinActiveUserNum" parameterType="com.ruoyi.system.entity.douyin.DouyinActiveUserNumEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_douyin_active_user_num
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">cur_date,</if>
                    <if test="mpId != null and mpId != ''">mp_id,</if>
                    <if test="dayUserNum != null">day_user_num,</if>
                    <if test="hourUserNum != null and hourUserNum != ''">hour_user_num,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">#{curDate},</if>
                    <if test="mpId != null and mpId != ''">#{mpId},</if>
                    <if test="dayUserNum != null">#{dayUserNum},</if>
                    <if test="hourUserNum != null and hourUserNum != ''">#{hourUserNum},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateDouyinActiveUserNum" parameterType="com.ruoyi.system.entity.douyin.DouyinActiveUserNumEntity">
        update tb_douyin_active_user_num
        <trim prefix="SET" suffixOverrides=",">
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="mpId != null and mpId != ''">mp_id = #{mpId},</if>
                    <if test="dayUserNum != null">day_user_num = #{dayUserNum},</if>
                    <if test="hourUserNum != null and hourUserNum != ''">hour_user_num = #{hourUserNum},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDouyinActiveUserNumById" parameterType="Long">
        delete from tb_douyin_active_user_num where id = #{id}
    </delete>

    <delete id="deleteDouyinActiveUserNumByIds" parameterType="String">
        delete from tb_douyin_active_user_num where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDouyinActiveUserNumByAppidAndDate">
        DELETE
            FROM tb_douyin_active_user_num
        WHERE
            mp_id = #{appid}
            AND cur_date = #{date}
    </delete>
</mapper>