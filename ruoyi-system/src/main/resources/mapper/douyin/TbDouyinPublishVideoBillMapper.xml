<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.TbDouyinPublishVideoBillMapper">

    <resultMap type="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoBill" id="TbDouyinPublishVideoBillResult">
        <result property="id"    column="id"    />
        <result property="billDate"    column="bill_date"    />
        <result property="videoId"    column="video_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="clientKey"    column="client_key"    />
        <result property="clientName"    column="client_name"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="douyinId"    column="douyin_id"    />
        <result property="videoViews"    column="video_views"    />
        <result property="clicks"    column="clicks"    />
        <result property="comments"    column="comments"    />
        <result property="likes"    column="likes"    />
        <result property="shares"    column="shares"    />
        <result property="talentProfit1d"    column="talent_profit_1d"    />
        <result property="talentProfitTd"    column="talent_profit_td"    />
        <result property="gmv1d"    column="gmv_1d"    />
        <result property="gmvTd"    column="gmv_td"    />
        <result property="refundGmv1d"    column="refund_gmv_1d"    />
        <result property="refundGmvTd"    column="refund_gmv_td"    />
        <result property="billingGmv1d"    column="billing_gmv_1d"    />
        <result property="billingGmvTd"    column="billing_gmv_td"    />
        <result property="billingRefundGmv1d"    column="billing_refund_gmv_1d"    />
        <result property="billingRefundGmvTd"    column="billing_refund_gmv_td"    />
        <result property="adShareCost1d"    column="ad_share_cost_1d"    />
        <result property="adShareCostTd"    column="ad_share_cost_td"    />
        <result property="feedAdShareCost1d"    column="feed_ad_share_cost_1d"    />
        <result property="feedAdShareCostTd"    column="feed_ad_share_cost_td"    />
        <result property="activeCnt1d"    column="active_cnt_1d"    />
        <result property="activeCntTd"    column="active_cnt_td"    />
        <result property="stopBillTime"    column="stop_bill_time"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbDouyinPublishVideoBillVo">
        select id, bill_date, video_id, task_id, client_key, client_name, publish_time, douyin_id,
               video_views, clicks, comments, likes, shares,
               talent_profit_1d, talent_profit_td, gmv_1d, gmv_td, refund_gmv_1d, refund_gmv_td,
               billing_gmv_1d, billing_gmv_td, billing_refund_gmv_1d, billing_refund_gmv_td,
               ad_share_cost_1d, ad_share_cost_td, feed_ad_share_cost_1d, feed_ad_share_cost_td,
               active_cnt_1d, active_cnt_td, stop_bill_time,
               gmt_create, gmt_modified
        from tb_douyin_publish_video_bill
    </sql>

    <select id="selectTbDouyinPublishVideoBillList" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoBill" resultMap="TbDouyinPublishVideoBillResult">
        <include refid="selectTbDouyinPublishVideoBillVo"/>
        <where>
            <if test="billDate != null "> and bill_date = #{billDate}</if>
            <if test="videoId != null  and videoId != ''"> and video_id = #{videoId}</if>
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="clientKey != null  and clientKey != ''"> and client_key = #{clientKey}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="publishTime != null "> and publish_time = #{publishTime}</if>
            <if test="douyinId != null "> and douyin_id = #{douyinId}</if>
            <if test="videoViews != null "> and video_views = #{videoViews}</if>
            <if test="clicks != null "> and clicks = #{clicks}</if>
            <if test="comments != null "> and comments = #{comments}</if>
            <if test="likes != null "> and likes = #{likes}</if>
            <if test="shares != null "> and shares = #{shares}</if>
            <if test="talentProfit1d != null "> and talent_profit_1d = #{talentProfit1d}</if>
            <if test="talentProfitTd != null "> and talent_profit_td = #{talentProfitTd}</if>
            <if test="gmv1d != null "> and gmv_1d = #{gmv1d}</if>
            <if test="gmvTd != null "> and gmv_td = #{gmvTd}</if>
            <if test="refundGmv1d != null "> and refund_gmv_1d = #{refundGmv1d}</if>
            <if test="refundGmvTd != null "> and refund_gmv_td = #{refundGmvTd}</if>
            <if test="billingGmv1d != null "> and billing_gmv_1d = #{billingGmv1d}</if>
            <if test="billingGmvTd != null "> and billing_gmv_td = #{billingGmvTd}</if>
            <if test="billingRefundGmv1d != null "> and billing_refund_gmv_1d = #{billingRefundGmv1d}</if>
            <if test="billingRefundGmvTd != null "> and billing_refund_gmv_td = #{billingRefundGmvTd}</if>
            <if test="adShareCost1d != null "> and ad_share_cost_1d = #{adShareCost1d}</if>
            <if test="adShareCostTd != null "> and ad_share_cost_td = #{adShareCostTd}</if>
            <if test="feedAdShareCost1d != null "> and feed_ad_share_cost_1d = #{feedAdShareCost1d}</if>
            <if test="feedAdShareCostTd != null "> and feed_ad_share_cost_td = #{feedAdShareCostTd}</if>
            <if test="activeCnt1d != null "> and active_cnt_1d = #{activeCnt1d}</if>
            <if test="activeCntTd != null "> and active_cnt_td = #{activeCntTd}</if>
            <if test="stopBillTime != null "> and stop_bill_time = #{stopBillTime}</if>
        </where>
    </select>

    <select id="selectTbDouyinPublishVideoBillById" parameterType="String" resultMap="TbDouyinPublishVideoBillResult">
        <include refid="selectTbDouyinPublishVideoBillVo"/>
        where id = #{id}
    </select>

    <insert id="insertTbDouyinPublishVideoBill" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoBill" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_douyin_publish_video_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billDate != null">bill_date,</if>
            <if test="videoId != null and videoId != ''">video_id,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="clientKey != null and clientKey != ''">client_key,</if>
            <if test="clientName != null and clientName != ''">client_name,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="douyinId != null">douyin_id,</if>
            <if test="videoViews != null">video_views,</if>
            <if test="clicks != null">clicks,</if>
            <if test="comments != null">comments,</if>
            <if test="likes != null">likes,</if>
            <if test="shares != null">shares,</if>
            <if test="talentProfit1d != null">talent_profit_1d,</if>
            <if test="talentProfitTd != null">talent_profit_td,</if>
            <if test="gmv1d != null">gmv_1d,</if>
            <if test="gmvTd != null">gmv_td,</if>
            <if test="refundGmv1d != null">refund_gmv_1d,</if>
            <if test="refundGmvTd != null">refund_gmv_td,</if>
            <if test="billingGmv1d != null">billing_gmv_1d,</if>
            <if test="billingGmvTd != null">billing_gmv_td,</if>
            <if test="billingRefundGmv1d != null">billing_refund_gmv_1d,</if>
            <if test="billingRefundGmvTd != null">billing_refund_gmv_td,</if>
            <if test="adShareCost1d != null">ad_share_cost_1d,</if>
            <if test="adShareCostTd != null">ad_share_cost_td,</if>
            <if test="feedAdShareCost1d != null">feed_ad_share_cost_1d,</if>
            <if test="feedAdShareCostTd != null">feed_ad_share_cost_td,</if>
            <if test="activeCnt1d != null">active_cnt_1d,</if>
            <if test="activeCntTd != null">active_cnt_td,</if>
            <if test="stopBillTime != null">stop_bill_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billDate != null">#{billDate},</if>
            <if test="videoId != null and videoId != ''">#{videoId},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="clientKey != null and clientKey != ''">#{clientKey},</if>
            <if test="clientName != null and clientName != ''">#{clientName},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="douyinId != null">#{douyinId},</if>
            <if test="videoViews != null">#{videoViews},</if>
            <if test="clicks != null">#{clicks},</if>
            <if test="comments != null">#{comments},</if>
            <if test="likes != null">#{likes},</if>
            <if test="shares != null">#{shares},</if>
            <if test="talentProfit1d != null">#{talentProfit1d},</if>
            <if test="talentProfitTd != null">#{talentProfitTd},</if>
            <if test="gmv1d != null">#{gmv1d},</if>
            <if test="gmvTd != null">#{gmvTd},</if>
            <if test="refundGmv1d != null">#{refundGmv1d},</if>
            <if test="refundGmvTd != null">#{refundGmvTd},</if>
            <if test="billingGmv1d != null">#{billingGmv1d},</if>
            <if test="billingGmvTd != null">#{billingGmvTd},</if>
            <if test="billingRefundGmv1d != null">#{billingRefundGmv1d},</if>
            <if test="billingRefundGmvTd != null">#{billingRefundGmvTd},</if>
            <if test="adShareCost1d != null">#{adShareCost1d},</if>
            <if test="adShareCostTd != null">#{adShareCostTd},</if>
            <if test="feedAdShareCost1d != null">#{feedAdShareCost1d},</if>
            <if test="feedAdShareCostTd != null">#{feedAdShareCostTd},</if>
            <if test="activeCnt1d != null">#{activeCnt1d},</if>
            <if test="activeCntTd != null">#{activeCntTd},</if>
            <if test="stopBillTime != null">#{stopBillTime},</if>
         </trim>
    </insert>

    <update id="updateTbDouyinPublishVideoBill" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoBill">
        update tb_douyin_publish_video_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="billDate != null">bill_date = #{billDate},</if>
            <if test="videoId != null and videoId != ''">video_id = #{videoId},</if>
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="clientKey != null and clientKey != ''">client_key = #{clientKey},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="douyinId != null">douyin_id = #{douyinId},</if>
            <if test="videoViews != null">video_views = #{videoViews},</if>
            <if test="clicks != null">clicks = #{clicks},</if>
            <if test="comments != null">comments = #{comments},</if>
            <if test="likes != null">likes = #{likes},</if>
            <if test="shares != null">shares = #{shares},</if>
            <if test="talentProfit1d != null">talent_profit_1d = #{talentProfit1d},</if>
            <if test="talentProfitTd != null">talent_profit_td = #{talentProfitTd},</if>
            <if test="gmv1d != null">gmv_1d = #{gmv1d},</if>
            <if test="gmvTd != null">gmv_td = #{gmvTd},</if>
            <if test="refundGmv1d != null">refund_gmv_1d = #{refundGmv1d},</if>
            <if test="refundGmvTd != null">refund_gmv_td = #{refundGmvTd},</if>
            <if test="billingGmv1d != null">billing_gmv_1d = #{billingGmv1d},</if>
            <if test="billingGmvTd != null">billing_gmv_td = #{billingGmvTd},</if>
            <if test="billingRefundGmv1d != null">billing_refund_gmv_1d = #{billingRefundGmv1d},</if>
            <if test="billingRefundGmvTd != null">billing_refund_gmv_td = #{billingRefundGmvTd},</if>
            <if test="adShareCost1d != null">ad_share_cost_1d = #{adShareCost1d},</if>
            <if test="adShareCostTd != null">ad_share_cost_td = #{adShareCostTd},</if>
            <if test="feedAdShareCost1d != null">feed_ad_share_cost_1d = #{feedAdShareCost1d},</if>
            <if test="feedAdShareCostTd != null">feed_ad_share_cost_td = #{feedAdShareCostTd},</if>
            <if test="activeCnt1d != null">active_cnt_1d = #{activeCnt1d},</if>
            <if test="activeCntTd != null">active_cnt_td = #{activeCntTd},</if>
            <if test="stopBillTime != null">stop_bill_time = #{stopBillTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbDouyinPublishVideoBillById" parameterType="String">
        delete from tb_douyin_publish_video_bill where id = #{id}
    </delete>

    <delete id="deleteTbDouyinPublishVideoBillByIds" parameterType="String">
        delete from tb_douyin_publish_video_bill where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
