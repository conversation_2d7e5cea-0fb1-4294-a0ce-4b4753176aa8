<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.TbDouyinPublishVideoClientMapper">

    <resultMap type="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoClient" id="TbDouyinPublishVideoClientResult">
        <result property="id"    column="id"    />
        <result property="clientKey"    column="client_key"    />
        <result property="clientName"    column="client_name"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbDouyinPublishVideoClientVo">
        select id, client_key, client_name, gmt_create, gmt_modified from tb_douyin_publish_video_client
    </sql>

    <select id="selectTbDouyinPublishVideoClientList" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoClient" resultMap="TbDouyinPublishVideoClientResult">
        <include refid="selectTbDouyinPublishVideoClientVo"/>
        <where>
            <if test="clientKey != null  and clientKey != ''"> and client_key = #{clientKey}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectTbDouyinPublishVideoClientById" parameterType="String" resultMap="TbDouyinPublishVideoClientResult">
        <include refid="selectTbDouyinPublishVideoClientVo"/>
        where id = #{id}
    </select>

    <insert id="insertTbDouyinPublishVideoClient" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoClient" useGeneratedKeys="true" keyProperty="id">
        insert into tb_douyin_publish_video_client
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientKey != null and clientKey != ''">client_key,</if>
            <if test="clientName != null and clientName != ''">client_name,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientKey != null and clientKey != ''">#{clientKey},</if>
            <if test="clientName != null and clientName != ''">#{clientName},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>

    <update id="updateTbDouyinPublishVideoClient" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoClient">
        update tb_douyin_publish_video_client
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientKey != null and clientKey != ''">client_key = #{clientKey},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbDouyinPublishVideoClientById" parameterType="String">
        delete from tb_douyin_publish_video_client where id = #{id}
    </delete>

    <delete id="deleteTbDouyinPublishVideoClientByIds" parameterType="String">
        delete from tb_douyin_publish_video_client where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
