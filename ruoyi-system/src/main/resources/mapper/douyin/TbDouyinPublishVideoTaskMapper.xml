<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.TbDouyinPublishVideoTaskMapper">

    <resultMap type="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoTask" id="TbDouyinPublishVideoTaskResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbDouyinPublishVideoTaskVo">
        select id, task_id, task_name, gmt_create, gmt_modified from tb_douyin_publish_video_task
    </sql>

    <select id="selectNeedUpdateTask" parameterType="String" resultType="String">
        SELECT DISTINCT `task_id`
        FROM `tb_douyin_publish_video_bill`
        where `task_id` not in (
            SELECT `task_id` from tb_douyin_publish_video_task
        )
    </select>

    <select id="selectTbDouyinPublishVideoTaskList" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoTask" resultMap="TbDouyinPublishVideoTaskResult">
        <include refid="selectTbDouyinPublishVideoTaskVo"/>
        <where>
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectTbDouyinPublishVideoTaskById" parameterType="String" resultMap="TbDouyinPublishVideoTaskResult">
        <include refid="selectTbDouyinPublishVideoTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertTbDouyinPublishVideoTask" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoTask" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_douyin_publish_video_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
         </trim>
    </insert>

    <update id="updateTbDouyinPublishVideoTask" parameterType="com.ruoyi.system.entity.douyin.TbDouyinPublishVideoTask">
        update tb_douyin_publish_video_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
