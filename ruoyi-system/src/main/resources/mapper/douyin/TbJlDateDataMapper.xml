<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.douyin.TbJlDateDataMapper">
    
    <resultMap type="TbJlDateData" id="TbJlDateDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="adId"    column="ad_id"    />
        <result property="adName"    column="ad_name"    />
        <result property="statCost"    column="stat_cost"    />
        <result property="convertCnt"    column="convert_cnt"    />
        <result property="convertCost"    column="convert_cost"    />
        <result property="showCnt"    column="show_cnt"    />
        <result property="clickCnt"    column="click_cnt"    />
        <result property="ctr"    column="ctr"    />
        <result property="adWatchPv"    column="ad_watch_pv"    />
        <result property="adWatchUv"    column="ad_watch_uv"    />
        <result property="firstDayAdIncome"    column="first_day_ad_income"    />
        <result property="todayAdIncome"    column="today_ad_income"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbJlDateDataVo">
        select id, cur_date, advertiser_id, ad_id,ad_name, stat_cost, convert_cnt, convert_cost, show_cnt, click_cnt, ctr,ad_watch_pv,ad_watch_uv,first_day_ad_income,today_ad_income, gmt_create, gmt_modified from tb_jl_date_data
    </sql>

    <select id="selectTbJlDateDataList" parameterType="TbJlDateData" resultMap="TbJlDateDataResult">
        <include refid="selectTbJlDateDataVo"/>
        <where>  
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
            <if test="adId != null  and adId != ''"> and ad_id = #{adId}</if>
            <if test="statCost != null "> and stat_cost = #{statCost}</if>
            <if test="convertCnt != null "> and convert_cnt = #{convertCnt}</if>
            <if test="convertCost != null "> and convert_cost = #{convertCost}</if>
            <if test="showCnt != null "> and show_cnt = #{showCnt}</if>
            <if test="clickCnt != null "> and click_cnt = #{clickCnt}</if>
            <if test="ctr != null "> and ctr = #{ctr}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by cur_date desc
    </select>
    
    <select id="selectTbJlDateDataById" parameterType="String" resultMap="TbJlDateDataResult">
        <include refid="selectTbJlDateDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTbJlDateData" parameterType="TbJlDateData" useGeneratedKeys="true" keyProperty="id">
        insert into tb_jl_date_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
            <if test="adId != null and adId != ''">ad_id,</if>
            <if test="statCost != null">stat_cost,</if>
            <if test="convertCnt != null">convert_cnt,</if>
            <if test="convertCost != null">convert_cost,</if>
            <if test="showCnt != null">show_cnt,</if>
            <if test="clickCnt != null">click_cnt,</if>
            <if test="ctr != null">ctr,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
            <if test="adId != null and adId != ''">#{adId},</if>
            <if test="statCost != null">#{statCost},</if>
            <if test="convertCnt != null">#{convertCnt},</if>
            <if test="convertCost != null">#{convertCost},</if>
            <if test="showCnt != null">#{showCnt},</if>
            <if test="clickCnt != null">#{clickCnt},</if>
            <if test="ctr != null">#{ctr},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>
    <insert id="batchInsertOrUpdate" parameterType="TbJlDateData">
        insert into
        tb_jl_date_data(`cur_date`, `advertiser_id`, `ad_id`,`ad_name`, `stat_cost`, `convert_cnt`, `convert_cost`, `show_cnt`, `click_cnt`, `ctr`,`ad_watch_pv`,`ad_watch_uv`,`first_day_ad_income`,`today_ad_income`)
        values
        <foreach collection="list" separator="," item="entity">
            (#{entity.curDate},#{entity.advertiserId},#{entity.adId},#{entity.adName},#{entity.statCost},#{entity.convertCnt},#{entity.convertCost},#{entity.showCnt},#{entity.clickCnt},#{entity.ctr},#{entity.adWatchPv},#{entity.adWatchUv},#{entity.firstDayAdIncome},#{entity.todayAdIncome})
        </foreach>
        ON DUPLICATE KEY UPDATE
        ad_name = values(ad_name),
        stat_cost = values(stat_cost),
        convert_cnt = values(convert_cnt),
        convert_cost = values(convert_cost),
        show_cnt = values(show_cnt),
        click_cnt = values(click_cnt),
        ad_watch_pv = values(ad_watch_pv),
        ad_watch_uv = values(ad_watch_uv),
        first_day_ad_income = values(first_day_ad_income),
        today_ad_income = values(today_ad_income),
        ctr = values(ctr)
    </insert>

    <update id="updateTbJlDateData" parameterType="TbJlDateData">
        update tb_jl_date_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
            <if test="adId != null and adId != ''">ad_id = #{adId},</if>
            <if test="statCost != null">stat_cost = #{statCost},</if>
            <if test="convertCnt != null">convert_cnt = #{convertCnt},</if>
            <if test="convertCost != null">convert_cost = #{convertCost},</if>
            <if test="showCnt != null">show_cnt = #{showCnt},</if>
            <if test="clickCnt != null">click_cnt = #{clickCnt},</if>
            <if test="ctr != null">ctr = #{ctr},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbJlDateDataById" parameterType="String">
        delete from tb_jl_date_data where id = #{id}
    </delete>

    <delete id="deleteTbJlDateDataByIds" parameterType="String">
        delete from tb_jl_date_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>