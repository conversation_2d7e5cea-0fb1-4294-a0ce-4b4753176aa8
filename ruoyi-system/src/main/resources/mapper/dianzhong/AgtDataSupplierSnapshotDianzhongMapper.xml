<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.dianzhong.AgtDataSupplierSnapshotDianzhongMapper">

    <resultMap type="com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity" id="AgtDataSupplierSnapshotDianzhongResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="curHour"    column="cur_hour"    />
            <result property="outTradeNo"    column="out_trade_no"    />
            <result property="discount"    column="discount"    />
            <result property="type"    column="type"    />
            <result property="statusNotify"    column="status_notify"    />
            <result property="ctime"    column="ctime"    />
            <result property="finishTime"    column="finish_time"    />
            <result property="userId"    column="user_id"    />
            <result property="channelId"    column="channel_id"    />
            <result property="domain"    column="domain"    />
            <result property="sourceInfo"    column="source_info"    />
            <result property="chapterId"    column="chapter_id"    />
            <result property="sourceDesc"    column="source_desc"    />
            <result property="registerDate"    column="register_date"    />
            <result property="openId"    column="open_id"    />
            <result property="os"    column="os"    />
            <result property="referralId"    column="referral_id"    />
            <result property="referralName"    column="referral_name"    />
            <result property="adid"    column="adid"    />
            <result property="fromDrId"    column="from_dr_id"    />
            <result property="platform"    column="platform"    />
            <result property="scene"    column="scene"    />
            <result property="thirdCorpId"    column="third_corp_id"    />
            <result property="thirdWxId"    column="third_wx_id"    />
            <result property="kdrId"    column="kdr_id"    />
            <result property="selfReturn"    column="self_return"    />
            <result property="projectId"    column="project_id"    />
            <result property="promotionId"    column="promotion_id"    />
            <result property="schannelTime"    column="schannel_time"    />
            <result property="dyeTime"    column="dye_time"    />
            <result property="moneyBenefit"    column="money_benefit"    />
            <result property="mid1"    column="mid1"    />
            <result property="mid2"    column="mid2"    />
            <result property="mid3"    column="mid3"    />
            <result property="unionId"    column="union_id"    />
            <result property="from"    column="from"    />
            <result property="orderSubType"    column="order_sub_type"    />
            <result property="ver"    column="ver"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotDianzhongVo">
        select id, advertiser_id, cur_date, cur_hour, out_trade_no, discount, type, status_notify, ctime, finish_time, user_id, channel_id, domain, source_info, chapter_id, source_desc, register_date, open_id, os, referral_id, referral_name, adid, from_dr_id, platform, scene, third_corp_id, third_wx_id, kdr_id, self_return, project_id, promotion_id, schannel_time, dye_time, money_benefit, mid1, mid2, mid3, union_id, `from`, order_sub_type, ver, gmt_create, gmt_modified from tb_agt_data_supplier_snapshot_dianzhong
    </sql>

    <select id="selectAgtDataSupplierSnapshotDianzhongList" parameterType="com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity" resultMap="AgtDataSupplierSnapshotDianzhongResult">
        <include refid="selectAgtDataSupplierSnapshotDianzhongVo"/>
        <where>
                        <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="curHour != null "> and cur_hour = #{curHour}</if>
                        <if test="outTradeNo != null  and outTradeNo != ''"> and out_trade_no = #{outTradeNo}</if>
                        <if test="discount != null "> and discount = #{discount}</if>
                        <if test="type != null "> and type = #{type}</if>
                        <if test="statusNotify != null "> and status_notify = #{statusNotify}</if>
                        <if test="ctime != null "> and ctime = #{ctime}</if>
                        <if test="finishTime != null "> and finish_time = #{finishTime}</if>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="channelId != null  and channelId != ''"> and channel_id = #{channelId}</if>
                        <if test="domain != null "> and domain = #{domain}</if>
                        <if test="sourceInfo != null  and sourceInfo != ''"> and source_info = #{sourceInfo}</if>
                        <if test="chapterId != null  and chapterId != ''"> and chapter_id = #{chapterId}</if>
                        <if test="sourceDesc != null  and sourceDesc != ''"> and source_desc = #{sourceDesc}</if>
                        <if test="registerDate != null "> and register_date = #{registerDate}</if>
                        <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
                        <if test="os != null  and os != ''"> and os = #{os}</if>
                        <if test="referralId != null  and referralId != ''"> and referral_id = #{referralId}</if>
                        <if test="referralName != null  and referralName != ''"> and referral_name like concat('%', #{referralName}, '%')</if>
                        <if test="adid != null "> and adid = #{adid}</if>
                        <if test="fromDrId != null "> and from_dr_id = #{fromDrId}</if>
                        <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
                        <if test="scene != null  and scene != ''"> and scene = #{scene}</if>
                        <if test="thirdCorpId != null "> and third_corp_id = #{thirdCorpId}</if>
                        <if test="thirdWxId != null "> and third_wx_id = #{thirdWxId}</if>
                        <if test="kdrId != null "> and kdr_id = #{kdrId}</if>
                        <if test="selfReturn != null  and selfReturn != ''"> and self_return = #{selfReturn}</if>
                        <if test="projectId != null  and projectId != ''"> and project_id = #{projectId}</if>
                        <if test="promotionId != null  and promotionId != ''"> and promotion_id = #{promotionId}</if>
                        <if test="schannelTime != null "> and schannel_time = #{schannelTime}</if>
                        <if test="dyeTime != null "> and dye_time = #{dyeTime}</if>
                        <if test="moneyBenefit != null  and moneyBenefit != ''"> and money_benefit = #{moneyBenefit}</if>
                        <if test="mid1 != null  and mid1 != ''"> and mid1 = #{mid1}</if>
                        <if test="mid2 != null  and mid2 != ''"> and mid2 = #{mid2}</if>
                        <if test="mid3 != null  and mid3 != ''"> and mid3 = #{mid3}</if>
                        <if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
                        <if test="from != null  and from != ''"> and `from` = #{from}</if>
                        <if test="orderSubType != null  and orderSubType != ''"> and order_sub_type = #{orderSubType}</if>
                        <if test="ver != null  and ver != ''"> and ver = #{ver}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataSupplierSnapshotDianzhongById" parameterType="Long" resultMap="AgtDataSupplierSnapshotDianzhongResult">
            <include refid="selectAgtDataSupplierSnapshotDianzhongVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataSupplierSnapshotDianzhong" parameterType="com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity">
        insert into tb_agt_data_supplier_snapshot_dianzhong
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="curHour != null">cur_hour,</if>
                    <if test="outTradeNo != null">out_trade_no,</if>
                    <if test="discount != null">discount,</if>
                    <if test="type != null">type,</if>
                    <if test="statusNotify != null">status_notify,</if>
                    <if test="ctime != null">ctime,</if>
                    <if test="finishTime != null">finish_time,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="channelId != null">channel_id,</if>
                    <if test="domain != null">domain,</if>
                    <if test="sourceInfo != null">source_info,</if>
                    <if test="chapterId != null">chapter_id,</if>
                    <if test="sourceDesc != null">source_desc,</if>
                    <if test="registerDate != null">register_date,</if>
                    <if test="openId != null">open_id,</if>
                    <if test="os != null">os,</if>
                    <if test="referralId != null">referral_id,</if>
                    <if test="referralName != null">referral_name,</if>
                    <if test="adid != null">adid,</if>
                    <if test="fromDrId != null">from_dr_id,</if>
                    <if test="platform != null">platform,</if>
                    <if test="scene != null">scene,</if>
                    <if test="thirdCorpId != null">third_corp_id,</if>
                    <if test="thirdWxId != null">third_wx_id,</if>
                    <if test="kdrId != null">kdr_id,</if>
                    <if test="selfReturn != null">self_return,</if>
                    <if test="projectId != null">project_id,</if>
                    <if test="promotionId != null">promotion_id,</if>
                    <if test="schannelTime != null">schannel_time,</if>
                    <if test="dyeTime != null">dye_time,</if>
                    <if test="moneyBenefit != null">money_benefit,</if>
                    <if test="mid1 != null">mid1,</if>
                    <if test="mid2 != null">mid2,</if>
                    <if test="mid3 != null">mid3,</if>
                    <if test="unionId != null">union_id,</if>
                    <if test="from != null">`from`,</if>
                    <if test="orderSubType != null">order_sub_type,</if>
                    <if test="ver != null">ver,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="curHour != null">#{curHour},</if>
                    <if test="outTradeNo != null">#{outTradeNo},</if>
                    <if test="discount != null">#{discount},</if>
                    <if test="type != null">#{type},</if>
                    <if test="statusNotify != null">#{statusNotify},</if>
                    <if test="ctime != null">#{ctime},</if>
                    <if test="finishTime != null">#{finishTime},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="channelId != null">#{channelId},</if>
                    <if test="domain != null">#{domain},</if>
                    <if test="sourceInfo != null">#{sourceInfo},</if>
                    <if test="chapterId != null">#{chapterId},</if>
                    <if test="sourceDesc != null">#{sourceDesc},</if>
                    <if test="registerDate != null">#{registerDate},</if>
                    <if test="openId != null">#{openId},</if>
                    <if test="os != null">#{os},</if>
                    <if test="referralId != null">#{referralId},</if>
                    <if test="referralName != null">#{referralName},</if>
                    <if test="adid != null">#{adid},</if>
                    <if test="fromDrId != null">#{fromDrId},</if>
                    <if test="platform != null">#{platform},</if>
                    <if test="scene != null">#{scene},</if>
                    <if test="thirdCorpId != null">#{thirdCorpId},</if>
                    <if test="thirdWxId != null">#{thirdWxId},</if>
                    <if test="kdrId != null">#{kdrId},</if>
                    <if test="selfReturn != null">#{selfReturn},</if>
                    <if test="projectId != null">#{projectId},</if>
                    <if test="promotionId != null">#{promotionId},</if>
                    <if test="schannelTime != null">#{schannelTime},</if>
                    <if test="dyeTime != null">#{dyeTime},</if>
                    <if test="moneyBenefit != null">#{moneyBenefit},</if>
                    <if test="mid1 != null">#{mid1},</if>
                    <if test="mid2 != null">#{mid2},</if>
                    <if test="mid3 != null">#{mid3},</if>
                    <if test="unionId != null">#{unionId},</if>
                    <if test="from != null">#{from},</if>
                    <if test="orderSubType != null">#{orderSubType},</if>
                    <if test="ver != null">#{ver},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataSupplierSnapshotDianzhong" parameterType="com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity">
        update tb_agt_data_supplier_snapshot_dianzhong
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="curHour != null">cur_hour = #{curHour},</if>
                    <if test="outTradeNo != null">out_trade_no = #{outTradeNo},</if>
                    <if test="discount != null">discount = #{discount},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="statusNotify != null">status_notify = #{statusNotify},</if>
                    <if test="ctime != null">ctime = #{ctime},</if>
                    <if test="finishTime != null">finish_time = #{finishTime},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="channelId != null">channel_id = #{channelId},</if>
                    <if test="domain != null">domain = #{domain},</if>
                    <if test="sourceInfo != null">source_info = #{sourceInfo},</if>
                    <if test="chapterId != null">chapter_id = #{chapterId},</if>
                    <if test="sourceDesc != null">source_desc = #{sourceDesc},</if>
                    <if test="registerDate != null">register_date = #{registerDate},</if>
                    <if test="openId != null">open_id = #{openId},</if>
                    <if test="os != null">os = #{os},</if>
                    <if test="referralId != null">referral_id = #{referralId},</if>
                    <if test="referralName != null">referral_name = #{referralName},</if>
                    <if test="adid != null">adid = #{adid},</if>
                    <if test="fromDrId != null">from_dr_id = #{fromDrId},</if>
                    <if test="platform != null">platform = #{platform},</if>
                    <if test="scene != null">scene = #{scene},</if>
                    <if test="thirdCorpId != null">third_corp_id = #{thirdCorpId},</if>
                    <if test="thirdWxId != null">third_wx_id = #{thirdWxId},</if>
                    <if test="kdrId != null">kdr_id = #{kdrId},</if>
                    <if test="selfReturn != null">self_return = #{selfReturn},</if>
                    <if test="projectId != null">project_id = #{projectId},</if>
                    <if test="promotionId != null">promotion_id = #{promotionId},</if>
                    <if test="schannelTime != null">schannel_time = #{schannelTime},</if>
                    <if test="dyeTime != null">dye_time = #{dyeTime},</if>
                    <if test="moneyBenefit != null">money_benefit = #{moneyBenefit},</if>
                    <if test="mid1 != null">mid1 = #{mid1},</if>
                    <if test="mid2 != null">mid2 = #{mid2},</if>
                    <if test="mid3 != null">mid3 = #{mid3},</if>
                    <if test="unionId != null">union_id = #{unionId},</if>
                    <if test="from != null">`from` = #{from},</if>
                    <if test="orderSubType != null">order_sub_type = #{orderSubType},</if>
                    <if test="ver != null">ver = #{ver},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataSupplierSnapshotDianzhongById" parameterType="Long">
        delete from tb_agt_data_supplier_snapshot_dianzhong where id = #{id}
    </delete>

    <delete id="deleteAgtDataSupplierSnapshotDianzhongByIds" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_dianzhong where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>