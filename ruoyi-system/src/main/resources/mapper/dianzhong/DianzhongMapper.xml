<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.dianzhong.DianzhongMapper">

    <resultMap type="com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity"
               id="AgtDataSupplierSnapshotDianzhongResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="discount" column="discount"/>
        <result property="type" column="type"/>
        <result property="statusNotify" column="status_notify"/>
        <result property="ctime" column="ctime"/>
        <result property="finishTime" column="finish_time"/>
        <result property="userId" column="user_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="domain" column="domain"/>
        <result property="sourceInfo" column="source_info"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="sourceDesc" column="source_desc"/>
        <result property="registerDate" column="register_date"/>
        <result property="openId" column="open_id"/>
        <result property="os" column="os"/>
        <result property="referralId" column="referral_id"/>
        <result property="referralName" column="referral_name"/>
        <result property="adid" column="adid"/>
        <result property="fromDrId" column="from_dr_id"/>
        <result property="platform" column="platform"/>
        <result property="scene" column="scene"/>
        <result property="thirdCorpId" column="third_corp_id"/>
        <result property="thirdWxId" column="third_wx_id"/>
        <result property="kdrId" column="kdr_id"/>
        <result property="selfReturn" column="self_return"/>
        <result property="projectId" column="project_id"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="schannelTime" column="schannel_time"/>
        <result property="dyeTime" column="dye_time"/>
        <result property="moneyBenefit" column="money_benefit"/>
        <result property="mid1" column="mid1"/>
        <result property="mid2" column="mid2"/>
        <result property="mid3" column="mid3"/>
        <result property="unionId" column="union_id"/>
        <result property="from" column="from"/>
        <result property="orderSubType" column="order_sub_type"/>
        <result property="ver" column="ver"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>


    <insert id="saveOrUpdateBatch">
        insert into
        tb_agt_data_supplier_snapshot_dianzhong
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].id != null">id,</if>
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].outTradeNo != null">out_trade_no,</if>
            <if test="list[0].discount != null">discount,</if>
            <if test="list[0].type != null">type,</if>
            <if test="list[0].statusNotify != null">status_notify,</if>
            <if test="list[0].ctime != null">ctime,</if>
            <if test="list[0].finishTime != null">finish_time,</if>
            <if test="list[0].userId != null">user_id,</if>
            <if test="list[0].channelId != null">channel_id,</if>
            <if test="list[0].domain != null">domain,</if>
            <if test="list[0].sourceInfo != null">source_info,</if>
            <if test="list[0].chapterId != null">chapter_id,</if>
            <if test="list[0].sourceDesc != null">source_desc,</if>
            <if test="list[0].registerDate != null">register_date,</if>
            <if test="list[0].openId != null">open_id,</if>
            <if test="list[0].os != null">os,</if>
            <if test="list[0].referralId != null">referral_id,</if>
            <if test="list[0].referralName != null">referral_name,</if>
            <if test="list[0].adid != null">adid,</if>
            <if test="list[0].fromDrId != null">from_dr_id,</if>
            <if test="list[0].platform != null">platform,</if>
            <if test="list[0].scene != null">scene,</if>
            <if test="list[0].thirdCorpId != null">third_corp_id,</if>
            <if test="list[0].thirdWxId != null">third_wx_id,</if>
            <if test="list[0].kdrId != null">kdr_id,</if>
            <if test="list[0].selfReturn != null">self_return,</if>
            <if test="list[0].projectId != null">project_id,</if>
            <if test="list[0].promotionId != null">promotion_id,</if>
            <if test="list[0].schannelTime != null">schannel_time,</if>
            <if test="list[0].dyeTime != null">dye_time,</if>
            <if test="list[0].moneyBenefit != null">money_benefit,</if>
            <if test="list[0].mid1 != null">mid1,</if>
            <if test="list[0].mid2 != null">mid2,</if>
            <if test="list[0].mid3 != null">mid3,</if>
            <if test="list[0].unionId != null">union_id,</if>
            <if test="list[0].from != null">`from`,</if>
            <if test="list[0].orderSubType != null">order_sub_type,</if>
            <if test="list[0].ver != null">ver,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.id != null">#{entity.id},</if>
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.curDate != null">#{entity.curDate},</if>
                <if test="entity.curHour != null">#{entity.curHour},</if>
                <if test="entity.outTradeNo != null">#{entity.outTradeNo},</if>
                <if test="entity.discount != null">#{entity.discount},</if>
                <if test="entity.type != null">#{entity.type},</if>
                <if test="entity.statusNotify != null">#{entity.statusNotify},</if>
                <if test="entity.ctime != null">#{entity.ctime},</if>
                <if test="entity.finishTime != null">#{entity.finishTime},</if>
                <if test="entity.userId != null">#{entity.userId},</if>
                <if test="entity.channelId != null">#{entity.channelId},</if>
                <if test="entity.domain != null">#{entity.domain},</if>
                <if test="entity.sourceInfo != null">#{entity.sourceInfo},</if>
                <if test="entity.chapterId != null">#{entity.chapterId},</if>
                <if test="entity.sourceDesc != null">#{entity.sourceDesc},</if>
                <if test="entity.registerDate != null">#{entity.registerDate},</if>
                <if test="entity.openId != null">#{entity.openId},</if>
                <if test="entity.os != null">#{entity.os},</if>
                <if test="entity.referralId != null">#{entity.referralId},</if>
                <if test="entity.referralName != null">#{entity.referralName},</if>
                <if test="entity.adid != null">#{entity.adid},</if>
                <if test="entity.fromDrId != null">#{entity.fromDrId},</if>
                <if test="entity.platform != null">#{entity.platform},</if>
                <if test="entity.scene != null">#{entity.scene},</if>
                <if test="entity.thirdCorpId != null">#{entity.thirdCorpId},</if>
                <if test="entity.thirdWxId != null">#{entity.thirdWxId},</if>
                <if test="entity.kdrId != null">#{entity.kdrId},</if>
                <if test="entity.selfReturn != null">#{entity.selfReturn},</if>
                <if test="entity.projectId != null">#{entity.projectId},</if>
                <if test="entity.promotionId != null">#{entity.promotionId},</if>
                <if test="entity.schannelTime != null">#{entity.schannelTime},</if>
                <if test="entity.dyeTime != null">#{entity.dyeTime},</if>
                <if test="entity.moneyBenefit != null">#{entity.moneyBenefit},</if>
                <if test="entity.mid1 != null">#{entity.mid1},</if>
                <if test="entity.mid2 != null">#{entity.mid2},</if>
                <if test="entity.mid3 != null">#{entity.mid3},</if>
                <if test="entity.unionId != null">#{entity.unionId},</if>
                <if test="entity.from != null">#{entity.from},</if>
                <if test="entity.orderSubType != null">#{entity.orderSubType},</if>
                <if test="entity.ver != null">#{entity.ver},</if>
            </trim>
        </foreach>
        on duplicate key update
        advertiser_id = VALUES(advertiser_id),
        cur_date = VALUES(cur_date),
        cur_hour = VALUES(cur_hour)
    </insert>

    <select id="selectPayCount" resultType="java.lang.Integer">
        SELECT
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_dianzhong
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq})
        -
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_dianzhong
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq} - 1) AS pay_count
    </select>
    <select id="selectOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_agt_data_supplier_snapshot_dianzhong WHERE advertiser_id=#{adId} and cur_date=#{dateReq} and cur_hour=#{hourReq}
    </select>

    <select id="selectIncomeByDay"
            resultType="com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity">
        SELECT
        sum( discount ) AS discount,
        cur_date
        FROM
        tb_agt_data_supplier_snapshot_dianzhong
        WHERE
        cur_date BETWEEN #{start} and #{end}
        GROUP BY cur_date
    </select>

</mapper>