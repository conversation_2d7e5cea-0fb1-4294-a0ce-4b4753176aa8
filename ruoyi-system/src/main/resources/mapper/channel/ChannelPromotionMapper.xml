<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.channel.ChannelPromotionMapper">

    <resultMap type="com.ruoyi.system.entity.channel.ChannelPromotionEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="promotionUrl" column="promotion_url"/>
            <result property="channel" column="channel"/>
            <result property="channelName" column="channel_name"/>
            <result property="videoGroupId" column="video_group_id"/>
            <result property="videoId" column="video_id"/>
            <result property="remark" column="remark"/>
            <result property="type" column="type"/>
            <result property="operator" column="operator"/>
            <result property="description" column="description"/>
            <result property="lotteryUrl" column="lottery_url"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            promotion_url,
            channel,
            channel_name,
            video_group_id,
            video_id,
            remark,
            type,
            operator,
            `description`,
            lottery_url,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.channel.ChannelPromotionEntity">
        INSERT INTO tb_channel_promotion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="promotionUrl != null">
                promotion_url,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="channelName != null">
                channel_name,
            </if>
            <if test="videoGroupId != null">
                video_group_id,
            </if>
            <if test="videoId != null">
                video_id,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="description != null">
                `description`,
            </if>
            <if test="lotteryUrl != null">
                lottery_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="promotionUrl != null">
                #{promotionUrl},
            </if>
            <if test="channel != null">
                #{channel},
            </if>
            <if test="channelName != null">
                #{channelName},
            </if>
            <if test="videoGroupId != null">
                #{videoGroupId},
            </if>
            <if test="videoId != null">
                #{videoId},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="lotteryUrl != null">
                #{lotteryUrl},
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_channel_promotion WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.channel.ChannelPromotionEntity">
        UPDATE tb_channel_promotion
        <set>
            <if test="promotionUrl != null">
                promotion_url = #{promotionUrl},
            </if>
            <if test="channel != null">
                channel = #{channel},
            </if>
            <if test="channelName != null">
                channel_name = #{channelName},
            </if>
            <if test="videoGroupId != null">
                video_group_id = #{videoGroupId},
            </if>
            <if test="videoId != null">
                video_id = #{videoId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
            <if test="description != null">
                `description` = #{description},
            </if>
            <if test="lotteryUrl != null">
                lottery_url = #{lotteryUrl},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_channel_promotion
        WHERE id = #{id}
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_channel_promotion
        <where>
            <if test="req.channel != null">
                channel like concat('%',#{req.channel},'%')
            </if>
            <if test="req.promotionUrl != null">
                promotion_url like concat('%',#{req.promotionUrl},'%')
            </if>
        </where>
        order by id desc
    </select>
    <select id="selectListByIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_channel_promotion
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
