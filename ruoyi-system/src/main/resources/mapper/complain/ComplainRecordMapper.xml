<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.complain.ComplainRecordMapper">

    <resultMap type="com.ruoyi.system.entity.complain.ComplainRecordEntity" id="ComplainRecordResult">
        <result property="id" column="id"/>
        <result property="contact" column="contact"/>
        <result property="title" column="title"/>
        <result property="img" column="img"/>
        <result property="content" column="content"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectComplainRecordVo">
        select id, contact, title, img, content, gmt_create, gmt_modified
        from tb_complain_record
    </sql>

    <select id="selectComplainRecordList" parameterType="com.ruoyi.system.entity.complain.ComplainRecordEntity"
            resultMap="ComplainRecordResult">
        <include refid="selectComplainRecordVo"/>
        <where>
            <if test="contact != null  and contact != ''">and contact = #{contact}</if>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectComplainRecordById" parameterType="Long" resultMap="ComplainRecordResult">
        <include refid="selectComplainRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertComplainRecord" parameterType="com.ruoyi.system.entity.complain.ComplainRecordEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_complain_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="contact != null">contact,</if>
            <if test="title != null">title,</if>
            <if test="img != null">img,</if>
            <if test="content != null">content,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="contact != null">#{contact},</if>
            <if test="title != null">#{title},</if>
            <if test="img != null">#{img},</if>
            <if test="content != null">#{content},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateComplainRecord" parameterType="com.ruoyi.system.entity.complain.ComplainRecordEntity">
        update tb_complain_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="contact != null">contact = #{contact},</if>
            <if test="title != null">title = #{title},</if>
            <if test="img != null">img = #{img},</if>
            <if test="content != null">content = #{content},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteComplainRecordById" parameterType="Long">
        delete
        from tb_complain_record
        where id = #{id}
    </delete>

    <delete id="deleteComplainRecordByIds" parameterType="String">
        delete from tb_complain_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>