<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.roiall.AgtDataTotalRoiSelfMapper">

    <resultMap type="com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity" id="AgtDataTotalRoiResult">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="advertiserName" column="advertiser_name"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="userRealname" column="user_realname"/>
        <result property="playletName" column="playlet_name"/>
        <result property="supplierNameShort" column="supplier_name_short"/>
        <result property="adOthers" column="ad_others"/>
        <result property="platformDyWx" column="platform_dy_wx"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="statDatetime" column="stat_datetime"/>
        <result property="cost" column="cost"/>
        <result property="showPv" column="show_pv"/>
        <result property="avgShowCost" column="avg_show_cost"/>
        <result property="click" column="click"/>
        <result property="avgClickCost" column="avg_click_cost"/>
        <result property="ctr" column="ctr"/>
        <result property="convertUv" column="convert_uv"/>
        <result property="convertCost" column="convert_cost"/>
        <result property="convertRate" column="convert_rate"/>
        <result property="activeCount" column="active_count"/>
        <result property="activeCost" column="active_cost"/>
        <result property="costBack" column="cost_back"/>
        <result property="costCash" column="cost_cash"/>
        <result property="costIncome" column="cost_income"/>
        <result property="payOrderCount" column="pay_order_count"/>
        <result property="payCount" column="pay_count"/>
        <result property="payArpu" column="pay_arpu"/>
        <result property="costIncomeShare" column="cost_income_share"/>
        <result property="costIncomeAdv" column="cost_income_adv"/>
        <result property="roiNhAddAd" column="roi_nh_add_ad"/>
        <result property="roiNh" column="roi_nh"/>
        <result property="roi" column="roi"/>
        <result property="costProfit" column="cost_profit"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="extA" column="ext_a"/>
        <result property="extB" column="ext_b"/>
        <result property="extC" column="ext_c"/>
    </resultMap>

    <update id="updateAgtDataTotalRoi" parameterType="com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity">
        update tb_agt_data_total_roi
        <trim prefix="SET" suffixOverrides=",">
            <if test="costIncome != null">cost_income = #{costIncome},</if>
            <if test="costIncomeShare != null">cost_income_share = #{costIncomeShare},</if>
            <if test="costIncomeAdv != null">cost_income_adv = #{costIncomeAdv},</if>
        </trim>
        where advertiser_id = #{advertiserId}
        and cur_date = #{curDate} and cur_hour = #{curHour}
    </update>

    <insert id="batchInsertOrUpdateRoi" parameterType="java.util.List">
        insert into tb_agt_data_total_roi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].accountId != null">account_id,</if>
            <if test="list[0].accountName != null and list[0].accountName != ''">account_name,</if>
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].userNickname != null">user_nickname,</if>
            <if test="list[0].userRealname != null">user_realname,</if>
            <if test="list[0].playletName != null">playlet_name,</if>
            <if test="list[0].supplierNameShort != null">supplier_name_short,</if>
            <if test="list[0].adOthers != null">ad_others,</if>
            <if test="list[0].platformDyWx != null">platform_dy_wx,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].statDatetime != null">stat_datetime,</if>
            <if test="list[0].costBack != null">cost_back,</if>
            <if test="list[0].cost != null">cost,</if>
            <if test="list[0].showPv != null">show_pv,</if>
            <if test="list[0].avgShowCost != null">avg_show_cost,</if>
            <if test="list[0].click != null">click,</if>
            <if test="list[0].avgClickCost != null">avg_click_cost,</if>
            <if test="list[0].ctr != null">ctr,</if>
            <if test="list[0].convertUv != null">convert_uv,</if>
            <if test="list[0].convertCost != null">convert_cost,</if>
            <if test="list[0].convertRate != null">convert_rate,</if>
            <if test="list[0].costCash != null">cost_cash,</if>
            <if test="list[0].costIncome != null">cost_income,</if>
            <if test="list[0].costIncomeShare != null">cost_income_share,</if>
            <if test="list[0].costIncomeAdv != null">cost_income_adv,</if>
            <if test="list[0].roiNhAddAd != null">roi_nh_add_ad,</if>
            <if test="list[0].roiNh != null">roi_nh,</if>
            <if test="list[0].roi != null">roi,</if>
            <if test="list[0].costProfit != null">cost_profit,</if>
            <if test="list[0].activeCount != null">active_count,</if>
            <if test="list[0].activeCost != null">active_cost,</if>
            <if test="list[0].payOrderCount != null">pay_order_count,</if>
            <if test="list[0].extA != null">ext_a,</if>
            <if test="list[0].extB != null">ext_b,</if>
            <if test="list[0].extC != null">ext_c,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <if test="entity != null">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="entity.accountId != null">#{entity.accountId},</if>
                    <if test="entity.accountName != null and entity.accountName != ''">#{entity.accountName},</if>
                    <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                    <if test="entity.userNickname != null">#{entity.userNickname},</if>
                    <if test="entity.userRealname != null">#{entity.userRealname},</if>
                    <if test="entity.playletName != null">#{entity.playletName},</if>
                    <if test="entity.supplierNameShort != null">#{entity.supplierNameShort},</if>
                    <if test="entity.adOthers != null">#{entity.adOthers},</if>
                    <if test="entity.platformDyWx != null">#{entity.platformDyWx},</if>
                    <if test="entity.curDate != null">#{entity.curDate},</if>
                    <if test="entity.curHour != null">#{entity.curHour},</if>
                    <if test="entity.statDatetime != null">#{entity.statDatetime},</if>
                    <if test="entity.costBack != null">#{entity.costBack},</if>
                    <if test="entity.cost != null">#{entity.cost},</if>
                    <if test="entity.showPv != null">#{entity.showPv},</if>
                    <if test="entity.avgShowCost != null">#{entity.avgShowCost},</if>
                    <if test="entity.click != null">#{entity.click},</if>
                    <if test="entity.avgClickCost != null">#{entity.avgClickCost},</if>
                    <if test="entity.ctr != null">#{entity.ctr},</if>
                    <if test="entity.convertUv != null">#{entity.convertUv},</if>
                    <if test="entity.convertCost != null">#{entity.convertCost},</if>
                    <if test="entity.convertRate != null">#{entity.convertRate},</if>
                    <if test="entity.costCash != null">#{entity.costCash},</if>
                    <if test="entity.costIncome != null">#{entity.costIncome},</if>
                    <if test="entity.costIncomeShare != null">#{entity.costIncomeShare},</if>
                    <if test="entity.costIncomeAdv != null">#{entity.costIncomeAdv},</if>
                    <if test="entity.roiNhAddAd != null">#{entity.roiNhAddAd},</if>
                    <if test="entity.roiNh != null">#{entity.roiNh},</if>
                    <if test="entity.roi != null">#{entity.roi},</if>
                    <if test="entity.costProfit != null">#{entity.costProfit},</if>
                    <if test="entity.activeCount != null">#{entity.activeCount},</if>
                    <if test="entity.activeCost != null">#{entity.activeCost},</if>
                    <if test="entity.payOrderCount != null">#{entity.payOrderCount},</if>
                    <if test="entity.extA != null">#{entity.extA},</if>
                    <if test="entity.extB != null">#{entity.extB},</if>
                    <if test="entity.extC != null">#{entity.extC},</if>
                </trim>
            </if>
        </foreach>
        on duplicate key update
        cost = VALUES(cost),
        cost_cash = VALUES(cost_cash),
        show_pv = VALUES(show_pv),
        avg_show_cost = VALUES(avg_show_cost),
        click = VALUES(click),
        avg_click_cost = VALUES(avg_click_cost),
        ctr = VALUES(ctr),
        convert_uv = VALUES(convert_uv),
        convert_cost = VALUES(convert_cost),
        convert_rate = VALUES(convert_rate),
        active_count = VALUES(active_count),
        active_cost = VALUES(active_cost)
    </insert>

    <select id="filterCostAdvertiserId" resultType="Long">
        SELECT DISTINCT advertiser_id
        FROM tb_agt_data_total_roi
        <where>
            cur_date = curdate() and cost > 0
        </where>
    </select>

    <update id="updateAdInfoStatusZero">
        UPDATE tb_agt_account_juliang_advertiser ta
        SET ta.cost_status = 0
        where ta.gmt_modified &gt;= DATE_SUB(CURDATE(), INTERVAL 2 DAY)
    </update>

    <update id="updateAdInfoStatusOne">
        UPDATE tb_agt_account_juliang_advertiser ta
        SET ta.cost_status = 1
        WHERE ta.advertiser_id IN (
        SELECT DISTINCT(advertiser_id) advertiser_id
        FROM tb_agt_data_total_roi
        WHERE cur_date &gt;= DATE_SUB(CURDATE(), INTERVAL 2 DAY)
        AND cost > 0)
    </update>

    <update id="updateAdInfoToRoiDetail">
        UPDATE
        tb_agt_data_total_roi ta LEFT JOIN
        tb_agt_account_juliang_advertiser tb
        ON ta.advertiser_id=tb.advertiser_id
        SET
        ta.account_name=tb.account_name
        , ta.user_nickname=tb.user_nickname
        , ta.user_realname=tb.user_realname
        , ta.playlet_name=tb.playlet_name
        , ta.supplier_name_short=tb.supplier_name_short
        , ta.platform_dy_wx=tb.platform_dy_wx
        WHERE
        ta.cur_date=#{curDate}
    </update>

    <update id="updateCostCash">
        UPDATE tb_agt_data_total_roi
        SET cost_cash=cost/1.05
        WHERE cur_date=#{curDate} and cur_hour=#{curHour} and cost > 0;
    </update>

    <update id="updateProfit">
        UPDATE tb_agt_data_total_roi
        SET cost_profit=(cost_income_share+cost_income_adv)-cost_cash
        WHERE cur_date=#{curDate} and cur_hour=#{curHour} and cost>0;
    </update>

    <select id="selectRoiData" resultMap="AgtDataTotalRoiResult">
        SELECT
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                ${column}
            </foreach>
        </if>
        SUM(cost) AS cost,
        SUM(show_pv) AS show_pv,
        SUM(click) AS click,
        SUM(convert_uv) AS convert_uv,
        SUM(cost_cash) AS cost_cash,
        SUM(cost_income) AS cost_income,
        SUM(cost_income_share) AS cost_income_share,
        SUM(cost_income_adv) AS cost_income_adv,
        SUM(cost_income_share)+ ROUND( SUM(cost_income_adv)*0.85, 2)- SUM(cost_cash) AS cost_profit,
        SUM(cost_profit) AS cost_profit_origin,
        SUM(active_count) AS active_count,
        SUM(pay_count) as pay_count,
        SUM(pay_order_count) as pay_order_count,
        ROUND( CASE WHEN SUM( cost ) = 0 THEN 0 ELSE SUM( cost_income ) / SUM( cost ) END, 2 ) AS roi,
        ROUND( CASE WHEN SUM( cost_cash ) = 0 THEN 0 ELSE SUM( cost_income_share ) / SUM( cost_cash ) END, 2 ) AS
        roi_nh,
        ROUND( CASE WHEN SUM( cost_cash ) = 0 THEN 0 ELSE (SUM( cost_income_share )+ROUND( SUM(cost_income_adv)*0.85,
        2)) / SUM(
        cost_cash ) END, 2 ) AS roi_nh_add_ad,
        ROUND( CASE WHEN SUM( show_pv ) = 0 THEN 0 ELSE SUM( cost ) / SUM( show_pv ) * 10 END, 2 ) AS avg_show_cost,
        ROUND( CASE WHEN SUM( show_pv ) = 0 THEN 0 ELSE SUM( click ) / SUM( show_pv ) * 100 END, 2 ) AS ctr,
        ROUND( CASE WHEN SUM( click ) = 0 THEN 0 ELSE (SUM( cost )/100) / SUM( click ) END, 2 ) AS avg_click_cost,
        ROUND( CASE WHEN SUM( convert_uv ) = 0 THEN 0 ELSE (SUM( cost )/100) / SUM( convert_uv ) END, 2 ) AS
        convert_cost,
        ROUND( CASE WHEN SUM( click ) = 0 THEN 0 ELSE SUM( convert_uv ) / SUM( click ) END, 2 ) AS convert_rate,
        ROUND( CASE WHEN SUM( active_count ) = 0 THEN 0 ELSE (SUM( cost )/100) / SUM( active_count ) END, 2 ) AS
        active_cost,
        ROUND( CASE WHEN SUM( pay_count ) = 0 THEN 0 ELSE (SUM( cost_income )/100) / SUM( pay_count ) END, 2 ) AS
        pay_arpu
        FROM tb_agt_data_total_roi
        WHERE cur_date BETWEEN #{filterStartDate} AND #{filterEndDate}
        AND user_nickname != ""
        AND (`cost` &gt; 0 or show_pv &gt; 0 or `cost_income` &gt; 0)
        <if test="filterAdvertiserId != null and filterAdvertiserId.size() > 0">
            AND advertiser_id IN
            <foreach item="item" index="index" collection="filterAdvertiserId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="filterSupplierName != null and filterSupplierName.size() > 0">
            AND supplier_name_short IN
            <foreach item="item" index="index" collection="filterSupplierName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="filterUserNickname != null and filterUserNickname.size() > 0">
            AND user_nickname IN
            <foreach item="item" index="index" collection="filterUserNickname" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="filterPlayletName != null and filterPlayletName.size() > 0">
            AND playlet_name IN
            <foreach item="item" index="index" collection="filterPlayletName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <if test="orderBy == null or orderBy.size == 0">order by
            <if test="dimension != null and dimension.contains('cur_date')">
                cur_date desc,
            </if>
            cost desc
        </if>
        <if test="orderBy != null and orderBy.size > 0">order by
            <foreach collection="orderBy" item="column" separator=",">
                ${column.field} ${column.type}
            </foreach>
        </if>
    </select>
    <select id="selectCostIncomeAd" resultType="com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity">
        select account_id,advertiser_id
        FROM tb_agt_data_total_roi
        WHERE (cost>0 or cost_income>0) and
        cur_date=#{curDate} GROUP BY advertiser_id,account_id
    </select>

    <delete id="deleteUselessData">
        DELETE
        FROM
        tb_agt_data_total_roi
        WHERE
        cur_date = DATE_SUB( CURDATE(), INTERVAL 1 DAY )
        AND cost = 0
        AND show_pv = 0
        AND cost_income = 0
    </delete>
</mapper>
