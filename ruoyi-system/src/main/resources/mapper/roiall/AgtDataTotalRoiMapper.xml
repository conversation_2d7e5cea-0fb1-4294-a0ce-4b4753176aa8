<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.roiall.AgtDataTotalRoiMapper">

    <resultMap type="com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity" id="AgtDataTotalRoiResult">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="advertiserName" column="advertiser_name"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="userRealname" column="user_realname"/>
        <result property="playletName" column="playlet_name"/>
        <result property="supplierNameShort" column="supplier_name_short"/>
        <result property="adOthers" column="ad_others"/>
        <result property="platformDyWx" column="platform_dy_wx"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="statDatetime" column="stat_datetime"/>
        <result property="cost" column="cost"/>
        <result property="showPv" column="show_pv"/>
        <result property="avgShowCost" column="avg_show_cost"/>
        <result property="click" column="click"/>
        <result property="avgClickCost" column="avg_click_cost"/>
        <result property="ctr" column="ctr"/>
        <result property="convertUv" column="convert_uv"/>
        <result property="convertCost" column="convert_cost"/>
        <result property="convertRate" column="convert_rate"/>
        <result property="activeCount" column="active_count"/>
        <result property="activeCost" column="active_cost"/>
        <result property="costBack" column="cost_back"/>
        <result property="costCash" column="cost_cash"/>
        <result property="costIncome" column="cost_income"/>
        <result property="payOrderCount" column="pay_order_count"/>
        <result property="payCount" column="pay_count"/>
        <result property="payArpu" column="pay_arpu"/>
        <result property="costIncomeShare" column="cost_income_share"/>
        <result property="costIncomeAdv" column="cost_income_adv"/>
        <result property="roiNhAddAd" column="roi_nh_add_ad"/>
        <result property="roiNh" column="roi_nh"/>
        <result property="roi" column="roi"/>
        <result property="costProfit" column="cost_profit"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="extA" column="ext_a"/>
        <result property="extB" column="ext_b"/>
        <result property="extC" column="ext_c"/>
    </resultMap>

    <sql id="selectAgtDataTotalRoiVo">
        select id, account_id, account_name, advertiser_id, advertiser_name, user_nickname, user_realname, playlet_name,
        supplier_name_short, ad_others, platform_dy_wx, cur_date, cur_hour, stat_datetime, cost, show_pv, avg_show_cost,
        click, avg_click_cost, ctr, convert_uv, convert_cost, convert_rate, active_count,active_cost, cost_back,
        cost_cash, cost_income, pay_order_count,
        pay_count, pay_arpu, cost_income_share, cost_income_adv, roi_nh_add_ad, roi_nh, roi, cost_profit, gmt_create,
        gmt_modified, ext_a, ext_b, ext_c from tb_agt_data_total_roi
    </sql>

    <select id="selectAgtDataTotalRoiList" parameterType="com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity"
            resultMap="AgtDataTotalRoiResult">
        <include refid="selectAgtDataTotalRoiVo"/>
        <where>
            <if test="accountId != null ">and account_id = #{accountId}</if>
            <if test="accountName != null  and accountName != ''">and account_name like concat('%', #{accountName},
                '%')
            </if>
            <if test="advertiserId != null ">and advertiser_id = #{advertiserId}</if>
            <if test="advertiserName != null  and advertiserName != ''">and advertiser_name like concat('%',
                #{advertiserName}, '%')
            </if>
            <if test="userNickname != null  and userNickname != ''">and user_nickname like concat('%', #{userNickname},
                '%')
            </if>
            <if test="userRealname != null  and userRealname != ''">and user_realname like concat('%', #{userRealname},
                '%')
            </if>
            <if test="playletName != null  and playletName != ''">and playlet_name like concat('%', #{playletName},
                '%')
            </if>
            <if test="supplierNameShort != null  and supplierNameShort != ''">and supplier_name_short =
                #{supplierNameShort}
            </if>
            <if test="adOthers != null  and adOthers != ''">and ad_others = #{adOthers}</if>
            <if test="platformDyWx != null ">and platform_dy_wx = #{platformDyWx}</if>
            <if test="curDate != null ">and cur_date = #{curDate}</if>
            <if test="curHour != null ">and cur_hour = #{curHour}</if>
            <if test="statDatetime != null  and statDatetime != ''">and stat_datetime = #{statDatetime}</if>
            <if test="cost != null ">and cost = #{cost}</if>
            <if test="showPv != null ">and show_pv = #{showPv}</if>
            <if test="avgShowCost != null ">and avg_show_cost = #{avgShowCost}</if>
            <if test="click != null ">and click = #{click}</if>
            <if test="avgClickCost != null ">and avg_click_cost = #{avgClickCost}</if>
            <if test="ctr != null ">and ctr = #{ctr}</if>
            <if test="convertUv != null ">and convert_uv = #{convertUv}</if>
            <if test="convertCost != null ">and convert_cost = #{convertCost}</if>
            <if test="convertRate != null ">and convert_rate = #{convertRate}</if>
            <if test="costBack != null ">and cost_back = #{costBack}</if>
            <if test="costCash != null ">and cost_cash = #{costCash}</if>
            <if test="costIncome != null ">and cost_income = #{costIncome}</if>
            <if test="payCount != null ">and pay_count = #{payCount}</if>
            <if test="payArpu != null ">and pay_arpu = #{payArpu}</if>
            <if test="costIncomeShare != null ">and cost_income_share = #{costIncomeShare}</if>
            <if test="costIncomeAdv != null ">and cost_income_adv = #{costIncomeAdv}</if>
            <if test="roiNhAddAd != null ">and roi_nh_add_ad = #{roiNhAddAd}</if>
            <if test="roiNh != null ">and roi_nh = #{roiNh}</if>
            <if test="roi != null ">and roi = #{roi}</if>
            <if test="costProfit != null ">and cost_profit = #{costProfit}</if>
            <if test="activeCount != null ">and active_count = #{activeCount}</if>
            <if test="activeCost != null ">and active_cost = #{activeCost}</if>
            <if test="payOrderCount != null ">and pay_order_count = #{payOrderCount}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
            <if test="extA != null  and extA != ''">and ext_a = #{extA}</if>
            <if test="extB != null  and extB != ''">and ext_b = #{extB}</if>
            <if test="extC != null  and extC != ''">and ext_c = #{extC}</if>
        </where>
    </select>

    <select id="selectAgtDataTotalRoiById" parameterType="Long" resultMap="AgtDataTotalRoiResult">
        <include refid="selectAgtDataTotalRoiVo"/>
        where id = #{id}
    </select>

    <select id="selectPayCountSum" resultType="java.lang.Integer">
        SELECT sum(pay_count) FROM tb_agt_data_total_roi
        WHERE advertiser_id=#{adId} and cur_date=#{dateReq}
        and cur_hour <![CDATA[ <= #{hourReq} ]]>
    </select>

    <insert id="insertAgtDataTotalRoi" parameterType="com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_data_total_roi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="advertiserName != null">advertiser_name,</if>
            <if test="userNickname != null">user_nickname,</if>
            <if test="userRealname != null">user_realname,</if>
            <if test="playletName != null">playlet_name,</if>
            <if test="supplierNameShort != null">supplier_name_short,</if>
            <if test="adOthers != null">ad_others,</if>
            <if test="platformDyWx != null">platform_dy_wx,</if>
            <if test="curDate != null">cur_date,</if>
            <if test="curHour != null">cur_hour,</if>
            <if test="statDatetime != null">stat_datetime,</if>
            <if test="cost != null">cost,</if>
            <if test="showPv != null">show_pv,</if>
            <if test="avgShowCost != null">avg_show_cost,</if>
            <if test="click != null">click,</if>
            <if test="avgClickCost != null">avg_click_cost,</if>
            <if test="ctr != null">ctr,</if>
            <if test="convertUv != null">convert_uv,</if>
            <if test="convertCost != null">convert_cost,</if>
            <if test="convertRate != null">convert_rate,</if>
            <if test="costBack != null">cost_back,</if>
            <if test="costCash != null">cost_cash,</if>
            <if test="costIncome != null">cost_income,</if>
            <if test="payCount != null">pay_count,</if>
            <if test="payArpu != null">pay_arpu,</if>
            <if test="costIncomeShare != null">cost_income_share,</if>
            <if test="costIncomeAdv != null">cost_income_adv,</if>
            <if test="roiNhAddAd != null">roi_nh_add_ad,</if>
            <if test="roiNh != null">roi_nh,</if>
            <if test="roi != null">roi,</if>
            <if test="costProfit != null">cost_profit,</if>
            <if test="activeCount != null">active_count,</if>
            <if test="activeCost != null">active_cost,</if>
            <if test="payOrderCount != null">pay_order_count,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="extA != null">ext_a,</if>
            <if test="extB != null">ext_b,</if>
            <if test="extC != null">ext_c,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="advertiserName != null">#{advertiserName},</if>
            <if test="userNickname != null">#{userNickname},</if>
            <if test="userRealname != null">#{userRealname},</if>
            <if test="playletName != null">#{playletName},</if>
            <if test="supplierNameShort != null">#{supplierNameShort},</if>
            <if test="adOthers != null">#{adOthers},</if>
            <if test="platformDyWx != null">#{platformDyWx},</if>
            <if test="curDate != null">#{curDate},</if>
            <if test="curHour != null">#{curHour},</if>
            <if test="statDatetime != null">#{statDatetime},</if>
            <if test="cost != null">#{cost},</if>
            <if test="showPv != null">#{showPv},</if>
            <if test="avgShowCost != null">#{avgShowCost},</if>
            <if test="click != null">#{click},</if>
            <if test="avgClickCost != null">#{avgClickCost},</if>
            <if test="ctr != null">#{ctr},</if>
            <if test="convertUv != null">#{convertUv},</if>
            <if test="convertCost != null">#{convertCost},</if>
            <if test="convertRate != null">#{convertRate},</if>
            <if test="costBack != null">#{costBack},</if>
            <if test="costCash != null">#{costCash},</if>
            <if test="costIncome != null">#{costIncome},</if>
            <if test="payCount != null">#{payCount},</if>
            <if test="payArpu != null">#{payArpu},</if>
            <if test="costIncomeShare != null">#{costIncomeShare},</if>
            <if test="costIncomeAdv != null">#{costIncomeAdv},</if>
            <if test="roiNhAddAd != null">#{roiNhAddAd},</if>
            <if test="roiNh != null">#{roiNh},</if>
            <if test="roi != null">#{roi},</if>
            <if test="costProfit != null">#{costProfit},</if>
            <if test="activeCount != null">#{activeCount},</if>
            <if test="activeCost != null">#{activeCost},</if>
            <if test="payOrderCount != null">#{payOrderCount},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
            <if test="extA != null">#{extA},</if>
            <if test="extB != null">#{extB},</if>
            <if test="extC != null">#{extC},</if>
        </trim>
    </insert>

    <update id="updateAgtDataTotalRoi" parameterType="com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity">
        update tb_agt_data_total_roi
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="advertiserName != null">advertiser_name = #{advertiserName},</if>
            <if test="userNickname != null">user_nickname = #{userNickname},</if>
            <if test="userRealname != null">user_realname = #{userRealname},</if>
            <if test="playletName != null">playlet_name = #{playletName},</if>
            <if test="supplierNameShort != null">supplier_name_short = #{supplierNameShort},</if>
            <if test="adOthers != null">ad_others = #{adOthers},</if>
            <if test="platformDyWx != null">platform_dy_wx = #{platformDyWx},</if>
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="curHour != null">cur_hour = #{curHour},</if>
            <if test="statDatetime != null">stat_datetime = #{statDatetime},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="showPv != null">show_pv = #{showPv},</if>
            <if test="avgShowCost != null">avg_show_cost = #{avgShowCost},</if>
            <if test="click != null">click = #{click},</if>
            <if test="avgClickCost != null">avg_click_cost = #{avgClickCost},</if>
            <if test="ctr != null">ctr = #{ctr},</if>
            <if test="convertUv != null">convert_uv = #{convertUv},</if>
            <if test="convertCost != null">convert_cost = #{convertCost},</if>
            <if test="convertRate != null">convert_rate = #{convertRate},</if>
            <if test="costBack != null">cost_back = #{costBack},</if>
            <if test="costCash != null">cost_cash = #{costCash},</if>
            <if test="costIncome != null">cost_income = #{costIncome},</if>
            <if test="payCount != null">pay_count = #{payCount},</if>
            <if test="payArpu != null">pay_arpu = #{payArpu},</if>
            <if test="costIncomeShare != null">cost_income_share = #{costIncomeShare},</if>
            <if test="costIncomeAdv != null">cost_income_adv = #{costIncomeAdv},</if>
            <if test="roiNhAddAd != null">roi_nh_add_ad = #{roiNhAddAd},</if>
            <if test="roiNh != null">roi_nh = #{roiNh},</if>
            <if test="roi != null">roi = #{roi},</if>
            <if test="costProfit != null">cost_profit = #{costProfit},</if>
            <if test="activeCount != null">active_count = #{activeCount},</if>
            <if test="activeCost != null">active_cost = #{activeCost},</if>
            <if test="payOrderCount != null">pay_order_count = #{payOrderCount},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
            <if test="extA != null">ext_a = #{extA},</if>
            <if test="extB != null">ext_b = #{extB},</if>
            <if test="extC != null">ext_c = #{extC},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataTotalRoiById" parameterType="Long">
        delete from tb_agt_data_total_roi where id = #{id}
    </delete>

    <delete id="deleteAgtDataTotalRoiByIds" parameterType="String">
        delete from tb_agt_data_total_roi where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>