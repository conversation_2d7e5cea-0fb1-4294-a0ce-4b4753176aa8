<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fanqiePromotion.AgtDataFanqiePromotionMapper">

    <resultMap type="com.ruoyi.system.entity.fanqiePromotion.AgtDataFanqiePromotionEntity" id="AgtDataFanqiePromotionResult">
            <result property="id"    column="id"    />
            <result property="bookId"    column="book_id"    />
            <result property="bookName"    column="book_name"    />
            <result property="chapterId"    column="chapter_id"    />
            <result property="chapterOrder"    column="chapter_order"    />
            <result property="chapterTitle"    column="chapter_title"    />
            <result property="createTime"    column="create_time"    />
            <result property="mediaSource"    column="media_source"    />
            <result property="monitorUrl"    column="monitor_url"    />
            <result property="optimizerAccount"    column="optimizer_account"    />
            <result property="optimizerId"    column="optimizer_id"    />
            <result property="packStrategyStatus"    column="pack_strategy_status"    />
            <result property="permissionStatus"    column="permission_status"    />
            <result property="promotionId"    column="promotion_id"    />
            <result property="promotionName"    column="promotion_name"    />
            <result property="promotionUrl"    column="promotion_url"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtDataFanqiePromotionVo">
        select id, book_id, book_name, chapter_id, chapter_order, chapter_title, create_time, media_source, monitor_url, optimizer_account, optimizer_id, pack_strategy_status, permission_status, promotion_id, promotion_name, promotion_url, gmt_create, gmt_modified from tb_agt_data_fanqie_promotion
    </sql>

    <select id="selectAgtDataFanqiePromotionList" parameterType="com.ruoyi.system.entity.fanqiePromotion.AgtDataFanqiePromotionEntity" resultMap="AgtDataFanqiePromotionResult">
        <include refid="selectAgtDataFanqiePromotionVo"/>
        <where>
                        <if test="bookId != null "> and book_id = #{bookId}</if>
                        <if test="bookName != null  and bookName != ''"> and book_name like concat('%', #{bookName}, '%')</if>
                        <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
                        <if test="chapterOrder != null "> and chapter_order = #{chapterOrder}</if>
                        <if test="chapterTitle != null  and chapterTitle != ''"> and chapter_title = #{chapterTitle}</if>
                        <if test="mediaSource != null "> and media_source = #{mediaSource}</if>
                        <if test="monitorUrl != null  and monitorUrl != ''"> and monitor_url = #{monitorUrl}</if>
                        <if test="optimizerAccount != null  and optimizerAccount != ''"> and optimizer_account = #{optimizerAccount}</if>
                        <if test="optimizerId != null "> and optimizer_id = #{optimizerId}</if>
                        <if test="packStrategyStatus != null "> and pack_strategy_status = #{packStrategyStatus}</if>
                        <if test="permissionStatus != null "> and permission_status = #{permissionStatus}</if>
                        <if test="promotionId != null "> and promotion_id = #{promotionId}</if>
                        <if test="promotionName != null  and promotionName != ''"> and promotion_name like concat('%', #{promotionName}, '%')</if>
                        <if test="promotionUrl != null  and promotionUrl != ''"> and promotion_url = #{promotionUrl}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataFanqiePromotionById" parameterType="Long" resultMap="AgtDataFanqiePromotionResult">
            <include refid="selectAgtDataFanqiePromotionVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataFanqiePromotion" parameterType="com.ruoyi.system.entity.fanqiePromotion.AgtDataFanqiePromotionEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_data_fanqie_promotion
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="bookId != null">book_id,</if>
                    <if test="bookName != null">book_name,</if>
                    <if test="chapterId != null">chapter_id,</if>
                    <if test="chapterOrder != null">chapter_order,</if>
                    <if test="chapterTitle != null">chapter_title,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="mediaSource != null">media_source,</if>
                    <if test="monitorUrl != null">monitor_url,</if>
                    <if test="optimizerAccount != null">optimizer_account,</if>
                    <if test="optimizerId != null">optimizer_id,</if>
                    <if test="packStrategyStatus != null">pack_strategy_status,</if>
                    <if test="permissionStatus != null">permission_status,</if>
                    <if test="promotionId != null">promotion_id,</if>
                    <if test="promotionName != null">promotion_name,</if>
                    <if test="promotionUrl != null">promotion_url,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="bookId != null">#{bookId},</if>
                    <if test="bookName != null">#{bookName},</if>
                    <if test="chapterId != null">#{chapterId},</if>
                    <if test="chapterOrder != null">#{chapterOrder},</if>
                    <if test="chapterTitle != null">#{chapterTitle},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="mediaSource != null">#{mediaSource},</if>
                    <if test="monitorUrl != null">#{monitorUrl},</if>
                    <if test="optimizerAccount != null">#{optimizerAccount},</if>
                    <if test="optimizerId != null">#{optimizerId},</if>
                    <if test="packStrategyStatus != null">#{packStrategyStatus},</if>
                    <if test="permissionStatus != null">#{permissionStatus},</if>
                    <if test="promotionId != null">#{promotionId},</if>
                    <if test="promotionName != null">#{promotionName},</if>
                    <if test="promotionUrl != null">#{promotionUrl},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataFanqiePromotion" parameterType="com.ruoyi.system.entity.fanqiePromotion.AgtDataFanqiePromotionEntity">
        update tb_agt_data_fanqie_promotion
        <trim prefix="SET" suffixOverrides=",">
                    <if test="bookId != null">book_id = #{bookId},</if>
                    <if test="bookName != null">book_name = #{bookName},</if>
                    <if test="chapterId != null">chapter_id = #{chapterId},</if>
                    <if test="chapterOrder != null">chapter_order = #{chapterOrder},</if>
                    <if test="chapterTitle != null">chapter_title = #{chapterTitle},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="mediaSource != null">media_source = #{mediaSource},</if>
                    <if test="monitorUrl != null">monitor_url = #{monitorUrl},</if>
                    <if test="optimizerAccount != null">optimizer_account = #{optimizerAccount},</if>
                    <if test="optimizerId != null">optimizer_id = #{optimizerId},</if>
                    <if test="packStrategyStatus != null">pack_strategy_status = #{packStrategyStatus},</if>
                    <if test="permissionStatus != null">permission_status = #{permissionStatus},</if>
                    <if test="promotionId != null">promotion_id = #{promotionId},</if>
                    <if test="promotionName != null">promotion_name = #{promotionName},</if>
                    <if test="promotionUrl != null">promotion_url = #{promotionUrl},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataFanqiePromotionById" parameterType="Long">
        delete from tb_agt_data_fanqie_promotion where id = #{id}
    </delete>

    <delete id="deleteAgtDataFanqiePromotionByIds" parameterType="String">
        delete from tb_agt_data_fanqie_promotion where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>