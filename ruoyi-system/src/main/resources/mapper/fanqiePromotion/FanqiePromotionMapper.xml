<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fanqiePromotion.FanqiePromotionMapper">

    <resultMap type="com.ruoyi.system.entity.fanqiePromotion.AgtDataFanqiePromotionEntity"
               id="AgtDataFanqiePromotionResult">
        <result property="id" column="id"/>
        <result property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="chapterOrder" column="chapter_order"/>
        <result property="chapterTitle" column="chapter_title"/>
        <result property="createTime" column="create_time"/>
        <result property="mediaSource" column="media_source"/>
        <result property="monitorUrl" column="monitor_url"/>
        <result property="optimizerAccount" column="optimizer_account"/>
        <result property="optimizerId" column="optimizer_id"/>
        <result property="packStrategyStatus" column="pack_strategy_status"/>
        <result property="permissionStatus" column="permission_status"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="promotionName" column="promotion_name"/>
        <result property="promotionUrl" column="promotion_url"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectAgtDataFanqiePromotionVo">
        select id, book_id, book_name, chapter_id, chapter_order, chapter_title, create_time, media_source, monitor_url,
        optimizer_account, optimizer_id, pack_strategy_status, permission_status, promotion_id, promotion_name,
        promotion_url, gmt_create, gmt_modified from tb_agt_data_fanqie_promotion
    </sql>

    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_data_fanqie_promotion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].bookId != null">book_id,</if>
            <if test="list[0].bookName != null">book_name,</if>
            <if test="list[0].chapterId != null">chapter_id,</if>
            <if test="list[0].chapterOrder != null">chapter_order,</if>
            <if test="list[0].chapterTitle != null">chapter_title,</if>
            <if test="list[0].createTime != null">create_time,</if>
            <if test="list[0].mediaSource != null">media_source,</if>
            <if test="list[0].monitorUrl != null">monitor_url,</if>
            <if test="list[0].optimizerAccount != null">optimizer_account,</if>
            <if test="list[0].optimizerId != null">optimizer_id,</if>
            <if test="list[0].packStrategyStatus != null">pack_strategy_status,</if>
            <if test="list[0].permissionStatus != null">permission_status,</if>
            <if test="list[0].promotionId != null">promotion_id,</if>
            <if test="list[0].promotionName != null">promotion_name,</if>
            <if test="list[0].promotionUrl != null">promotion_url,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.bookId != null">#{entity.bookId},</if>
                <if test="entity.bookName != null">#{entity.bookName},</if>
                <if test="entity.chapterId != null">#{entity.chapterId},</if>
                <if test="entity.chapterOrder != null">#{entity.chapterOrder},</if>
                <if test="entity.chapterTitle != null">#{entity.chapterTitle},</if>
                <if test="entity.createTime != null">#{entity.createTime},</if>
                <if test="entity.mediaSource != null">#{entity.mediaSource},</if>
                <if test="entity.monitorUrl != null">#{entity.monitorUrl},</if>
                <if test="entity.optimizerAccount != null">#{entity.optimizerAccount},</if>
                <if test="entity.optimizerId != null">#{entity.optimizerId},</if>
                <if test="entity.packStrategyStatus != null">#{entity.packStrategyStatus},</if>
                <if test="entity.permissionStatus != null">#{entity.permissionStatus},</if>
                <if test="entity.promotionId != null">#{entity.promotionId},</if>
                <if test="entity.promotionName != null">#{entity.promotionName},</if>
                <if test="entity.promotionUrl != null">#{entity.promotionUrl},</if>
            </trim>
        </foreach>
        on duplicate key update
        book_name = values(book_name),
        chapter_order = values(chapter_order),
        chapter_title = values(chapter_title),
        create_time = values(create_time),
        promotion_name=values(promotion_name)
    </insert>

</mapper>