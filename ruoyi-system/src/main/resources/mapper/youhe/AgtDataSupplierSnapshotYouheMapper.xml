<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.youhe.AgtDataSupplierSnapshotYouheMapper">

    <resultMap type="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity" id="AgtDataSupplierSnapshotYouheResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="androidIos"    column="android_ios"    />
            <result property="curDate"    column="cur_date"    />
            <result property="curHour"    column="cur_hour"    />
            <result property="adId"    column="ad_id"    />
            <result property="userId"    column="user_id"    />
            <result property="wechatId"    column="wechat_id"    />
            <result property="promotionsId"    column="promotions_id"    />
            <result property="totalFee"    column="total_fee"    />
            <result property="accountId"    column="account_id"    />
            <result property="accountNickname"    column="account_nickname"    />
            <result property="isFirstCharge"    column="is_first_charge"    />
            <result property="isNewUser"    column="is_new_user"    />
            <result property="orderNo"    column="order_no"    />
            <result property="orderStatus"    column="order_status"    />
            <result property="thirdPartyInfo"    column="third_party_info"    />
            <result property="finishTime"    column="finish_time"    />
            <result property="bookId"    column="book_id"    />
            <result property="bookName"    column="book_name"    />
            <result property="createTimeStr"    column="create_time_str"    />
            <result property="userRegTime"    column="user_reg_time"    />
            <result property="creativeId"    column="creative_id"    />
            <result property="promotionsName"    column="promotions_name"    />
            <result property="openId"    column="open_id"    />
            <result property="ua"    column="ua"    />
            <result property="userRegIp"    column="user_reg_ip"    />
            <result property="dyeingTime"    column="dyeing_time"    />
            <result property="orderChannel"    column="order_channel"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotYouheVo">
        select id, advertiser_id, android_ios, cur_date, cur_hour, ad_id, user_id, wechat_id, promotions_id, total_fee, account_id, account_nickname, is_first_charge, is_new_user, order_no, order_status, third_party_info, finish_time, book_id, book_name, create_time_str, user_reg_time, creative_id, promotions_name, open_id, ua, user_reg_ip, dyeing_time, order_channel, gmt_create, gmt_modified from tb_agt_data_supplier_snapshot_youhe
    </sql>

    <select id="selectAgtDataSupplierSnapshotYouheList" parameterType="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity" resultMap="AgtDataSupplierSnapshotYouheResult">
        <include refid="selectAgtDataSupplierSnapshotYouheVo"/>
        <where>
                        <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
                        <if test="androidIos != null "> and android_ios = #{androidIos}</if>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="curHour != null  and curHour != ''"> and cur_hour = #{curHour}</if>
                        <if test="adId != null  and adId != ''"> and ad_id = #{adId}</if>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="wechatId != null "> and wechat_id = #{wechatId}</if>
                        <if test="promotionsId != null "> and promotions_id = #{promotionsId}</if>
                        <if test="totalFee != null "> and total_fee = #{totalFee}</if>
                        <if test="accountId != null "> and account_id = #{accountId}</if>
                        <if test="accountNickname != null  and accountNickname != ''"> and account_nickname like concat('%', #{accountNickname}, '%')</if>
                        <if test="isFirstCharge != null "> and is_first_charge = #{isFirstCharge}</if>
                        <if test="isNewUser != null "> and is_new_user = #{isNewUser}</if>
                        <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                        <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
                        <if test="thirdPartyInfo != null  and thirdPartyInfo != ''"> and third_party_info = #{thirdPartyInfo}</if>
                        <if test="finishTime != null  and finishTime != ''"> and finish_time = #{finishTime}</if>
                        <if test="bookId != null  and bookId != ''"> and book_id = #{bookId}</if>
                        <if test="bookName != null  and bookName != ''"> and book_name like concat('%', #{bookName}, '%')</if>
                        <if test="createTimeStr != null  and createTimeStr != ''"> and create_time_str = #{createTimeStr}</if>
                        <if test="userRegTime != null  and userRegTime != ''"> and user_reg_time = #{userRegTime}</if>
                        <if test="creativeId != null  and creativeId != ''"> and creative_id = #{creativeId}</if>
                        <if test="promotionsName != null  and promotionsName != ''"> and promotions_name like concat('%', #{promotionsName}, '%')</if>
                        <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
                        <if test="ua != null  and ua != ''"> and ua = #{ua}</if>
                        <if test="userRegIp != null  and userRegIp != ''"> and user_reg_ip = #{userRegIp}</if>
                        <if test="dyeingTime != null  and dyeingTime != ''"> and dyeing_time = #{dyeingTime}</if>
                        <if test="orderChannel != null  and orderChannel != ''"> and order_channel = #{orderChannel}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataSupplierSnapshotYouheById" parameterType="String" resultMap="AgtDataSupplierSnapshotYouheResult">
            <include refid="selectAgtDataSupplierSnapshotYouheVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataSupplierSnapshotYouhe" parameterType="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_data_supplier_snapshot_youhe
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id,</if>
                    <if test="androidIos != null">android_ios,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="curHour != null">cur_hour,</if>
                    <if test="adId != null">ad_id,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="wechatId != null">wechat_id,</if>
                    <if test="promotionsId != null">promotions_id,</if>
                    <if test="totalFee != null">total_fee,</if>
                    <if test="accountId != null">account_id,</if>
                    <if test="accountNickname != null and accountNickname != ''">account_nickname,</if>
                    <if test="isFirstCharge != null">is_first_charge,</if>
                    <if test="isNewUser != null">is_new_user,</if>
                    <if test="orderNo != null">order_no,</if>
                    <if test="orderStatus != null">order_status,</if>
                    <if test="thirdPartyInfo != null">third_party_info,</if>
                    <if test="finishTime != null">finish_time,</if>
                    <if test="bookId != null">book_id,</if>
                    <if test="bookName != null">book_name,</if>
                    <if test="createTimeStr != null">create_time_str,</if>
                    <if test="userRegTime != null">user_reg_time,</if>
                    <if test="creativeId != null">creative_id,</if>
                    <if test="promotionsName != null">promotions_name,</if>
                    <if test="openId != null">open_id,</if>
                    <if test="ua != null">ua,</if>
                    <if test="userRegIp != null">user_reg_ip,</if>
                    <if test="dyeingTime != null">dyeing_time,</if>
                    <if test="orderChannel != null">order_channel,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null">#{advertiserId},</if>
                    <if test="androidIos != null">#{androidIos},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="curHour != null">#{curHour},</if>
                    <if test="adId != null">#{adId},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="wechatId != null">#{wechatId},</if>
                    <if test="promotionsId != null">#{promotionsId},</if>
                    <if test="totalFee != null">#{totalFee},</if>
                    <if test="accountId != null">#{accountId},</if>
                    <if test="accountNickname != null and accountNickname != ''">#{accountNickname},</if>
                    <if test="isFirstCharge != null">#{isFirstCharge},</if>
                    <if test="isNewUser != null">#{isNewUser},</if>
                    <if test="orderNo != null">#{orderNo},</if>
                    <if test="orderStatus != null">#{orderStatus},</if>
                    <if test="thirdPartyInfo != null">#{thirdPartyInfo},</if>
                    <if test="finishTime != null">#{finishTime},</if>
                    <if test="bookId != null">#{bookId},</if>
                    <if test="bookName != null">#{bookName},</if>
                    <if test="createTimeStr != null">#{createTimeStr},</if>
                    <if test="userRegTime != null">#{userRegTime},</if>
                    <if test="creativeId != null">#{creativeId},</if>
                    <if test="promotionsName != null">#{promotionsName},</if>
                    <if test="openId != null">#{openId},</if>
                    <if test="ua != null">#{ua},</if>
                    <if test="userRegIp != null">#{userRegIp},</if>
                    <if test="dyeingTime != null">#{dyeingTime},</if>
                    <if test="orderChannel != null">#{orderChannel},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataSupplierSnapshotYouhe" parameterType="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity">
        update tb_agt_data_supplier_snapshot_youhe
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
                    <if test="androidIos != null">android_ios = #{androidIos},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="curHour != null">cur_hour = #{curHour},</if>
                    <if test="adId != null">ad_id = #{adId},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="wechatId != null">wechat_id = #{wechatId},</if>
                    <if test="promotionsId != null">promotions_id = #{promotionsId},</if>
                    <if test="totalFee != null">total_fee = #{totalFee},</if>
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="accountNickname != null and accountNickname != ''">account_nickname = #{accountNickname},</if>
                    <if test="isFirstCharge != null">is_first_charge = #{isFirstCharge},</if>
                    <if test="isNewUser != null">is_new_user = #{isNewUser},</if>
                    <if test="orderNo != null">order_no = #{orderNo},</if>
                    <if test="orderStatus != null">order_status = #{orderStatus},</if>
                    <if test="thirdPartyInfo != null">third_party_info = #{thirdPartyInfo},</if>
                    <if test="finishTime != null">finish_time = #{finishTime},</if>
                    <if test="bookId != null">book_id = #{bookId},</if>
                    <if test="bookName != null">book_name = #{bookName},</if>
                    <if test="createTimeStr != null">create_time_str = #{createTimeStr},</if>
                    <if test="userRegTime != null">user_reg_time = #{userRegTime},</if>
                    <if test="creativeId != null">creative_id = #{creativeId},</if>
                    <if test="promotionsName != null">promotions_name = #{promotionsName},</if>
                    <if test="openId != null">open_id = #{openId},</if>
                    <if test="ua != null">ua = #{ua},</if>
                    <if test="userRegIp != null">user_reg_ip = #{userRegIp},</if>
                    <if test="dyeingTime != null">dyeing_time = #{dyeingTime},</if>
                    <if test="orderChannel != null">order_channel = #{orderChannel},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataSupplierSnapshotYouheById" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_youhe where id = #{id}
    </delete>

    <delete id="deleteAgtDataSupplierSnapshotYouheByIds" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_youhe where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>