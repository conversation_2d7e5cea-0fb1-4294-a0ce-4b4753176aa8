<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.youhe.AgtDataSupplierSnapshotYouheSelfMapper">

    <resultMap type="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity"
               id="AgtDataSupplierSnapshotYouheResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="androidIos" column="android_ios"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="adId" column="ad_id"/>
        <result property="userId" column="user_id"/>
        <result property="wechatId" column="wechat_id"/>
        <result property="promotionsId" column="promotions_id"/>
        <result property="totalFee" column="total_fee"/>
        <result property="accountId" column="account_id"/>
        <result property="accountNickname" column="account_nickname"/>
        <result property="isFirstCharge" column="is_first_charge"/>
        <result property="isNewUser" column="is_new_user"/>
        <result property="orderNo" column="order_no"/>
        <result property="orderStatus" column="order_status"/>
        <result property="thirdPartyInfo" column="third_party_info"/>
        <result property="finishTime" column="finish_time"/>
        <result property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="createTimeStr" column="create_time_str"/>
        <result property="userRegTime" column="user_reg_time"/>
        <result property="creativeId" column="creative_id"/>
        <result property="promotionsName" column="promotions_name"/>
        <result property="openId" column="open_id"/>
        <result property="ua" column="ua"/>
        <result property="userRegIp" column="user_reg_ip"/>
        <result property="dyeingTime" column="dyeing_time"/>
        <result property="orderChannel" column="order_channel"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <select id="selectAgtDataSupplierSnapshotYouheList"
            parameterType="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity"
            resultMap="AgtDataSupplierSnapshotYouheResult">
        SELECT advertiser_id,
        android_ios,
        SUM(total_fee) total_fee
        FROM tb_agt_data_supplier_snapshot_youhe
        WHERE cur_date = #{curDate}
        and cur_hour = #{curHour}
        GROUP BY advertiser_id, android_ios
    </select>
    <select id="getNewestPromotionName"
            resultType="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity">
        SELECT DISTINCT m.promotions_id, m.promotions_name
        FROM tb_agt_data_supplier_snapshot_youhe m
        INNER JOIN (
        SELECT promotions_id, MAX(gmt_create) as gmt_create
        FROM tb_agt_data_supplier_snapshot_youhe
        WHERE cur_date = CURDATE()
        GROUP BY promotions_id
        ) as t
        ON m.gmt_create = t.gmt_create AND m.promotions_id = t.promotions_id WHERE m.cur_date = #{curDate}
    </select>

    <select id="selectPayCount" resultType="java.lang.Integer">
        SELECT
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_youhe
        WHERE advertiser_id = #{adId}
        AND cur_date = #{dateReq}
        AND cur_hour &lt;= #{hourReq})
        -
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_youhe
        WHERE advertiser_id = #{adId}
        AND cur_date = #{dateReq}
        AND cur_hour &lt;= #{hourReq} - 1) AS pay_count
    </select>

    <select id="selectOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_agt_data_supplier_snapshot_youhe WHERE advertiser_id=#{adId} and cur_date=#{dateReq} and
        cur_hour=#{hourReq}
    </select>
    <select id="selectIncomeByDay"
            resultType="com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity">
        SELECT
        sum( total_fee ) AS total_fee,
        cur_date
        FROM
        tb_agt_data_supplier_snapshot_youhe
        WHERE
        cur_date BETWEEN #{start} and #{end}
        GROUP BY cur_date
    </select>

    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_data_supplier_snapshot_youhe
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].androidIos != null">android_ios,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].adId != null">ad_id,</if>
            <if test="list[0].userId != null">user_id,</if>
            <if test="list[0].wechatId != null">wechat_id,</if>
            <if test="list[0].promotionsId != null">promotions_id,</if>
            <if test="list[0].totalFee != null">total_fee,</if>
            <if test="list[0].accountId != null">account_id,</if>
            <if test="list[0].accountNickname != null">account_nickname,</if>
            <if test="list[0].isFirstCharge != null">is_first_charge,</if>
            <if test="list[0].isNewUser != null">is_new_user,</if>
            <if test="list[0].orderNo != null">order_no,</if>
            <if test="list[0].orderStatus != null">order_status,</if>
            <if test="list[0].thirdPartyInfo != null">third_party_info,</if>
            <if test="list[0].finishTime != null">finish_time,</if>
            <if test="list[0].bookId != null">book_id,</if>
            <if test="list[0].bookName != null">book_name,</if>
            <if test="list[0].createTimeStr != null">create_time_str,</if>
            <if test="list[0].userRegTime != null">user_reg_time,</if>
            <if test="list[0].creativeId != null">creative_id,</if>
            <if test="list[0].promotionsName != null">promotions_name,</if>
            <if test="list[0].openId != null">open_id,</if>
            <if test="list[0].ua != null">ua,</if>
            <if test="list[0].userRegIp != null">user_reg_ip,</if>
            <if test="list[0].dyeingTime != null">dyeing_time,</if>
            <if test="list[0].orderChannel != null">order_channel,</if>
            <if test="list[0].gmtCreate != null">gmt_create,</if>
            <if test="list[0].gmtModified != null">gmt_modified,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.androidIos != null">#{entity.androidIos},</if>
                <if test="entity.curDate != null">#{entity.curDate},</if>
                <if test="entity.curHour != null">#{entity.curHour},</if>
                <if test="entity.adId != null">#{entity.adId},</if>
                <if test="entity.userId != null">#{entity.userId},</if>
                <if test="entity.wechatId != null">#{entity.wechatId},</if>
                <if test="entity.promotionsId != null">#{entity.promotionsId},</if>
                <if test="entity.totalFee != null">#{entity.totalFee},</if>
                <if test="entity.accountId != null">#{entity.accountId},</if>
                <if test="entity.accountNickname != null">#{entity.accountNickname},
                </if>
                <if test="entity.isFirstCharge != null">#{entity.isFirstCharge},</if>
                <if test="entity.isNewUser != null">#{entity.isNewUser},</if>
                <if test="entity.orderNo != null">#{entity.orderNo},</if>
                <if test="entity.orderStatus != null">#{entity.orderStatus},</if>
                <if test="entity.thirdPartyInfo != null">#{entity.thirdPartyInfo},</if>
                <if test="entity.finishTime != null">#{entity.finishTime},</if>
                <if test="entity.bookId != null">#{entity.bookId},</if>
                <if test="entity.bookName != null">#{entity.bookName},</if>
                <if test="entity.createTimeStr != null">#{entity.createTimeStr},</if>
                <if test="entity.userRegTime != null">#{entity.userRegTime},</if>
                <if test="entity.creativeId != null">#{entity.creativeId},</if>
                <if test="entity.promotionsName != null">#{entity.promotionsName},</if>
                <if test="entity.openId != null">#{entity.openId},</if>
                <if test="entity.ua != null">#{entity.ua},</if>
                <if test="entity.userRegIp != null">#{entity.userRegIp},</if>
                <if test="entity.dyeingTime != null">#{entity.dyeingTime},</if>
                <if test="entity.orderChannel != null">#{entity.orderChannel},</if>
                <if test="entity.gmtCreate != null">#{entity.gmtCreate},</if>
                <if test="entity.gmtModified != null">#{entity.gmtModified},</if>
            </trim>
        </foreach>
        on duplicate key update
        account_nickname = VALUES(account_nickname),
        advertiser_id = VALUES(advertiser_id),
        android_ios = VALUES(android_ios)
    </insert>

</mapper>