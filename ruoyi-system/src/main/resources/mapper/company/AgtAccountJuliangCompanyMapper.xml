<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.company.AgtAccountJuliangCompanyMapper">

    <resultMap type="com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity" id="AgtAccountJuliangCompanyResult">
            <result property="id"    column="id"    />
            <result property="accountId"    column="account_id"    />
            <result property="companyName"    column="company_name"    />
            <result property="accountAgent"    column="account_agent"    />
            <result property="commission"    column="commission"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtAccountJuliangCompanyVo">
        select id, account_id, company_name, account_agent, commission, gmt_create, gmt_modified from tb_agt_account_juliang_company
    </sql>

    <select id="selectAgtAccountJuliangCompanyList" parameterType="com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity" resultMap="AgtAccountJuliangCompanyResult">
        <include refid="selectAgtAccountJuliangCompanyVo"/>
        <where>
                        <if test="accountId != null "> and account_id = #{accountId}</if>
                        <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
                        <if test="accountAgent != null  and accountAgent != ''"> and account_agent = #{accountAgent}</if>
                        <if test="commission != null "> and commission = #{commission}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectAgtAccountJuliangCompanyById" parameterType="Integer" resultMap="AgtAccountJuliangCompanyResult">
            <include refid="selectAgtAccountJuliangCompanyVo"/>
            where id = #{id}
    </select>

    <select id="queryAccountCompanyNum" resultType="com.ruoyi.system.entity.company.AccountCompanyNumEntity">
        SELECT account_id ,COUNT(account_id) as company_num FROM tb_agt_account_juliang_company GROUP BY account_id
    </select>

    <insert id="insertAgtAccountJuliangCompany" parameterType="com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity">
        insert into tb_agt_account_juliang_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="accountId != null">account_id,</if>
                    <if test="companyName != null and companyName != ''">company_name,</if>
                    <if test="accountAgent != null and accountAgent != ''">account_agent,</if>
                    <if test="commission != null">commission,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="accountId != null">#{accountId},</if>
                    <if test="companyName != null and companyName != ''">#{companyName},</if>
                    <if test="accountAgent != null and accountAgent != ''">#{accountAgent},</if>
                    <if test="commission != null">#{commission},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtAccountJuliangCompany" parameterType="com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity">
        update tb_agt_account_juliang_company
        <trim prefix="SET" suffixOverrides=",">
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
                    <if test="accountAgent != null and accountAgent != ''">account_agent = #{accountAgent},</if>
                    <if test="commission != null">commission = #{commission},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtAccountJuliangCompanyById" parameterType="Integer">
        delete from tb_agt_account_juliang_company where id = #{id}
    </delete>

    <delete id="deleteAgtAccountJuliangCompanyByIds" parameterType="String">
        delete from tb_agt_account_juliang_company where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>