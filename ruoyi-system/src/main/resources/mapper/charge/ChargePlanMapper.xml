<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.charge.ChargePlanMapper">

    <resultMap type="com.ruoyi.system.entity.charge.ChargePlanEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="title" column="title"/>
            <result property="amount" column="amount"/>
            <result property="presentCoin" column="present_coin"/>
            <result property="sort" column="sort"/>
            <result property="operator" column="operator"/>
            <result property="isDeleted" column="is_deleted"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            title,
            amount,
            present_coin,
            sort,
            operator,
            is_deleted,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.charge.ChargePlanEntity">
        INSERT INTO tb_charge_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">
                title,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="coin != null">
                coin,
            </if>
            <if test="presentCoin != null">
                present_coin,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="isDeleted != null">
                is_deleted
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">
                #{title},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="coin != null">
                #{coin},
            </if>
            <if test="presentCoin != null">
                #{presentCoin},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
            <if test="isDeleted != null">
                #{isDeleted}
            </if>
        </trim>
    </insert>

    <update id="deleteById">
        update tb_charge_plan set is_deleted = 1 WHERE id=#{id}
    </update>

    <update id="updateById" parameterType="com.ruoyi.system.entity.charge.ChargePlanEntity">
        UPDATE tb_charge_plan
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="coin != null">
                coin = #{coin},
            </if>
            <if test="presentCoin != null">
                present_coin = #{presentCoin},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    <update id="batchMove" >
        <foreach collection="entityList" separator=";" item="item">
            UPDATE tb_charge_plan
            <set>
                <if test="item.sort != null">
                    sort = #{item.sort}
                </if>
                <if test="item.operator != null and item.operator != ''">
                    ,operator = #{item.operator}
                </if>
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_charge_plan
        WHERE id = #{id}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_charge_plan where is_deleted = 0 order by sort asc
    </select>
    <select id="selectMaxSort" resultType="java.lang.Integer">
        select max(sort) from tb_charge_plan where is_deleted = 0
    </select>

</mapper>