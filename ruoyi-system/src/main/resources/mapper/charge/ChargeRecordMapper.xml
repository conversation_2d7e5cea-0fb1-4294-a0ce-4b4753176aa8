<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.charge.ChargeRecordMapper">

    <resultMap type="com.ruoyi.system.entity.charge.ChargeRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="userId" column="user_id"/>
            <result property="channelId" column="channel_id"/>
            <result property="chargeStatus" column="charge_status"/>
            <result property="chargeType" column="charge_type"/>
            <result property="outTradeNo" column="out_trade_no"/>
            <result property="amount" column="amount"/>
            <result property="coin" column="coin"/>
            <result property="coinSum" column="coin_sum"/>
            <result property="tradeNo" column="trade_no"/>
            <result property="operator" column="operator"/>
            <result property="mchId" column="mch_id"/>
            <result property="outOrderNo" column="out_order_no"/>
            <result property="originalAmount" column="original_amount"/>
            <result property="payPlatform" column="pay_platform"/>
            <result property="ext" column="ext"/>
            <result property="signStatus" column="sign_status"/>
            <result property="payFailedReason" column="pay_failed_reason"/>
            <result property="engineChannel" column="engine_channel"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            user_id,
            charge_status,
            channel_id,
            charge_type,
            out_trade_no,
            amount,
            coin,
            coin_sum,
            trade_no,
            operator,
            mch_id,
            out_order_no,
            original_amount,
            pay_platform,
            ext,
            engine_channel,
            sign_status,
            pay_failed_reason,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.charge.ChargeRecordEntity">
        INSERT INTO tb_charge_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="chargeStatus != null">
                charge_status,
            </if>
            <if test="chargeType != null">
                charge_type,
            </if>
            <if test="outTradeNo != null">
                out_trade_no,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="coin != null">
                coin,
            </if>
            <if test="coinSum != null">
                coin_sum,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="tradeNo != null">
                trade_no
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="chargeStatus != null">
                #{chargeStatus},
            </if>
            <if test="chargeType != null">
                #{chargeType},
            </if>
            <if test="outTradeNo != null">
                #{outTradeNo},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="coin != null">
                #{coin},
            </if>
            <if test="coinSum != null">
                #{coinSum},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
            <if test="tradeNo != null">
                #{tradeNo}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_charge_record WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.charge.ChargeRecordEntity">
        UPDATE tb_charge_record
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="chargeStatus != null">
                charge_status = #{chargeStatus},
            </if>
            <if test="chargeType != null">
                charge_type = #{chargeType},
            </if>
            <if test="outTradeNo != null">
                out_trade_no = #{outTradeNo},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="coin != null">
                coin = #{coin},
            </if>
            <if test="coinSum != null">
                coin_sum = #{coinSum},
            </if>
            <if test="tradeNo != null">
                trade_no = #{tradeNo},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_charge_record
        WHERE id = #{id}
    </select>
    <select id="selectChargeSum" resultType="com.ruoyi.system.bo.playlet.ChargeSumBo">
        SELECT
        user_id as userId,
        ifnull(SUM(amount),0) as chargeSum
        FROM tb_charge_record
        <where>
            charge_status = #{chargeStatus}
            and user_id in
            <foreach collection="userIds" separator="," open="(" item="userId" close=")">
                #{userId}
            </foreach>
        </where>
        GROUP BY user_id
    </select>
    <select id="selectListByReq" resultMap="BaseResultMap" parameterType="com.ruoyi.system.req.playlet.param.ChargeRecordParam">
        select
        <include refid="Base_Column_List"/>
        from tb_charge_record
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" separator="," open="(" item="userId" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
            </if>
            <if test="chargeStatus != null">
                and charge_status = #{chargeStatus}
            </if>
            <if test="chargeTypes != null and chargeTypes.size > 0">
                and charge_type in
                <foreach collection="chargeTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="engineChannel!=null">
                and engine_channel=#{engineChannel}
            </if>
            <if test="signStatus!=null">
                and sign_status=#{signStatus}
            </if>
            <if test="payFailedReason!=null">
                and pay_failed_reason=#{payFailedReason}
            </if>
        </where>
        order by id desc
    </select>
    <select id="chargeRecordSum" resultType="com.ruoyi.system.bo.playlet.ChargeRecordSumBo">
        select
        SUM(amount) as amountSum,
        SUM(coin) as coinSum
        from tb_charge_record
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" separator="," open="(" item="userId" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
            </if>
            <if test="chargeStatus != null">
                and charge_status = #{chargeStatus}
            </if>
            <if test="chargeTypes != null and chargeTypes.size > 0">
                and charge_type in
                <foreach collection="chargeTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="signStatus!=null">
                and sign_status=#{signStatus}
            </if>
            <if test="payFailedReason!=null">
                and pay_failed_reason=#{payFailedReason}
            </if>
        </where>
    </select>
    <select id="selectListByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_charge_record
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="appChargeRecordSum" resultType="com.ruoyi.system.bo.playlet.AppChargeRecordSumBo">
        select
        SUM(amount) as amountSum
        from tb_charge_record
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" separator="," open="(" item="userId" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
            </if>
            <if test="chargeTypes != null and chargeTypes.size > 0">
                and charge_type in
                <foreach collection="chargeTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="chargeStatus!=null">
                and charge_status=#{chargeStatus}
            </if>
            <if test="engineChannel!=null">
                and engine_channel=#{engineChannel}
            </if>
            <if test="signStatus!=null">
                and sign_status=#{signStatus}
            </if>
            <if test="payFailedReason!=null">
                and pay_failed_reason=#{payFailedReason}
            </if>
        </where>
    </select>
    <select id="selectListPageByParam"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_charge_record
        <where>
            id &lt; #{lastId}
            <if test="param.userId != null">
                and user_id = #{param.userId}
            </if>
            <if test="param.userIds != null and param.userIds.size > 0">
                and user_id in
                <foreach collection="param.userIds" separator="," open="(" item="userId" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="param.startDate != null and param.endDate != null">
                and gmt_create &gt;= #{param.startDate} and gmt_create &lt;= #{param.endDate}
            </if>
            <if test="param.chargeStatus != null">
                and charge_status = #{param.chargeStatus}
            </if>
            <if test="param.chargeTypes != null and param.chargeTypes.size > 0">
                and charge_type in
                <foreach collection="param.chargeTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.amount != null">
                and amount = #{param.amount}
            </if>
            <if test="param.engineChannel!=null">
                and engine_channel=#{param.engineChannel}
            </if>
            <if test="param.signStatus!=null">
                and sign_status=#{param.signStatus}
            </if>
            <if test="param.payFailedReason!=null">
                and pay_failed_reason=#{param.payFailedReason}
            </if>
        </where>
        order by id desc limit #{limit}
    </select>

</mapper>