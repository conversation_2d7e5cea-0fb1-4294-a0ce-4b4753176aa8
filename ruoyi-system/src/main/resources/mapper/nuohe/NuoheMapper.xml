<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.nuohe.NuoheMapper">

    <resultMap type="com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity"
               id="AgtDataSupplierSnapshotNuoheResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="promotionName" column="promotion_name"/>
        <result property="payFee" column="pay_fee"/>
        <result property="payTime" column="pay_time"/>
        <result property="rechargeId" column="recharge_id"/>
        <result property="userId" column="user_id"/>
        <result property="rechargeStatus" column="recharge_status"/>
        <result property="deviceType" column="device_type"/>
        <result property="organizationId" column="organization_id"/>
        <result property="appId" column="app_id"/>
        <result property="appName" column="app_name"/>
        <result property="platform" column="platform"/>
        <result property="agentUsername" column="agent_username"/>
        <result property="agentNickname" column="agent_nickname"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_data_supplier_snapshot_nuohe
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].promotionName != null">promotion_name,</if>
            <if test="list[0].payFee != null">pay_fee,</if>
            <if test="list[0].payTime != null">pay_time,</if>
            <if test="list[0].rechargeId != null">recharge_id,</if>
            <if test="list[0].userId != null">user_id,</if>
            <if test="list[0].rechargeStatus != null">recharge_status,</if>
            <if test="list[0].deviceType != null">device_type,</if>
            <if test="list[0].organizationId != null">organization_id,</if>
            <if test="list[0].appId != null">app_id,</if>
            <if test="list[0].appName != null">app_name,</if>
            <if test="list[0].platform != null">platform,</if>
            <if test="list[0].agentUsername != null">agent_username,</if>
            <if test="list[0].agentNickname != null">agent_nickname,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.curDate != null">#{entity.curDate},</if>
                <if test="entity.curHour != null">#{entity.curHour},</if>
                <if test="entity.promotionName != null">#{entity.promotionName},</if>
                <if test="entity.payFee != null">#{entity.payFee},</if>
                <if test="entity.payTime != null">#{entity.payTime},</if>
                <if test="entity.rechargeId != null">#{entity.rechargeId},</if>
                <if test="entity.userId != null">#{entity.userId},</if>
                <if test="entity.rechargeStatus != null">#{entity.rechargeStatus},</if>
                <if test="entity.deviceType != null">#{entity.deviceType},</if>
                <if test="entity.organizationId != null">#{entity.organizationId},</if>
                <if test="entity.appId != null">#{entity.appId},</if>
                <if test="entity.appName != null">#{entity.appName},</if>
                <if test="entity.platform != null">#{entity.platform},</if>
                <if test="entity.agentUsername != null">#{entity.agentUsername},</if>
                <if test="entity.agentNickname != null">#{entity.agentNickname},</if>
            </trim>
        </foreach>
        on duplicate key update
        advertiser_id = values(advertiser_id),
        cur_date = values(cur_date),
        cur_hour = values(cur_hour)
    </insert>

    <select id="selectIncomeByDay" resultType="com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity">
        SELECT
        sum( pay_fee ) AS pay_fee,
        cur_date
        FROM
        tb_agt_data_supplier_snapshot_nuohe
        WHERE
        cur_date BETWEEN #{start} and #{end}
        GROUP BY cur_date
    </select>

    <select id="selectPayCount" resultType="java.lang.Integer">
        SELECT
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_nuohe
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq})
        -
        (SELECT COUNT(DISTINCT user_id)
        FROM tb_agt_data_supplier_snapshot_nuohe
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq} - 1) AS pay_count
    </select>

    <select id="selectOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM tb_agt_data_supplier_snapshot_nuohe
        WHERE advertiser_id=#{adId}
        and cur_date=#{dateReq}
        and cur_hour=#{hourReq}
    </select>


</mapper>