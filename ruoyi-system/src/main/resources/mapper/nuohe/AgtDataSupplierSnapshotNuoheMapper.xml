<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.nuohe.AgtDataSupplierSnapshotNuoheMapper">

    <resultMap type="com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity" id="AgtDataSupplierSnapshotNuoheResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="curHour"    column="cur_hour"    />
            <result property="promotionName"    column="promotion_name"    />
            <result property="payFee"    column="pay_fee"    />
            <result property="payTime"    column="pay_time"    />
            <result property="rechargeId"    column="recharge_id"    />
            <result property="userId"    column="user_id"    />
            <result property="rechargeStatus"    column="recharge_status"    />
            <result property="deviceType"    column="device_type"    />
            <result property="organizationId"    column="organization_id"    />
            <result property="appId"    column="app_id"    />
            <result property="appName"    column="app_name"    />
            <result property="platform"    column="platform"    />
            <result property="agentUsername"    column="agent_username"    />
            <result property="agentNickname"    column="agent_nickname"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotNuoheVo">
        select id, advertiser_id, cur_date, cur_hour, promotion_name, pay_fee, pay_time, recharge_id, user_id, recharge_status, device_type, organization_id, app_id, app_name, platform, agent_username, agent_nickname, gmt_create, gmt_modified from tb_agt_data_supplier_snapshot_nuohe
    </sql>

    <select id="selectAgtDataSupplierSnapshotNuoheList" parameterType="com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity" resultMap="AgtDataSupplierSnapshotNuoheResult">
        <include refid="selectAgtDataSupplierSnapshotNuoheVo"/>
        <where>
                        <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="curHour != null "> and cur_hour = #{curHour}</if>
                        <if test="promotionName != null  and promotionName != ''"> and promotion_name like concat('%', #{promotionName}, '%')</if>
                        <if test="payFee != null "> and pay_fee = #{payFee}</if>
                        <if test="payTime != null "> and pay_time = #{payTime}</if>
                        <if test="rechargeId != null  and rechargeId != ''"> and recharge_id = #{rechargeId}</if>
                        <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                        <if test="rechargeStatus != null  and rechargeStatus != ''"> and recharge_status = #{rechargeStatus}</if>
                        <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
                        <if test="organizationId != null  and organizationId != ''"> and organization_id = #{organizationId}</if>
                        <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
                        <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
                        <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
                        <if test="agentUsername != null  and agentUsername != ''"> and agent_username like concat('%', #{agentUsername}, '%')</if>
                        <if test="agentNickname != null  and agentNickname != ''"> and agent_nickname like concat('%', #{agentNickname}, '%')</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataSupplierSnapshotNuoheById" parameterType="Long" resultMap="AgtDataSupplierSnapshotNuoheResult">
            <include refid="selectAgtDataSupplierSnapshotNuoheVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataSupplierSnapshotNuohe" parameterType="com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity">
        insert into tb_agt_data_supplier_snapshot_nuohe
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="advertiserId != null">advertiser_id,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="curHour != null">cur_hour,</if>
                    <if test="promotionName != null">promotion_name,</if>
                    <if test="payFee != null">pay_fee,</if>
                    <if test="payTime != null">pay_time,</if>
                    <if test="rechargeId != null">recharge_id,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="rechargeStatus != null">recharge_status,</if>
                    <if test="deviceType != null">device_type,</if>
                    <if test="organizationId != null">organization_id,</if>
                    <if test="appId != null">app_id,</if>
                    <if test="appName != null">app_name,</if>
                    <if test="platform != null">platform,</if>
                    <if test="agentUsername != null">agent_username,</if>
                    <if test="agentNickname != null">agent_nickname,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="advertiserId != null">#{advertiserId},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="curHour != null">#{curHour},</if>
                    <if test="promotionName != null">#{promotionName},</if>
                    <if test="payFee != null">#{payFee},</if>
                    <if test="payTime != null">#{payTime},</if>
                    <if test="rechargeId != null">#{rechargeId},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="rechargeStatus != null">#{rechargeStatus},</if>
                    <if test="deviceType != null">#{deviceType},</if>
                    <if test="organizationId != null">#{organizationId},</if>
                    <if test="appId != null">#{appId},</if>
                    <if test="appName != null">#{appName},</if>
                    <if test="platform != null">#{platform},</if>
                    <if test="agentUsername != null">#{agentUsername},</if>
                    <if test="agentNickname != null">#{agentNickname},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataSupplierSnapshotNuohe" parameterType="com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity">
        update tb_agt_data_supplier_snapshot_nuohe
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="curHour != null">cur_hour = #{curHour},</if>
                    <if test="promotionName != null">promotion_name = #{promotionName},</if>
                    <if test="payFee != null">pay_fee = #{payFee},</if>
                    <if test="payTime != null">pay_time = #{payTime},</if>
                    <if test="rechargeId != null">recharge_id = #{rechargeId},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="rechargeStatus != null">recharge_status = #{rechargeStatus},</if>
                    <if test="deviceType != null">device_type = #{deviceType},</if>
                    <if test="organizationId != null">organization_id = #{organizationId},</if>
                    <if test="appId != null">app_id = #{appId},</if>
                    <if test="appName != null">app_name = #{appName},</if>
                    <if test="platform != null">platform = #{platform},</if>
                    <if test="agentUsername != null">agent_username = #{agentUsername},</if>
                    <if test="agentNickname != null">agent_nickname = #{agentNickname},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataSupplierSnapshotNuoheById" parameterType="Long">
        delete from tb_agt_data_supplier_snapshot_nuohe where id = #{id}
    </delete>

    <delete id="deleteAgtDataSupplierSnapshotNuoheByIds" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_nuohe where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>