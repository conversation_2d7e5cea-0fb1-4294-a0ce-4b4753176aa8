<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.novel.NovelUserBookshelfMapper">

    <resultMap type="com.ruoyi.system.entity.novel.NovelUserBookshelfEntity" id="NovelUserBookshelfResult">
            <result property="id"    column="id"    />
            <result property="userId"    column="user_id"    />
            <result property="novelId"    column="novel_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectNovelUserBookshelfVo">
        select id, user_id, novel_id, gmt_create, gmt_modified from tb_novel_user_bookshelf
    </sql>

    <select id="selectNovelUserBookshelfList" parameterType="com.ruoyi.system.entity.novel.NovelUserBookshelfEntity" resultMap="NovelUserBookshelfResult">
        <include refid="selectNovelUserBookshelfVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="novelId != null ">and novel_id = #{novelId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectNovelUserBookshelfById" parameterType="Long" resultMap="NovelUserBookshelfResult">
            <include refid="selectNovelUserBookshelfVo"/>
            where id = #{id}
    </select>
    <select id="selectInUserBookshelf" resultType="java.lang.Long">
        select novel_id from tb_novel_user_bookshelf where user_id = #{userId}
        and novel_id in
        <foreach collection="novelIds" item="novelId" open="(" separator="," close=")">
            #{novelId}
        </foreach>
    </select>
    <select id="selectByUserId" resultMap="NovelUserBookshelfResult">
        <include refid="selectNovelUserBookshelfVo"/>
        where user_id = #{userId}
        order by id desc
    </select>

    <insert id="insertNovelUserBookshelf" parameterType="com.ruoyi.system.entity.novel.NovelUserBookshelfEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_novel_user_bookshelf
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,</if>
                    <if test="novelId != null">novel_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},</if>
                    <if test="novelId != null">#{novelId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.ruoyi.system.entity.novel.NovelUserBookshelfEntity">
        insert into tb_novel_user_bookshelf(user_id, novel_id)
        values (#{userId}, #{novelId}) on duplicate key
        update
            gmt_modified = now()
    </insert>

    <update id="updateNovelUserBookshelf" parameterType="com.ruoyi.system.entity.novel.NovelUserBookshelfEntity">
        update tb_novel_user_bookshelf
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="novelId != null">novel_id = #{novelId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNovelUserBookshelfById" parameterType="Long">
        delete from tb_novel_user_bookshelf where id = #{id}
    </delete>

    <delete id="deleteNovelUserBookshelfByIds" parameterType="String">
        delete from tb_novel_user_bookshelf where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByNovelIdAndUserId">
        delete from tb_novel_user_bookshelf where novel_id = #{novelId} and user_id = #{userId}
    </delete>
</mapper>