<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.novel.NovelUserMapper">

    <resultMap type="com.ruoyi.system.entity.novel.NovelUserEntity" id="NovelUserResult">
            <result property="id"    column="id"    />
            <result property="avatar"    column="avatar"    />
            <result property="nickname"    column="nickname"    />
            <result property="openid"    column="openid"    />
            <result property="appId"    column="app_id"    />
            <result property="channelId"    column="channel_id"    />
            <result property="os"    column="os"    />
            <result property="phone"    column="phone"    />
            <result property="reportData"    column="report_data"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="reportStat"    column="report_stat"    />
    </resultMap>

    <sql id="selectNovelUserVo">
        select id, avatar, nickname,channel_id,app_id,phone,os,report_data, openid, gmt_create, gmt_modified,report_stat from tb_novel_user
    </sql>

    <select id="selectNovelUserList" parameterType="com.ruoyi.system.req.novel.manage.NovelUserReq" resultMap="NovelUserResult">
        <include refid="selectNovelUserVo"/>
        <where>
                        <if test="id != null "> and id = #{id}</if>
                        <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
                        <if test="os != null  and os != ''"> and os = #{os}</if>
                        <if test="channelId != null "> and channel_id = #{channelId}</if>
                        <if test="appId != null "> and app_id = #{appId}</if>
                        <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
                        <if test="endDate != null "> and date(gmt_modified) &lt;= #{endDate}</if>
        </where>
        order by id desc
    </select>

    <select id="selectNovelUserById" parameterType="Long" resultMap="NovelUserResult">
            <include refid="selectNovelUserVo"/>
            where id = #{id}
    </select>

    <insert id="insertNovelUser" parameterType="com.ruoyi.system.entity.novel.NovelUserEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_novel_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="avatar != null ">avatar,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="os != null and os != ''">os,</if>
            <if test="openid != null ">channel_id,</if>
            <if test="reportData != null ">report_data,</if>
            <if test="appChannel != null and appChannel != ''">app_channel,</if>
            <if test="reportStat != null ">report_stat,</if>
            <if test="clickid != null and clickid != ''">clickid,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="avatar != null ">#{avatar},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="os != null and os != ''">#{os},</if>
            <if test="openid != null ">#{channelId},</if>
            <if test="reportData != null ">#{reportData},</if>
            <if test="appChannel != null and appChannel != ''">#{appChannel},</if>
            <if test="reportStat != null ">#{reportStat},</if>
            <if test="clickid != null and clickid != ''">#{clickid},</if>
        </trim>
        on duplicate key update
        gmt_modified = now()
    </insert>

    <update id="updateNovelUser" parameterType="com.ruoyi.system.entity.novel.NovelUserEntity">
        update tb_novel_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="os != null and os != ''">os = #{os},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="reportData != null">report_data = #{reportData},</if>
            <if test="reportStat != null ">report_stat = #{reportStat},</if>
            <if test="clickid != null and clickid != ''">clickid = #{clickid},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNovelUserById" parameterType="Long">
        delete from tb_novel_user where id = #{id}
    </delete>

    <delete id="deleteNovelUserByIds" parameterType="String">
        delete from tb_novel_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
