<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.novel.NovelMiniappMapper">

    <resultMap type="com.ruoyi.system.entity.novel.NovelMiniappEntity" id="NovelMiniappResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="appName"    column="app_name"    />
            <result property="appSecret"    column="app_secret"    />
            <result property="adConfig"    column="ad_config"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectNovelMiniappVo">
        select id, app_id, app_secret,app_name,ad_config, gmt_create, gmt_modified from tb_novel_miniapp
    </sql>

    <select id="selectNovelMiniappList" parameterType="com.ruoyi.system.entity.novel.NovelMiniappEntity" resultMap="NovelMiniappResult">
        <include refid="selectNovelMiniappVo"/>
        <where>
                        <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
                        <if test="appName != null  and appName != ''"> and app_name = #{appName}</if>
                        <if test="appSecret != null  and appSecret != ''"> and app_secret = #{appSecret}</if>
                        <if test="adConfig != null  and adConfig != ''"> and ad_config = #{adConfig}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectNovelMiniappById" parameterType="String" resultMap="NovelMiniappResult">
            <include refid="selectNovelMiniappVo"/>
            where id = #{id}
    </select>

    <select id="selectByAppId" parameterType="String" resultMap="NovelMiniappResult">
        <include refid="selectNovelMiniappVo"/>
        where app_id = #{appId}
    </select>

    <insert id="insertNovelMiniapp" parameterType="com.ruoyi.system.entity.novel.NovelMiniappEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_novel_miniapp
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="appSecret != null and appSecret != ''">app_secret,</if>
                    <if test="appName != null and appName != ''">app_name,</if>
                    <if test="adConfig != null and adConfig != ''">ad_config,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="appSecret != null and appSecret != ''">#{appSecret},</if>
                    <if test="appName != null and appName != ''">#{appName},</if>
                    <if test="adConfig != null and adConfig != ''">#{adConfig},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateNovelMiniapp" parameterType="com.ruoyi.system.entity.novel.NovelMiniappEntity">
        update tb_novel_miniapp
        <trim prefix="SET" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="appSecret != null and appSecret != ''">app_secret = #{appSecret},</if>
                    <if test="appName != null and appName != ''">app_name = #{appName},</if>
                    <if test="adConfig != null and adConfig != ''">ad_config = #{adConfig},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNovelMiniappById" parameterType="String">
        delete from tb_novel_miniapp where id = #{id}
    </delete>

    <delete id="deleteNovelMiniappByIds" parameterType="String">
        delete from tb_novel_miniapp where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
