<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.novel.NovelChannelDayStatisticsMapper">

    <resultMap type="com.ruoyi.system.entity.novel.NovelChannelDayStatisticsEntity" id="NovelChannelDayStatisticsResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="channelId"    column="channel_id"    />
            <result property="newUserCount"    column="new_user_count"    />
            <result property="watchUserCount"    column="watch_user_count"    />
            <result property="watchChapterSum"    column="watch_chapter_sum"    />
            <result property="adExposure"    column="ad_exposure"    />
            <result property="adUnlockPv"    column="ad_unlock_pv"    />
            <result property="adUnlockUv"    column="ad_unlock_uv"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectNovelChannelDayStatisticsVo">
        select id, cur_date, channel_id, new_user_count, watch_user_count, watch_chapter_sum, ad_exposure, ad_unlock_pv, ad_unlock_uv, gmt_create, gmt_modified from tb_novel_channel_day_statistics
    </sql>

    <select id="selectNovelChannelDayStatisticsList" parameterType="com.ruoyi.system.req.novel.manage.NovelChannelDayStatisticsReq" resultMap="NovelChannelDayStatisticsResult">
        <include refid="selectNovelChannelDayStatisticsVo"/>
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="channelId != null "> and channel_id = #{channelId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectNovelChannelDayStatisticsById" parameterType="Long" resultMap="NovelChannelDayStatisticsResult">
            <include refid="selectNovelChannelDayStatisticsVo"/>
            where id = #{id}
    </select>
    <select id="selectByDateAndChannelId"
            resultType="com.ruoyi.system.entity.novel.NovelChannelDayStatisticsEntity">
        <include refid="selectNovelChannelDayStatisticsVo"/>
        where cur_date = #{today} and channel_id = #{channelId}
    </select>

    <insert id="insertNovelChannelDayStatistics" parameterType="com.ruoyi.system.entity.novel.NovelChannelDayStatisticsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_novel_channel_day_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">cur_date,</if>
                    <if test="channelId != null">channel_id,</if>
                    <if test="newUserCount != null">new_user_count,</if>
                    <if test="watchUserCount != null">watch_user_count,</if>
                    <if test="watchChapterSum != null">watch_chapter_sum,</if>
                    <if test="adExposure != null">ad_exposure,</if>
                    <if test="adUnlockPv != null">ad_unlock_pv,</if>
                    <if test="adUnlockUv != null">ad_unlock_uv,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">#{curDate},</if>
                    <if test="channelId != null">#{channelId},</if>
                    <if test="newUserCount != null">#{newUserCount},</if>
                    <if test="watchUserCount != null">#{watchUserCount},</if>
                    <if test="watchChapterSum != null">#{watchChapterSum},</if>
                    <if test="adExposure != null">#{adExposure},</if>
                    <if test="adUnlockPv != null">#{adUnlockPv},</if>
                    <if test="adUnlockUv != null">#{adUnlockUv},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>
    <update id="addNovelStat" parameterType="com.ruoyi.system.req.novel.web.NovelChannelDayStatAddReq">
        update tb_novel_channel_day_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="newUserCountAdd != null">new_user_count =new_user_count+ #{newUserCountAdd},</if>
            <if test="watchUserCountAdd != null">watch_user_count = watch_user_count+#{watchUserCountAdd},</if>
            <if test="watchChapterSumAdd != null">watch_chapter_sum =watch_chapter_sum+ #{watchChapterSumAdd},</if>
            <if test="adExposureAdd != null">ad_exposure =ad_exposure+ #{adExposureAdd},</if>
            <if test="adUnlockPvAdd != null">ad_unlock_pv = ad_unlock_pv+#{adUnlockPvAdd},</if>
            <if test="adUnlockUvAdd != null">ad_unlock_uv = ad_unlock_uv+#{adUnlockUvAdd},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateNovelChannelDayStatistics" parameterType="com.ruoyi.system.entity.novel.NovelChannelDayStatisticsEntity">
        update tb_novel_channel_day_statistics
        <trim prefix="SET" suffixOverrides=",">
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="channelId != null">channel_id = #{channelId},</if>
                    <if test="newUserCount != null">new_user_count = #{newUserCount},</if>
                    <if test="watchUserCount != null">watch_user_count = #{watchUserCount},</if>
                    <if test="watchChapterSum != null">watch_chapter_sum = #{watchChapterSum},</if>
                    <if test="adExposure != null">ad_exposure = #{adExposure},</if>
                    <if test="adUnlockPv != null">ad_unlock_pv = #{adUnlockPv},</if>
                    <if test="adUnlockUv != null">ad_unlock_uv = #{adUnlockUv},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNovelChannelDayStatisticsById" parameterType="Long">
        delete from tb_novel_channel_day_statistics where id = #{id}
    </delete>

    <delete id="deleteNovelChannelDayStatisticsByIds" parameterType="String">
        delete from tb_novel_channel_day_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
