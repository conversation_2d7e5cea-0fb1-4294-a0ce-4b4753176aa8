<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.novel.NovelUserUnlockMapper">

    <resultMap type="com.ruoyi.system.entity.novel.NovelUserUnlockEntity" id="NovelUserUnlockResult">
            <result property="id"    column="id"    />
            <result property="userId"    column="user_id"    />
            <result property="novelId"    column="novel_id"    />
            <result property="chapterSeq"    column="chapter_seq"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectNovelUserUnlockVo">
        select id, user_id, novel_id, chapter_seq, gmt_create, gmt_modified from tb_novel_user_unlock
    </sql>

    <select id="selectNovelUserUnlockList" parameterType="com.ruoyi.system.entity.novel.NovelUserUnlockEntity" resultMap="NovelUserUnlockResult">
        <include refid="selectNovelUserUnlockVo"/>
        <where>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="novelId != null "> and novel_id = #{novelId}</if>
                        <if test="chapterSeq != null "> and chapter_seq = #{chapterSeq}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectNovelUserUnlockById" parameterType="Long" resultMap="NovelUserUnlockResult">
            <include refid="selectNovelUserUnlockVo"/>
            where id = #{id}
    </select>

    <insert id="insertNovelUserUnlock" parameterType="com.ruoyi.system.entity.novel.NovelUserUnlockEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_novel_user_unlock
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,</if>
                    <if test="novelId != null">novel_id,</if>
                    <if test="chapterSeq != null">chapter_seq,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},</if>
                    <if test="novelId != null">#{novelId},</if>
                    <if test="chapterSeq != null">#{chapterSeq},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.ruoyi.system.entity.novel.NovelUserUnlockEntity">
        insert into tb_novel_user_unlock(`user_id`, `novel_id`, `chapter_seq`)
        values (#{userId}, #{novelId}, #{chapterSeq})
        on duplicate key update
        gmt_modified = now()
    </insert>
    <insert id="batchInsert">
        insert IGNORE into tb_novel_user_unlock(`user_id`, `novel_id`, `chapter_seq`)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId}, #{item.novelId}, #{item.chapterSeq})
        </foreach>
    </insert>

    <update id="updateNovelUserUnlock" parameterType="com.ruoyi.system.entity.novel.NovelUserUnlockEntity">
        update tb_novel_user_unlock
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="novelId != null">novel_id = #{novelId},</if>
                    <if test="chapterSeq != null">chapter_seq = #{chapterSeq},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNovelUserUnlockById" parameterType="Long">
        delete from tb_novel_user_unlock where id = #{id}
    </delete>

    <delete id="deleteNovelUserUnlockByIds" parameterType="String">
        delete from tb_novel_user_unlock where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>