<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.novel.NovelChannelPromotionMapper">

    <resultMap type="com.ruoyi.system.entity.novel.NovelChannelPromotionEntity" id="NovelChannelPromotionResult">
        <result property="id" column="id"/>
        <result property="promotionUrl" column="promotion_url"/>
        <result property="channel" column="channel"/>
        <result property="novelId" column="novel_id"/>
        <result property="wxBookId" column="wx_book_id"/>
        <result property="dyBookId" column="dy_book_id"/>
        <result property="chapterSeq" column="chapter_seq"/>
        <result property="channelName" column="channel_name"/>
        <result property="appChannel" column="app_channel"/>
        <result property="lockChapterSeq" column="lock_chapter_seq"/>
        <result property="adUnlockSeq" column="ad_unlock_seq"/>
        <result property="type" column="type"/>
        <result property="iaaConfig" column="iaa_config"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectNovelChannelPromotionVo">
        select id,
               promotion_url,
               channel,
               ad_unlock_seq,
               novel_id,
               wx_book_id,
               dy_book_id,
               chapter_seq,
               channel_name,
               app_channel,
               lock_chapter_seq,
               type,
               iaa_config,
               gmt_create,
               gmt_modified
        from tb_novel_channel_promotion
    </sql>

    <select id="selectNovelChannelPromotionList"
            parameterType="com.ruoyi.system.entity.novel.NovelChannelPromotionEntity"
            resultMap="NovelChannelPromotionResult">
        <include refid="selectNovelChannelPromotionVo"/>
        <where>
            <if test="promotionUrl != null  and promotionUrl != ''">and promotion_url = #{promotionUrl}</if>
            <if test="channel != null  and channel != ''">and channel = #{channel}</if>
            <if test="channelName != null  and channelName != ''">and channel_name like concat('%', #{channelName},
                '%')
            </if>
            <if test="appChannel != null and appChannel != ''">and app_channel = #{appChannel}</if>
        </where>
        order by id desc
    </select>

    <select id="selectNovelChannelPromotionById"  resultMap="NovelChannelPromotionResult">
        <include refid="selectNovelChannelPromotionVo"/>
        where id = #{id}
    </select>

    <insert id="insertNovelChannelPromotion" parameterType="com.ruoyi.system.entity.novel.NovelChannelPromotionEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_novel_channel_promotion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="promotionUrl != null">promotion_url,</if>
            <if test="channel != null">channel,</if>
            <if test="novelId != null">novel_id,</if>
            <if test="wxBookId != null">wx_book_id,</if>
            <if test="dyBookId != null">dy_book_id,</if>
            <if test="chapterSeq != null">chapter_seq,</if>
            <if test="channelName != null">channel_name,</if>
            <if test="appChannel != null and appChannel != ''">app_channel,</if>
            <if test="lockChapterSeq != null">lock_chapter_seq,</if>
            <if test="type != null">type,</if>
            <if test="iaaConfig != null">iaa_config,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="promotionUrl != null">#{promotionUrl},</if>
            <if test="channel != null">#{channel},</if>
            <if test="novelId != null">#{novelId},</if>
            <if test="wxBookId != null">#{wxBookId},</if>
            <if test="dyBookId != null">#{dyBookId},</if>
            <if test="chapterSeq != null">#{chapterSeq},</if>
            <if test="channelName != null">#{channelName},</if>
            <if test="appChannel != null and appChannel != ''">#{appChannel},</if>
            <if test="lockChapterSeq != null">#{lockChapterSeq},</if>
            <if test="type != null">#{type},</if>
            <if test="iaaConfig != null">#{iaaConfig},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateNovelChannelPromotion" parameterType="com.ruoyi.system.entity.novel.NovelChannelPromotionEntity">
        update tb_novel_channel_promotion
        <trim prefix="SET" suffixOverrides=",">
            <if test="promotionUrl != null">promotion_url = #{promotionUrl},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="novelId != null">novel_id = #{novelId},</if>
            <if test="wxBookId != null">wx_book_id = #{wxBookId},</if>
            <if test="dyBookId != null">dy_book_id = #{dyBookId},</if>
            <if test="chapterSeq != null">chapter_seq = #{chapterSeq},</if>
            <if test="channelName != null">channel_name = #{channelName},</if>
            <if test="appChannel != null and appChannel != ''">app_channel = #{appChannel},</if>
            <if test="lockChapterSeq != null">lock_chapter_seq = #{lockChapterSeq},</if>
            <if test="type != null">type = #{type},</if>
            <if test="iaaConfig != null">iaa_config = #{iaaConfig},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNovelChannelPromotionById" parameterType="String">
        delete
        from tb_novel_channel_promotion
        where id = #{id}
    </delete>

    <delete id="deleteNovelChannelPromotionByIds" parameterType="String">
        delete from tb_novel_channel_promotion where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
