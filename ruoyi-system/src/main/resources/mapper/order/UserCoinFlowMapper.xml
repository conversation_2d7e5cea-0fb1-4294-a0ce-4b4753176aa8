<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.order.UserCoinFlowMapper">

    <resultMap type="com.ruoyi.system.entity.order.UserCoinFlowEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="userId" column="user_id"/>
            <result property="flowType" column="flow_type"/>
            <result property="orderId" column="order_id"/>
            <result property="coin" column="coin"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            user_id,
            flow_type,
            order_id,
            coin,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.order.UserCoinFlowEntity">
        INSERT INTO tb_user_coin_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="flowType != null">
                flow_type,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="coin != null">
                coin
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="flowType != null">
                #{flowType},
            </if>
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="coin != null">
                #{coin}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_user_coin_flow WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.order.UserCoinFlowEntity">
        UPDATE tb_user_coin_flow
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="flowType != null">
                flow_type = #{flowType},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="coin != null">
                coin = #{coin},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_user_coin_flow
        WHERE id = #{id}
    </select>
    <select id="selectListByParam" parameterType="com.ruoyi.system.req.playlet.param.CoinFlowParam" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_user_coin_flow
        <where>
            <if test="lastId!=null" >
                and id &lt; #{lastId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" separator="," open="(" item="userId" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
            </if>
            <if test="flowType != null">
                and flow_type = #{flowType}
            </if>
        </where>
        order by id desc
        <if test="pageSize!=null">
           limit #{pageSize}
        </if>
    </select>

</mapper>