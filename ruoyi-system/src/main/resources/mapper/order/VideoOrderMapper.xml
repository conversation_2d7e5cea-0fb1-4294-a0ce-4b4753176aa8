<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.order.VideoOrderMapper">

    <resultMap type="com.ruoyi.system.entity.order.VideoOrderEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="videoId" column="video_id"/>
            <result property="videoGroupId" column="video_group_id"/>
            <result property="userId" column="user_id"/>
            <result property="coin" column="coin"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            video_id,
            video_group_id,
            user_id,
            coin,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.order.VideoOrderEntity">
        INSERT INTO tb_video_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="videoId != null">
                video_id,
            </if>
            <if test="videoGroupId != null">
                video_group_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="coin != null">
                coin
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="videoId != null">
                #{videoId},
            </if>
            <if test="videoGroupId != null">
                #{videoGroupId},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="coin != null">
                #{coin}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_video_order WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.order.VideoOrderEntity">
        UPDATE tb_video_order
        <set>
            <if test="videoId != null">
                video_id = #{videoId},
            </if>
            <if test="videoGroupId != null">
                video_group_id = #{videoGroupId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="coin != null">
                coin = #{coin},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video_order
        WHERE id = #{id}
    </select>
    <select id="selectConsumerSum" resultType="com.ruoyi.system.bo.playlet.ConsumerSumBo">
        SELECT
        user_id as userId,
        SUM(coin) as consumerCoinSum
        FROM tb_video_order
        <where>
            user_id in
            <foreach collection="userIds" separator="," open="(" item="userId" close=")">
                #{userId}
            </foreach>
        </where>
        GROUP BY user_id
    </select>
    <select id="selectListByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_video_order
        WHERE id in
        <foreach collection="ids" close=")" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>