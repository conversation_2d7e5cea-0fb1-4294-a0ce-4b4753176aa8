<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.app.AppVipConfigMapper">

    <resultMap type="com.ruoyi.system.entity.app.AppVipConfigEntity" id="AppVipConfigResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="payType" column="pay_type"/>
        <result property="renewalPeriod" column="renewal_period"/>
        <result property="trialDuration" column="trial_duration"/>
        <result property="discountText" column="discount_text"/>
        <result property="headText" column="head_text"/>
        <result property="amount" column="amount"/>
        <result property="bottomText" column="bottom_text"/>
        <result property="status" column="status"/>
        <result property="expireDay" column="expire_day"/>
        <result property="operId" column="oper_id"/>
        <result property="platform" column="platform"/>
        <result property="operName" column="oper_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="renewalAmount" column="renewal_amount"/>
        <result property="aboveText" column="above_text"/>
        <result property="lowerText" column="lower_text"/>
        <result property="showPosition" column="show_position"/>
        <result property="backInterceptPrice" column="back_intercept_price"/>
        <result property="backInterceptTitle" column="back_intercept_title"/>
        <result property="isTrial" column="is_trial"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <sql id="selectAppVipConfigVo">
        select id,
        name,
        pay_type,
        renewal_period,
        trial_duration,
        discount_text,
        head_text,
        amount,
        bottom_text,
        status,
        expire_day,
        oper_id,
        platform,
        oper_name,
        gmt_create,
        gmt_modified,
        renewal_amount,
        above_text,
        lower_text,
        show_position,
        back_intercept_price,
        back_intercept_title,
        is_trial,
        sort_order
        from tb_app_vip_config
    </sql>
    <delete id="cancelAppVipConfigPosition">
        update tb_app_vip_config set show_position = show_position <![CDATA[ & (~ (1 << (2 - 1)))]]>
    </delete>

    <select id="selectAppVipConfigList" parameterType="com.ruoyi.system.entity.app.AppVipConfigEntity"
            resultMap="AppVipConfigResult">
        <include refid="selectAppVipConfigVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="payType != null ">and pay_type = #{payType}</if>
            <if test="renewalPeriod != null ">and renewal_period = #{renewalPeriod}</if>
            <if test="trialDuration != null ">and trial_duration = #{trialDuration}</if>
            <if test="discountText != null  and discountText != ''">and discount_text = #{discountText}</if>
            <if test="headText != null  and headText != ''">and head_text = #{headText}</if>
            <if test="amount != null  and amount != ''">and amount = #{amount}</if>
            <if test="bottomText != null  and bottomText != ''">and bottom_text = #{bottomText}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="expireDay != null ">and expire_day = #{expireDay}</if>
            <if test="operId != null ">and oper_id = #{operId}</if>
            <if test="operName != null  and operName != ''">and oper_name like concat('%', #{operName}, '%')</if>
            <if test="platform!=null">and platform=#{platform}</if>
            <if test="renewalAmount!=null">and renewal_amount=#{renewalAmount}</if>
            <if test="aboveText!=null">and above_text=#{aboveText}</if>
            <if test="lowerText!=null">and lower_text=#{lowerText}</if>
            <if test="isTrial!=null">and is_trial=#{isTrial}</if>
        </where>
        order by `status` desc, sort_order, gmt_modified desc
    </select>
    <select id="selectAppVipConfigListByState"
            resultMap="AppVipConfigResult">
        <include refid="selectAppVipConfigVo"/>
        <where>
            <if test="state!=null">
                status=#{state}
            </if>
        </where>
    </select>


    <insert id="insertAppVipConfig" parameterType="com.ruoyi.system.entity.app.AppVipConfigEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_app_vip_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="payType != null">pay_type,</if>
            <if test="renewalPeriod != null">renewal_period,</if>
            <if test="trialDuration != null">trial_duration,</if>
            <if test="discountText != null and discountText != ''">discount_text,</if>
            <if test="headText != null and headText != ''">head_text,</if>
            <if test="amount != null and amount != ''">amount,</if>
            <if test="bottomText != null and bottomText != ''">bottom_text,</if>
            <if test="status != null">status,</if>
            <if test="expireDay != null">expire_day,</if>
            <if test="operId != null">oper_id,</if>
            <if test="operName != null and operName != ''">oper_name,</if>
            <if test="platform!=null">platform,</if>
            <if test="renewalAmount != null">renewal_amount,</if>
            <if test="aboveText != null">above_text,</if>
            <if test="lowerText != null">lower_text,</if>
            <if test="isTrial != null">is_trial,</if>
            <if test="backInterceptPrice != null">back_intercept_price,</if>
            <if test="backInterceptTitle != null">back_intercept_title,</if>
            <if test="showPosition != null">show_position,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="payType != null">#{payType},</if>
            <if test="renewalPeriod != null">#{renewalPeriod},</if>
            <if test="trialDuration != null">#{trialDuration},</if>
            <if test="discountText != null and discountText != ''">#{discountText},</if>
            <if test="headText != null and headText != ''">#{headText},</if>
            <if test="amount != null and amount != ''">#{amount},</if>
            <if test="bottomText != null and bottomText != ''">#{bottomText},</if>
            <if test="status != null">#{status},</if>
            <if test="expireDay != null">#{expireDay},</if>
            <if test="operId != null">#{operId},</if>
            <if test="operName != null and operName != ''">#{operName},</if>
            <if test="platform!=null">#{platform},</if>
            <if test="renewalAmount != null">#{renewalAmount},</if>
            <if test="aboveText != null">#{aboveText},</if>
            <if test="lowerText != null">#{lowerText},</if>
            <if test="isTrial != null">#{isTrial},</if>
            <if test="backInterceptPrice != null">#{backInterceptPrice},</if>
            <if test="backInterceptTitle != null">#{backInterceptTitle},</if>
            <if test="showPosition != null">#{showPosition},</if>
        </trim>
    </insert>

    <update id="updateAppVipConfig" parameterType="com.ruoyi.system.entity.app.AppVipConfigEntity">
        update tb_app_vip_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="renewalPeriod != null">renewal_period = #{renewalPeriod},</if>
            <if test="trialDuration != null">trial_duration = #{trialDuration},</if>
            <if test="discountText != null and discountText != ''">discount_text = #{discountText},</if>
            <if test="headText != null and headText != ''">head_text = #{headText},</if>
            <if test="amount != null and amount != ''">amount = #{amount},</if>
            <if test="bottomText != null and bottomText != ''">bottom_text = #{bottomText},</if>
            <if test="status != null">status = #{status},</if>
            <if test="expireDay != null">expire_day = #{expireDay},</if>
            <if test="operId != null">oper_id = #{operId},</if>
            <if test="operName != null and operName != ''">oper_name = #{operName},</if>
            <if test="platform!=null">platform=#{platform},</if>
            <if test="renewalAmount != null">renewal_amount = #{renewalAmount},</if>
            <if test="aboveText != null">above_text=#{aboveText},</if>
            <if test="lowerText != null">lower_text=#{lowerText},</if>
            <if test="showPosition != null">show_position=#{showPosition},</if>
            <if test="backInterceptTitle != null">back_intercept_title=#{backInterceptTitle},</if>
            <if test="backInterceptPrice != null">back_intercept_price=#{backInterceptPrice},</if>
            <if test="isTrial != null">is_trial=#{isTrial},</if>
            <if test="sortOrder != null">sort_order=#{sortOrder}</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
