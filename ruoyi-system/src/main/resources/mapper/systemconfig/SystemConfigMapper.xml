<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.systemconfig.SystemConfigMapper">

    <resultMap type="com.ruoyi.system.entity.systemconfig.SystemConfigEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="configKey" column="config_key"/>
            <result property="configValue" column="config_value"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            config_key,
            config_value,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.systemconfig.SystemConfigEntity">
        INSERT INTO tb_system_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configKey != null">
                config_key,
            </if>
            <if test="configValue != null">
                config_value
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configKey != null">
                #{configKey},
            </if>
            <if test="configValue != null">
                #{configValue}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_system_config WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.systemconfig.SystemConfigEntity">
        UPDATE tb_system_config
        <set>
            <if test="configKey != null">
                config_key = #{configKey},
            </if>
            <if test="configValue != null">
                config_value = #{configValue},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    <insert id="updateByConfigKey" parameterType="com.ruoyi.system.entity.systemconfig.SystemConfigEntity">
        insert into tb_system_config (config_key,config_value)
        values( #{configKey},#{configValue})
        on duplicate key update
        config_value = #{configValue}
    </insert>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_system_config
        WHERE id = #{id}
    </select>
    <select id="selectListByConfigKeys"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_system_config
        where config_key in
        <foreach collection="keys" item="configKey" open="(" separator="," close=")">
            #{configKey}
        </foreach>
    </select>
    <select id="selectByConfigKey"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from tb_system_config
            where config_key = #{configKey}
    </select>

</mapper>