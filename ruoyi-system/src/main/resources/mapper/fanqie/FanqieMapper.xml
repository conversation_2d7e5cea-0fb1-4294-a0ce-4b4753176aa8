<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fanqie.FanqieMapper">

    <resultMap type="com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity"
               id="AgtDataSupplierSnapshotFanqieResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="deviceId" column="device_id"/>
        <result property="openId" column="open_id"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="payWay" column="pay_way"/>
        <result property="payFee" column="pay_fee"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="payTime" column="pay_time"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="bookId" column="book_id"/>
        <result property="isActivity" column="is_activity"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="registerTime" column="register_time"/>
        <result property="recentReadBookId" column="recent_read_book_id"/>
        <result property="externalId" column="external_id"/>
        <result property="orderType" column="order_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>


    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_data_supplier_snapshot_fanqie
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].deviceId != null">device_id,</if>
            <if test="list[0].openId != null">open_id,</if>
            <if test="list[0].outTradeNo != null">out_trade_no,</if>
            <if test="list[0].payWay != null">pay_way,</if>
            <if test="list[0].payFee != null">pay_fee,</if>
            <if test="list[0].status != null">status,</if>
            <if test="list[0].createTime != null">create_time,</if>
            <if test="list[0].payTime != null">pay_time,</if>
            <if test="list[0].promotionId != null">promotion_id,</if>
            <if test="list[0].bookId != null">book_id,</if>
            <if test="list[0].isActivity != null">is_activity,</if>
            <if test="list[0].tradeNo != null">trade_no,</if>
            <if test="list[0].registerTime != null">register_time,</if>
            <if test="list[0].recentReadBookId != null">recent_read_book_id,</if>
            <if test="list[0].externalId != null">external_id,</if>
            <if test="list[0].orderType != null">order_type,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.curDate != null">#{entity.curDate},</if>
                <if test="entity.curHour != null">#{entity.curHour},</if>
                <if test="entity.deviceId != null">#{entity.deviceId},</if>
                <if test="entity.openId != null">#{entity.openId},</if>
                <if test="entity.outTradeNo != null">#{entity.outTradeNo},</if>
                <if test="entity.payWay != null">#{entity.payWay},</if>
                <if test="entity.payFee != null">#{entity.payFee},</if>
                <if test="entity.status != null">#{entity.status},</if>
                <if test="entity.createTime != null">#{entity.createTime},</if>
                <if test="entity.payTime != null">#{entity.payTime},</if>
                <if test="entity.promotionId != null">#{entity.promotionId},</if>
                <if test="entity.bookId != null">#{entity.bookId},</if>
                <if test="entity.isActivity != null">#{entity.isActivity},</if>
                <if test="entity.tradeNo != null">#{entity.tradeNo},</if>
                <if test="entity.registerTime != null">#{entity.registerTime},</if>
                <if test="entity.recentReadBookId != null">#{entity.recentReadBookId},</if>
                <if test="entity.externalId != null">#{entity.externalId},</if>
                <if test="entity.orderType != null">#{entity.orderType},</if>
            </trim>
        </foreach>
        on duplicate key update
        advertiser_id = values(advertiser_id),
        cur_date = values(cur_date),
        cur_hour = values(cur_hour)
    </insert>

    <select id="selectIncomeByDay"
            resultType="com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity">
        SELECT
        sum( pay_fee ) AS pay_fee,
        cur_date
        FROM
        tb_agt_data_supplier_snapshot_fanqie
        WHERE
        cur_date BETWEEN #{start} and #{end}
        GROUP BY cur_date
    </select>

    <select id="selectPayCount" resultType="java.lang.Integer">
        SELECT
        (SELECT COUNT(DISTINCT device_id)
        FROM tb_agt_data_supplier_snapshot_fanqie
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq})
        -
        (SELECT COUNT(DISTINCT device_id)
        FROM tb_agt_data_supplier_snapshot_fanqie
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq} - 1) AS pay_count
    </select>

    <select id="selectOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_agt_data_supplier_snapshot_fanqie WHERE advertiser_id=#{adId} and cur_date=#{dateReq} and cur_hour=#{hourReq}
    </select>
</mapper>