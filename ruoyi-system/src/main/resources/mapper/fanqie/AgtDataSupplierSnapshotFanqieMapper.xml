<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fanqie.AgtDataSupplierSnapshotFanqieMapper">

    <resultMap type="com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity" id="AgtDataSupplierSnapshotFanqieResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="curHour"    column="cur_hour"    />
            <result property="deviceId"    column="device_id"    />
            <result property="openId"    column="open_id"    />
            <result property="outTradeNo"    column="out_trade_no"    />
            <result property="payWay"    column="pay_way"    />
            <result property="payFee"    column="pay_fee"    />
            <result property="status"    column="status"    />
            <result property="createTime"    column="create_time"    />
            <result property="payTime"    column="pay_time"    />
            <result property="promotionId"    column="promotion_id"    />
            <result property="bookId"    column="book_id"    />
            <result property="isActivity"    column="is_activity"    />
            <result property="tradeNo"    column="trade_no"    />
            <result property="registerTime"    column="register_time"    />
            <result property="recentReadBookId"    column="recent_read_book_id"    />
            <result property="externalId"    column="external_id"    />
            <result property="orderType"    column="order_type"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotFanqieVo">
        select id, advertiser_id, cur_date, cur_hour, device_id, open_id, out_trade_no, pay_way, pay_fee, status, create_time, pay_time, promotion_id, book_id, is_activity, trade_no, register_time, recent_read_book_id, external_id, order_type, gmt_create, gmt_modified from tb_agt_data_supplier_snapshot_fanqie
    </sql>

    <select id="selectAgtDataSupplierSnapshotFanqieList" parameterType="com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity" resultMap="AgtDataSupplierSnapshotFanqieResult">
        <include refid="selectAgtDataSupplierSnapshotFanqieVo"/>
        <where>
                        <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="curHour != null "> and cur_hour = #{curHour}</if>
                        <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
                        <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
                        <if test="outTradeNo != null  and outTradeNo != ''"> and out_trade_no = #{outTradeNo}</if>
                        <if test="payWay != null "> and pay_way = #{payWay}</if>
                        <if test="payFee != null "> and pay_fee = #{payFee}</if>
                        <if test="status != null "> and status = #{status}</if>
                        <if test="payTime != null "> and pay_time = #{payTime}</if>
                        <if test="promotionId != null "> and promotion_id = #{promotionId}</if>
                        <if test="bookId != null "> and book_id = #{bookId}</if>
                        <if test="isActivity != null "> and is_activity = #{isActivity}</if>
                        <if test="tradeNo != null "> and trade_no = #{tradeNo}</if>
                        <if test="registerTime != null "> and register_time = #{registerTime}</if>
                        <if test="recentReadBookId != null  and recentReadBookId != ''"> and recent_read_book_id = #{recentReadBookId}</if>
                        <if test="externalId != null  and externalId != ''"> and external_id = #{externalId}</if>
                        <if test="orderType != null "> and order_type = #{orderType}</if>
                        <if test="gmtCreate != null  and gmtCreate != ''"> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null  and gmtModified != ''"> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataSupplierSnapshotFanqieById" parameterType="Long" resultMap="AgtDataSupplierSnapshotFanqieResult">
            <include refid="selectAgtDataSupplierSnapshotFanqieVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataSupplierSnapshotFanqie" parameterType="com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity">
        insert into tb_agt_data_supplier_snapshot_fanqie
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="advertiserId != null">advertiser_id,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="curHour != null">cur_hour,</if>
                    <if test="deviceId != null">device_id,</if>
                    <if test="openId != null">open_id,</if>
                    <if test="outTradeNo != null">out_trade_no,</if>
                    <if test="payWay != null">pay_way,</if>
                    <if test="payFee != null">pay_fee,</if>
                    <if test="status != null">status,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="payTime != null">pay_time,</if>
                    <if test="promotionId != null">promotion_id,</if>
                    <if test="bookId != null">book_id,</if>
                    <if test="isActivity != null">is_activity,</if>
                    <if test="tradeNo != null">trade_no,</if>
                    <if test="registerTime != null">register_time,</if>
                    <if test="recentReadBookId != null">recent_read_book_id,</if>
                    <if test="externalId != null">external_id,</if>
                    <if test="orderType != null">order_type,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="advertiserId != null">#{advertiserId},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="curHour != null">#{curHour},</if>
                    <if test="deviceId != null">#{deviceId},</if>
                    <if test="openId != null">#{openId},</if>
                    <if test="outTradeNo != null">#{outTradeNo},</if>
                    <if test="payWay != null">#{payWay},</if>
                    <if test="payFee != null">#{payFee},</if>
                    <if test="status != null">#{status},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="payTime != null">#{payTime},</if>
                    <if test="promotionId != null">#{promotionId},</if>
                    <if test="bookId != null">#{bookId},</if>
                    <if test="isActivity != null">#{isActivity},</if>
                    <if test="tradeNo != null">#{tradeNo},</if>
                    <if test="registerTime != null">#{registerTime},</if>
                    <if test="recentReadBookId != null">#{recentReadBookId},</if>
                    <if test="externalId != null">#{externalId},</if>
                    <if test="orderType != null">#{orderType},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataSupplierSnapshotFanqie" parameterType="com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity">
        update tb_agt_data_supplier_snapshot_fanqie
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="curHour != null">cur_hour = #{curHour},</if>
                    <if test="deviceId != null">device_id = #{deviceId},</if>
                    <if test="openId != null">open_id = #{openId},</if>
                    <if test="outTradeNo != null">out_trade_no = #{outTradeNo},</if>
                    <if test="payWay != null">pay_way = #{payWay},</if>
                    <if test="payFee != null">pay_fee = #{payFee},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="payTime != null">pay_time = #{payTime},</if>
                    <if test="promotionId != null">promotion_id = #{promotionId},</if>
                    <if test="bookId != null">book_id = #{bookId},</if>
                    <if test="isActivity != null">is_activity = #{isActivity},</if>
                    <if test="tradeNo != null">trade_no = #{tradeNo},</if>
                    <if test="registerTime != null">register_time = #{registerTime},</if>
                    <if test="recentReadBookId != null">recent_read_book_id = #{recentReadBookId},</if>
                    <if test="externalId != null">external_id = #{externalId},</if>
                    <if test="orderType != null">order_type = #{orderType},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataSupplierSnapshotFanqieById" parameterType="Long">
        delete from tb_agt_data_supplier_snapshot_fanqie where id = #{id}
    </delete>

    <delete id="deleteAgtDataSupplierSnapshotFanqieByIds" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_fanqie where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>