<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.coupon.PlayletCouponMapper">
    
    <resultMap type="com.ruoyi.system.entity.coupon.PlayletCouponEntity" id="PlayletCouponResult">
        <result property="id"    column="id"    />
        <result property="phone"    column="phone"    />
        <result property="deliverStatus"    column="deliver_status"    />
        <result property="useTime"    column="use_time"    />
        <result property="expiredTime"    column="expired_time"    />
        <result property="useStatus"    column="use_status"    />
        <result property="orderId"    column="order_id"    />
        <result property="videoId"    column="video_id"    />
        <result property="videoName"    column="video_name"    />
        <result property="nickname"    column="nickname"    />
        <result property="userId"    column="user_id"    />
        <result property="openId"    column="open_id"    />
        <result property="productNo"    column="product_no"    />
        <result property="useProductType"    column="use_product_type"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
        <result property="platformType" column="platform_type"/>
        <result property="appName" column="app_name"/>
    </resultMap>

    <sql id="selectPlayletCouponVo">
        select id, phone, deliver_status, use_time, expired_time, use_status, order_id,nickname,user_id,product_no,use_product_type, open_id,video_id, video_name, gmt_create, gmt_modified,platform_type,app_name from tb_playlet_coupon
    </sql>

    <select id="selectPlayletCouponList" parameterType="com.ruoyi.system.entity.coupon.PlayletCouponEntity" resultMap="PlayletCouponResult">
        <include refid="selectPlayletCouponVo"/>
        <where>
            <if test="id!=null">and id =#{id}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
             <if test="userId!=null">and user_id=#{userId}</if>
            <if test="deliverStatus != null "> and deliver_status = #{deliverStatus}</if>
            <if test="useTime != null "> and use_time = #{useTime}</if>
            <if test="expiredTime != null "> and expired_time = #{expiredTime}</if>
            <if test="useStatus != null "> and use_status = #{useStatus}</if>
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="videoId != null  and videoId != ''"> and video_id = #{videoId}</if>
            <if test="videoName != null  and videoName != ''"> and video_name like concat('%', #{videoName}, '%')</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
            <if test="startDate!=null">and gmt_create >= #{startDate}</if>
            <if test="endDate!=null">and gmt_create &lt;=#{endDate}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectPlayletCouponById" parameterType="String" resultMap="PlayletCouponResult">
        <include refid="selectPlayletCouponVo"/>
        where id = #{id}
    </select>
    <select id="selectByOrderId" resultMap="PlayletCouponResult">
        <include refid="selectPlayletCouponVo"/>
        where order_id = #{orderId}
    </select>
    <select id="selectByPhoneAndVideoId" resultMap="PlayletCouponResult">
        <include refid="selectPlayletCouponVo"/>
        where phone = #{phone} and video_id = #{videoId}
        limit 1
    </select>
    <select id="selectUnusedListByPhone" resultMap="PlayletCouponResult">
        <include refid="selectPlayletCouponVo"/>
        where phone = #{phone} and use_status = #{useStatus} and expired_time >= now()
        limit 1
    </select>
    <select id="selectListByPhoneAndStatus" resultMap="PlayletCouponResult">
        <include refid="selectPlayletCouponVo"/>
        where phone = #{phone}
        <if test="useStatus != null">
            and use_status = #{useStatus}
        </if>
        <if test="useStatus == 0">
            and expired_time >= now()
        </if>
    </select>
    <select id="selectExpiredList" resultMap="PlayletCouponResult">
        <include refid="selectPlayletCouponVo"/>
        where expired_time &lt; now() and use_status = #{status} and id &lt; #{lastId} order by id desc
        limit  1000
    </select>

    <insert id="insertPlayletCoupon" parameterType="com.ruoyi.system.entity.coupon.PlayletCouponEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_playlet_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone,</if>
            <if test="deliverStatus != null">deliver_status,</if>
            <if test="useTime != null">use_time,</if>
            <if test="expiredTime != null">expired_time,</if>
            <if test="useStatus != null">use_status,</if>
            <if test="orderId != null and orderId != ''">order_id,</if>
            <if test="videoId != null and videoId != ''">video_id,</if>
            <if test="videoName != null and videoName != ''">video_name,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="productNo != null and productNo != ''">product_no,</if>
            <if test="useProductType != null and useProductType != ''">use_product_type,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="platformType!=null">platform_type,</if>
            <if test="appName!=null">app_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="deliverStatus != null">#{deliverStatus},</if>
            <if test="useTime != null">#{useTime},</if>
            <if test="expiredTime != null">#{expiredTime},</if>
            <if test="useStatus != null">#{useStatus},</if>
            <if test="orderId != null and orderId != ''">#{orderId},</if>
            <if test="videoId != null and videoId != ''">#{videoId},</if>
            <if test="videoName != null and videoName != ''">#{videoName},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="productNo != null and productNo != ''">#{productNo},</if>
            <if test="useProductType != null and useProductType != ''">#{useProductType},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
            <if test="platformType!=null">#{platformType},</if>
            <if test="appName!=null">#{appName},</if>
         </trim>
    </insert>

    <update id="updatePlayletCoupon" parameterType="com.ruoyi.system.entity.coupon.PlayletCouponEntity">
        update tb_playlet_coupon
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="deliverStatus != null">deliver_status = #{deliverStatus},</if>
            <if test="useTime != null">use_time = #{useTime},</if>
            <if test="expiredTime != null">expired_time = #{expiredTime},</if>
            <if test="useStatus != null">use_status = #{useStatus},</if>
            <if test="orderId != null and orderId != ''">order_id = #{orderId},</if>
            <if test="videoId != null and videoId != ''">video_id = #{videoId},</if>
            <if test="videoName != null and videoName != ''">video_name = #{videoName},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="openId != null and openId != ''">open_id = #{openId},</if>
            <if test="platformType!=null">platform_type=#{platformType},</if>
            <if test="appName!=null">app_name=#{appName}</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdateUseStatusByIds">
        update tb_playlet_coupon set use_status=#{status} where id in
        <foreach collection="list" separator="," item="id" close=")" open="(">
            #{id}
        </foreach>
    </update>

    <delete id="deletePlayletCouponById" parameterType="String">
        delete from tb_playlet_coupon where id = #{id}
    </delete>

    <delete id="deletePlayletCouponByIds" parameterType="String">
        delete from tb_playlet_coupon where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>