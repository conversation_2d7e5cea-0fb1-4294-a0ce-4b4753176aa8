<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.coupon.AgentCouponMapper">

    <resultMap type="com.ruoyi.system.entity.coupon.AgentCouponEntity" id="AgentCouponResult">
            <result property="id"    column="id"    />
            <result property="couponCode"    column="coupon_code"    />
            <result property="agentId"    column="agent_id"    />
            <result property="agentNickname"    column="agent_nickname"    />
            <result property="agentUsername"    column="agent_username"    />
            <result property="inviteCode"    column="invite_code"    />
            <result property="couponName"    column="coupon_name"    />
            <result property="couponDesc"    column="coupon_desc"    />
            <result property="couponType"    column="coupon_type"    />
            <result property="couponNum"    column="coupon_num"    />
            <result property="couponClaimExpire"    column="coupon_claim_expire"    />
            <result property="couponUseExpire"    column="coupon_use_expire"    />
            <result property="couponStatus"    column="coupon_status"    />
            <result property="operatorName"    column="operator_name"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgentCouponVo">
        select id, coupon_code, agent_id, agent_nickname, agent_username, invite_code, coupon_name, coupon_desc, coupon_type, coupon_num, coupon_claim_expire, coupon_use_expire, coupon_status, operator_name, gmt_create, gmt_modified from tb_agent_coupon
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.coupon.AgentCouponEntity" resultMap="AgentCouponResult">
        <include refid="selectAgentCouponVo"/>
        <where>
                <if test="couponCode != null  and couponCode != ''"> and coupon_code = #{couponCode}</if>
                <if test="agentId != null  and agentId != ''"> and agent_id = #{agentId}</if>
                <if test="agentNickname != null  and agentNickname != ''"> and agent_nickname like concat('%', #{agentNickname}, '%')</if>
                <if test="agentUsername != null  and agentUsername != ''"> and agent_username like concat('%', #{agentUsername}, '%')</if>
                <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
                <if test="couponName != null  and couponName != ''"> and coupon_name like concat('%', #{couponName}, '%')</if>
                <if test="couponDesc != null  and couponDesc != ''"> and coupon_desc like concat('%', #{couponDesc}, '%')</if>
                <if test="couponType != null "> and coupon_type = #{couponType}</if>
                <if test="couponNum != null "> and coupon_num = #{couponNum}</if>
                <if test="couponClaimExpire != null "> and coupon_claim_expire = #{couponClaimExpire}</if>
                <if test="couponUseExpire != null "> and coupon_use_expire = #{couponUseExpire}</if>
                <if test="couponStatus != null "> and coupon_status = #{couponStatus}</if>
                <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectAgentCouponById" parameterType="Long" resultMap="AgentCouponResult">
            <include refid="selectAgentCouponVo"/>
            where id = #{id}
    </select>

    <select id="selectByCouponCode" resultMap="AgentCouponResult">
        <include refid="selectAgentCouponVo"/>
        where coupon_code = #{couponCode}
    </select>

    <select id="selectListByCouponCode" resultMap="AgentCouponResult">
        <include refid="selectAgentCouponVo"/>
        where coupon_code in
        <foreach collection="couponCodes" item="couponCode" open="(" separator="," close=")">
            #{couponCode}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.coupon.AgentCouponEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agent_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="couponCode != null and couponCode != ''">coupon_code,</if>
                    <if test="agentId != null and agentId != ''">agent_id,</if>
                    <if test="agentNickname != null and agentNickname != ''">agent_nickname,</if>
                    <if test="agentUsername != null and agentUsername != ''">agent_username,</if>
                    <if test="inviteCode != null and inviteCode != ''">invite_code,</if>
                    <if test="couponName != null and couponName != ''">coupon_name,</if>
                    <if test="couponDesc != null and couponDesc != ''">coupon_desc,</if>
                    <if test="couponType != null">coupon_type,</if>
                    <if test="couponNum != null">coupon_num,</if>
                    <if test="couponClaimExpire != null">coupon_claim_expire,</if>
                    <if test="couponUseExpire != null">coupon_use_expire,</if>
                    <if test="couponStatus != null">coupon_status,</if>
                    <if test="operatorName != null and operatorName != ''">operator_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="couponCode != null and couponCode != ''">#{couponCode},</if>
                    <if test="agentId != null and agentId != ''">#{agentId},</if>
                    <if test="agentNickname != null and agentNickname != ''">#{agentNickname},</if>
                    <if test="agentUsername != null and agentUsername != ''">#{agentUsername},</if>
                    <if test="inviteCode != null and inviteCode != ''">#{inviteCode},</if>
                    <if test="couponName != null and couponName != ''">#{couponName},</if>
                    <if test="couponDesc != null and couponDesc != ''">#{couponDesc},</if>
                    <if test="couponType != null">#{couponType},</if>
                    <if test="couponNum != null">#{couponNum},</if>
                    <if test="couponClaimExpire != null">#{couponClaimExpire},</if>
                    <if test="couponUseExpire != null">#{couponUseExpire},</if>
                    <if test="couponStatus != null">#{couponStatus},</if>
                    <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.coupon.AgentCouponEntity">
        update tb_agent_coupon
        <trim prefix="SET" suffixOverrides=",">
                    <if test="couponCode != null and couponCode != ''">coupon_code = #{couponCode},</if>
                    <if test="agentId != null and agentId != ''">agent_id = #{agentId},</if>
                    <if test="agentNickname != null and agentNickname != ''">agent_nickname = #{agentNickname},</if>
                    <if test="agentUsername != null and agentUsername != ''">agent_username = #{agentUsername},</if>
                    <if test="inviteCode != null and inviteCode != ''">invite_code = #{inviteCode},</if>
                    <if test="couponName != null and couponName != ''">coupon_name = #{couponName},</if>
                    <if test="couponDesc != null and couponDesc != ''">coupon_desc = #{couponDesc},</if>
                    <if test="couponType != null">coupon_type = #{couponType},</if>
                    <if test="couponNum != null">coupon_num = #{couponNum},</if>
                    <if test="couponClaimExpire != null">coupon_claim_expire = #{couponClaimExpire},</if>
                    <if test="couponUseExpire != null">coupon_use_expire = #{couponUseExpire},</if>
                    <if test="couponStatus != null">coupon_status = #{couponStatus},</if>
                    <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
