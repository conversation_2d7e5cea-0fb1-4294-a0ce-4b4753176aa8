<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.coupon.AgentCouponUserRecordMapper">

    <resultMap type="com.ruoyi.system.entity.coupon.AgentCouponUserRecordEntity" id="AgentCouponUserRecordResult">
            <result property="id"    column="id"    />
            <result property="requestId"    column="request_id"    />
            <result property="couponNo"    column="coupon_no"    />
            <result property="phone"    column="phone"    />
            <result property="userId"    column="user_id"    />
            <result property="nickname"    column="nickname"    />
            <result property="agentId"    column="agent_id"    />
            <result property="couponCode"    column="coupon_code"    />
            <result property="couponType"    column="coupon_type"    />
            <result property="couponStatus"    column="coupon_status"    />
            <result property="deliverTime"    column="deliver_time"    />
            <result property="claimTime"    column="claim_time"    />
            <result property="claimIp"    column="claim_ip"    />
            <result property="claimExpireTime"    column="claim_expire_time"    />
            <result property="useTime"    column="use_time"    />
            <result property="useIp"    column="use_ip"    />
            <result property="useExpireTime"    column="use_expire_time"    />
            <result property="useExt"    column="use_ext"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgentCouponUserRecordVo">
        select id, request_id, coupon_no, phone, user_id, nickname, agent_id, coupon_code, coupon_type, coupon_status, deliver_time, claim_time, claim_ip, claim_expire_time, use_time, use_ip, use_expire_time, use_ext, gmt_create, gmt_modified from tb_agent_coupon_user_record
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.coupon.AgentCouponUserRecordEntity" resultMap="AgentCouponUserRecordResult">
        <include refid="selectAgentCouponUserRecordVo"/>
        <where>
            <if test="couponNo != null  and couponNo != ''"> and coupon_no = #{couponNo}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="agentId != null and agentId != '' and agentId != 'admin'"> and agent_id = #{agentId}</if>
            <if test="couponCode != null  and couponCode != ''"> and coupon_code = #{couponCode}</if>
            <if test="couponType != null "> and coupon_type = #{couponType}</if>
            <if test="couponStatus != null "> and coupon_status = #{couponStatus}</if>
            <if test="deliverTime != null "> and deliver_time = #{deliverTime}</if>
            <if test="claimTime != null "> and claim_time = #{claimTime}</if>
            <if test="claimIp != null  and claimIp != ''"> and claim_ip = #{claimIp}</if>
            <if test="claimExpireTime != null "> and claim_expire_time = #{claimExpireTime}</if>
            <if test="useTime != null "> and use_time = #{useTime}</if>
            <if test="useIp != null  and useIp != ''"> and use_ip = #{useIp}</if>
            <if test="useExpireTime != null "> and use_expire_time = #{useExpireTime}</if>
            <if test="useExt != null  and useExt != ''"> and use_ext = #{useExt}</if>
            <if test="agentIds != null and agentIds.size()>0">
                and agent_id in
                <foreach collection="agentIds" open="(" close=")" item="agentId" separator=",">
                    #{agentId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAgentCouponUserRecordById" parameterType="Long" resultMap="AgentCouponUserRecordResult">
            <include refid="selectAgentCouponUserRecordVo"/>
            where id = #{id}
    </select>

    <select id="selectByCouponNo" resultMap="AgentCouponUserRecordResult">
        <include refid="selectAgentCouponUserRecordVo"/>
        where coupon_no = #{couponNo}
    </select>

    <select id="isExistByAgentIdAndRequestId" resultType="Integer">
        select 1
        from tb_agent_coupon_user_record
        where agent_id = #{agentId} and request_id = #{requestId}
        limit 1
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.coupon.AgentCouponUserRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_agent_coupon_user_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="requestId != null and requestId != ''">request_id,</if>
                    <if test="couponNo != null and couponNo != ''">coupon_no,</if>
                    <if test="phone != null and phone != ''">phone,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="nickname != null">nickname,</if>
                    <if test="couponCode != null and couponCode != ''">coupon_code,</if>
                    <if test="couponType != null">coupon_type,</if>
                    <if test="couponStatus != null">coupon_status,</if>
                    <if test="deliverTime != null">deliver_time,</if>
                    <if test="claimTime != null">claim_time,</if>
                    <if test="claimIp != null">claim_ip,</if>
                    <if test="claimExpireTime != null">claim_expire_time,</if>
                    <if test="useTime != null">use_time,</if>
                    <if test="useIp != null">use_ip,</if>
                    <if test="useExpireTime != null">use_expire_time,</if>
                    <if test="useExt != null and useExt != ''">use_ext,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="requestId != null and requestId != ''">#{requestId},</if>
                    <if test="couponNo != null and couponNo != ''">#{couponNo},</if>
                    <if test="phone != null and phone != ''">#{phone},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="nickname != null">#{nickname},</if>
                    <if test="couponCode != null and couponCode != ''">#{couponCode},</if>
                    <if test="couponType != null">#{couponType},</if>
                    <if test="couponStatus != null">#{couponStatus},</if>
                    <if test="deliverTime != null">#{deliverTime},</if>
                    <if test="claimTime != null">#{claimTime},</if>
                    <if test="claimIp != null">#{claimIp},</if>
                    <if test="claimExpireTime != null">#{claimExpireTime},</if>
                    <if test="useTime != null">#{useTime},</if>
                    <if test="useIp != null">#{useIp},</if>
                    <if test="useExpireTime != null">#{useExpireTime},</if>
                    <if test="useExt != null and useExt != ''">#{useExt},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.coupon.AgentCouponUserRecordEntity">
        update tb_agent_coupon_user_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="couponNo != null and couponNo != ''">coupon_no = #{couponNo},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="couponCode != null and couponCode != ''">coupon_code = #{couponCode},</if>
            <if test="couponType != null">coupon_type = #{couponType},</if>
            <if test="couponStatus != null">coupon_status = #{couponStatus},</if>
            <if test="deliverTime != null">deliver_time = #{deliverTime},</if>
            <if test="claimTime != null">claim_time = #{claimTime},</if>
            <if test="claimIp != null">claim_ip = #{claimIp},</if>
            <if test="claimExpireTime != null">claim_expire_time = #{claimExpireTime},</if>
            <if test="useTime != null">use_time = #{useTime},</if>
            <if test="useIp != null">use_ip = #{useIp},</if>
            <if test="useExpireTime != null">use_expire_time = #{useExpireTime},</if>
            <if test="useExt != null and useExt != ''">use_ext = #{useExt},</if>
        </trim>
        <where>
            id = #{id}
            <if test="beforeStatus != null">
                and coupon_status = #{beforeStatus}
            </if>
        </where>
    </update>
</mapper>
