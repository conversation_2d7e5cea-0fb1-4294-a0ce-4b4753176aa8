<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.bingwu.AgtDataSupplierSnapshotBingwuMapper">

    <resultMap type="com.ruoyi.system.entity.bingwu.AgtDataSupplierSnapshotBingwuEntity" id="AgtDataSupplierSnapshotBingwuResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="curHour"    column="cur_hour"    />
            <result property="appId"    column="app_id"    />
            <result property="appName"    column="app_name"    />
            <result property="subAppId"    column="sub_app_id"    />
            <result property="subAppName"    column="sub_app_name"    />
            <result property="openId"    column="open_id"    />
            <result property="userCode"    column="user_code"    />
            <result property="promotionId"    column="promotion_id"    />
            <result property="promotionName"    column="promotion_name"    />
            <result property="promotionTime"    column="promotion_time"    />
            <result property="registerTime"    column="register_time"    />
            <result property="orderNo"    column="order_no"    />
            <result property="payAppType"    column="payAppType"    />
            <result property="description"    column="description"    />
            <result property="payStatus"    column="pay_status"    />
            <result property="state"    column="state"    />
            <result property="amount"    column="amount"    />
            <result property="sourceId"    column="source_id"    />
            <result property="source"    column="source"    />
            <result property="isVip"    column="is_vip"    />
            <result property="dramaId"    column="drama_id"    />
            <result property="epNo"    column="ep_no"    />
            <result property="successTime"    column="success_time"    />
            <result property="createTime"    column="create_time"    />
            <result property="dramaName"    column="drama_name"    />
            <result property="system"    column="system"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gtmModified"    column="gtm_modified"    />
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotBingwuVo">
        select id, advertiser_id, cur_date, cur_hour, app_id, app_name, sub_app_id, sub_app_name, open_id, user_code, promotion_id, promotion_name, promotion_time, register_time, order_no, payAppType, description, pay_status, state, amount, source_id, source, is_vip, drama_id, ep_no, success_time, create_time, drama_name, `system`, gmt_create, gtm_modified from tb_agt_data_supplier_snapshot_bingwu
    </sql>

    <select id="selectAgtDataSupplierSnapshotBingwuList" parameterType="com.ruoyi.system.entity.bingwu.AgtDataSupplierSnapshotBingwuEntity" resultMap="AgtDataSupplierSnapshotBingwuResult">
        <include refid="selectAgtDataSupplierSnapshotBingwuVo"/>
        <where>
                        <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="curHour != null "> and cur_hour = #{curHour}</if>
                        <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
                        <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
                        <if test="subAppId != null  and subAppId != ''"> and sub_app_id = #{subAppId}</if>
                        <if test="subAppName != null  and subAppName != ''"> and sub_app_name like concat('%', #{subAppName}, '%')</if>
                        <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
                        <if test="userCode != null  and userCode != ''"> and user_code = #{userCode}</if>
                        <if test="promotionId != null  and promotionId != ''"> and promotion_id = #{promotionId}</if>
                        <if test="promotionName != null  and promotionName != ''"> and promotion_name like concat('%', #{promotionName}, '%')</if>
                        <if test="promotionTime != null "> and promotion_time = #{promotionTime}</if>
                        <if test="registerTime != null "> and register_time = #{registerTime}</if>
                        <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                        <if test="payAppType != null "> and payAppType = #{payAppType}</if>
                        <if test="description != null  and description != ''"> and description = #{description}</if>
                        <if test="payStatus != null "> and pay_status = #{payStatus}</if>
                        <if test="state != null "> and state = #{state}</if>
                        <if test="amount != null "> and amount = #{amount}</if>
                        <if test="sourceId != null "> and source_id = #{sourceId}</if>
                        <if test="source != null  and source != ''"> and source = #{source}</if>
                        <if test="isVip != null "> and is_vip = #{isVip}</if>
                        <if test="dramaId != null "> and drama_id = #{dramaId}</if>
                        <if test="epNo != null "> and ep_no = #{epNo}</if>
                        <if test="successTime != null "> and success_time = #{successTime}</if>
                        <if test="dramaName != null  and dramaName != ''"> and drama_name like concat('%', #{dramaName}, '%')</if>
                        <if test="system != null  and system != ''"> and `system` = #{system}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gtmModified != null "> and gtm_modified = #{gtmModified}</if>
        </where>
    </select>

    <select id="selectAgtDataSupplierSnapshotBingwuById" parameterType="Long" resultMap="AgtDataSupplierSnapshotBingwuResult">
            <include refid="selectAgtDataSupplierSnapshotBingwuVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataSupplierSnapshotBingwu" parameterType="com.ruoyi.system.entity.bingwu.AgtDataSupplierSnapshotBingwuEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_data_supplier_snapshot_bingwu
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="curHour != null">cur_hour,</if>
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="appName != null">app_name,</if>
                    <if test="subAppId != null">sub_app_id,</if>
                    <if test="subAppName != null">sub_app_name,</if>
                    <if test="openId != null">open_id,</if>
                    <if test="userCode != null">user_code,</if>
                    <if test="promotionId != null">promotion_id,</if>
                    <if test="promotionName != null">promotion_name,</if>
                    <if test="promotionTime != null">promotion_time,</if>
                    <if test="registerTime != null">register_time,</if>
                    <if test="orderNo != null">order_no,</if>
                    <if test="payAppType != null">payAppType,</if>
                    <if test="description != null">description,</if>
                    <if test="payStatus != null">pay_status,</if>
                    <if test="state != null">state,</if>
                    <if test="amount != null">amount,</if>
                    <if test="sourceId != null">source_id,</if>
                    <if test="source != null">source,</if>
                    <if test="isVip != null">is_vip,</if>
                    <if test="dramaId != null">drama_id,</if>
                    <if test="epNo != null">ep_no,</if>
                    <if test="successTime != null">success_time,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="dramaName != null">drama_name,</if>
                    <if test="system != null">`system`,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gtmModified != null">gtm_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null">#{advertiserId},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="curHour != null">#{curHour},</if>
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="appName != null">#{appName},</if>
                    <if test="subAppId != null">#{subAppId},</if>
                    <if test="subAppName != null">#{subAppName},</if>
                    <if test="openId != null">#{openId},</if>
                    <if test="userCode != null">#{userCode},</if>
                    <if test="promotionId != null">#{promotionId},</if>
                    <if test="promotionName != null">#{promotionName},</if>
                    <if test="promotionTime != null">#{promotionTime},</if>
                    <if test="registerTime != null">#{registerTime},</if>
                    <if test="orderNo != null">#{orderNo},</if>
                    <if test="payAppType != null">#{payAppType},</if>
                    <if test="description != null">#{description},</if>
                    <if test="payStatus != null">#{payStatus},</if>
                    <if test="state != null">#{state},</if>
                    <if test="amount != null">#{amount},</if>
                    <if test="sourceId != null">#{sourceId},</if>
                    <if test="source != null">#{source},</if>
                    <if test="isVip != null">#{isVip},</if>
                    <if test="dramaId != null">#{dramaId},</if>
                    <if test="epNo != null">#{epNo},</if>
                    <if test="successTime != null">#{successTime},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="dramaName != null">#{dramaName},</if>
                    <if test="system != null">#{system},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gtmModified != null">#{gtmModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataSupplierSnapshotBingwu" parameterType="com.ruoyi.system.entity.bingwu.AgtDataSupplierSnapshotBingwuEntity">
        update tb_agt_data_supplier_snapshot_bingwu
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="curHour != null">cur_hour = #{curHour},</if>
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="appName != null">app_name = #{appName},</if>
                    <if test="subAppId != null">sub_app_id = #{subAppId},</if>
                    <if test="subAppName != null">sub_app_name = #{subAppName},</if>
                    <if test="openId != null">open_id = #{openId},</if>
                    <if test="userCode != null">user_code = #{userCode},</if>
                    <if test="promotionId != null">promotion_id = #{promotionId},</if>
                    <if test="promotionName != null">promotion_name = #{promotionName},</if>
                    <if test="promotionTime != null">promotion_time = #{promotionTime},</if>
                    <if test="registerTime != null">register_time = #{registerTime},</if>
                    <if test="orderNo != null">order_no = #{orderNo},</if>
                    <if test="payAppType != null">payAppType = #{payAppType},</if>
                    <if test="description != null">description = #{description},</if>
                    <if test="payStatus != null">pay_status = #{payStatus},</if>
                    <if test="state != null">state = #{state},</if>
                    <if test="amount != null">amount = #{amount},</if>
                    <if test="sourceId != null">source_id = #{sourceId},</if>
                    <if test="source != null">source = #{source},</if>
                    <if test="isVip != null">is_vip = #{isVip},</if>
                    <if test="dramaId != null">drama_id = #{dramaId},</if>
                    <if test="epNo != null">ep_no = #{epNo},</if>
                    <if test="successTime != null">success_time = #{successTime},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="dramaName != null">drama_name = #{dramaName},</if>
                    <if test="system != null">`system` = #{system},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gtmModified != null">gtm_modified = #{gtmModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataSupplierSnapshotBingwuById" parameterType="Long">
        delete from tb_agt_data_supplier_snapshot_bingwu where id = #{id}
    </delete>

    <delete id="deleteAgtDataSupplierSnapshotBingwuByIds" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_bingwu where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>