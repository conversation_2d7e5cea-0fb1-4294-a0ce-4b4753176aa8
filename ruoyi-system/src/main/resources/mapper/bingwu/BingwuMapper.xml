<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.bingwu.BingwuMapper">

    <resultMap type="com.ruoyi.system.entity.bingwu.AgtDataSupplierSnapshotBingwuEntity"
               id="AgtDataSupplierSnapshotBingwuResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="appId" column="app_id"/>
        <result property="appName" column="app_name"/>
        <result property="subAppId" column="sub_app_id"/>
        <result property="subAppName" column="sub_app_name"/>
        <result property="openId" column="open_id"/>
        <result property="userCode" column="user_code"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="promotionName" column="promotion_name"/>
        <result property="promotionTime" column="promotion_time"/>
        <result property="registerTime" column="register_time"/>
        <result property="orderNo" column="order_no"/>
        <result property="payAppType" column="payAppType"/>
        <result property="description" column="description"/>
        <result property="payStatus" column="pay_status"/>
        <result property="state" column="state"/>
        <result property="amount" column="amount"/>
        <result property="sourceId" column="source_id"/>
        <result property="source" column="source"/>
        <result property="isVip" column="is_vip"/>
        <result property="dramaId" column="drama_id"/>
        <result property="epNo" column="ep_no"/>
        <result property="successTime" column="success_time"/>
        <result property="createTime" column="create_time"/>
        <result property="dramaName" column="drama_name"/>
        <result property="system" column="system"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gtmModified" column="gtm_modified"/>
    </resultMap>

    <insert id="saveOrUpdateBatch">
        insert into
        tb_agt_data_supplier_snapshot_bingwu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].appId != null">app_id,</if>
            <if test="list[0].appName != null">app_name,</if>
            <if test="list[0].subAppId != null">sub_app_id,</if>
            <if test="list[0].subAppName != null">sub_app_name,</if>
            <if test="list[0].openId != null">open_id,</if>
            <if test="list[0].userCode != null">user_code,</if>
            <if test="list[0].promotionId != null">promotion_id,</if>
            <if test="list[0].promotionName != null">promotion_name,</if>
            <if test="list[0].promotionTime != null">promotion_time,</if>
            <if test="list[0].registerTime != null">register_time,</if>
            <if test="list[0].orderNo != null">order_no,</if>
            <if test="list[0].payAppType != null">payAppType,</if>
            <if test="list[0].description != null">description,</if>
            <if test="list[0].payStatus != null">pay_status,</if>
            <if test="list[0].state != null">state,</if>
            <if test="list[0].amount != null">amount,</if>
            <if test="list[0].sourceId != null">source_id,</if>
            <if test="list[0].source != null">source,</if>
            <if test="list[0].isVip != null">is_vip,</if>
            <if test="list[0].dramaId != null">drama_id,</if>
            <if test="list[0].epNo != null">ep_no,</if>
            <if test="list[0].successTime != null">success_time,</if>
            <if test="list[0].createTime != null">create_time,</if>
            <if test="list[0].dramaName != null">drama_name,</if>
            <if test="list[0].system != null">`system`,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.curDate != null">#{entity.curDate},</if>
                <if test="entity.curHour != null">#{entity.curHour},</if>
                <if test="entity.appId != null">#{entity.appId},</if>
                <if test="entity.appName != null">#{entity.appName},</if>
                <if test="entity.subAppId != null">#{entity.subAppId},</if>
                <if test="entity.subAppName != null">#{entity.subAppName},</if>
                <if test="entity.openId != null">#{entity.openId},</if>
                <if test="entity.userCode != null">#{entity.userCode},</if>
                <if test="entity.promotionId != null">#{entity.promotionId},</if>
                <if test="entity.promotionName != null">#{entity.promotionName},</if>
                <if test="entity.promotionTime != null">#{entity.promotionTime},</if>
                <if test="entity.registerTime != null">#{entity.registerTime},</if>
                <if test="entity.orderNo != null">#{entity.orderNo},</if>
                <if test="entity.payAppType != null">#{entity.payAppType},</if>
                <if test="entity.description != null">#{entity.description},</if>
                <if test="entity.payStatus != null">#{entity.payStatus},</if>
                <if test="entity.state != null">#{entity.state},</if>
                <if test="entity.amount != null">#{entity.amount},</if>
                <if test="entity.sourceId != null">#{entity.sourceId},</if>
                <if test="entity.source != null">#{entity.source},</if>
                <if test="entity.isVip != null">#{entity.isVip},</if>
                <if test="entity.dramaId != null">#{entity.dramaId},</if>
                <if test="entity.epNo != null">#{entity.epNo},</if>
                <if test="entity.successTime != null">#{entity.successTime},</if>
                <if test="entity.createTime != null">#{entity.createTime},</if>
                <if test="entity.dramaName != null">#{entity.dramaName},</if>
                <if test="entity.system != null">#{entity.system},</if>
            </trim>
        </foreach>
        on duplicate key update
        advertiser_id = VALUES(advertiser_id),
        cur_date = VALUES(cur_date),
        cur_hour = VALUES(cur_hour)
    </insert>

    <select id="selectPayCount" resultType="java.lang.Integer">
        SELECT
        (SELECT COUNT(DISTINCT user_code)
        FROM tb_agt_data_supplier_snapshot_bingwu
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq})
        -
        (SELECT COUNT(DISTINCT user_code)
        FROM tb_agt_data_supplier_snapshot_bingwu
        WHERE cur_date = #{dateReq} and advertiser_id=#{adId}
        and cur_hour &lt;= #{hourReq} - 1) AS pay_count
    </select>
    <select id="selectOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_agt_data_supplier_snapshot_bingwu WHERE advertiser_id=#{adId} and cur_date=#{dateReq} and cur_hour=#{hourReq}
    </select>

    <select id="selectIncomeByDay"
            resultType="com.ruoyi.system.entity.bingwu.AgtDataSupplierSnapshotBingwuEntity">
        SELECT
        sum( amount ) AS amount,
        cur_date
        FROM
        tb_agt_data_supplier_snapshot_bingwu
        WHERE
        cur_date BETWEEN #{start} and #{end}
        GROUP BY cur_date
    </select>


</mapper>