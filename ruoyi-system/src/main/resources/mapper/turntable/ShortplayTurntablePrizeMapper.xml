<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.turntable.ShortplayTurntablePrizeMapper">

    <resultMap type="com.ruoyi.system.entity.turntable.ShortplayTurntablePrizeEntity" id="ShortplayTurntablePrizeResult">
            <result property="id"    column="id"    />
            <result property="name"    column="name"    />
            <result property="type"    column="type"    />
            <result property="num"    column="num"    />
            <result property="probability"    column="probability"    />
            <result property="img"    column="img"    />
            <result property="sort"    column="sort"    />
            <result property="appId"    column="app_id"    />
            <result property="path"    column="path"    />
            <result property="isDeleted"    column="is_deleted"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayTurntablePrizeVo">
        select id, name, type, num, probability, img, sort, app_id, path, is_deleted, gmt_create, gmt_modified from tb_shortplay_turntable_prize
    </sql>

    <select id="selectShortplayTurntablePrizeList" parameterType="com.ruoyi.system.entity.turntable.ShortplayTurntablePrizeEntity" resultMap="ShortplayTurntablePrizeResult">
        <include refid="selectShortplayTurntablePrizeVo"/>
        <where>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayTurntablePrizeById" parameterType="Long" resultMap="ShortplayTurntablePrizeResult">
            <include refid="selectShortplayTurntablePrizeVo"/>
            where id = #{id}
    </select>
    <select id="selectTurntableList"
            resultMap="ShortplayTurntablePrizeResult">
        <include refid="selectShortplayTurntablePrizeVo"/>
        where is_deleted = 0 order by sort asc
    </select>

    <insert id="insertShortplayTurntablePrize" parameterType="com.ruoyi.system.entity.turntable.ShortplayTurntablePrizeEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_turntable_prize
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="name != null and name != ''">name,</if>
                    <if test="type != null">type,</if>
                    <if test="num != null">num,</if>
                    <if test="probability != null">probability,</if>
                    <if test="img != null and img != ''">img,</if>
                    <if test="sort != null">sort,</if>
                    <if test="appId != null">app_id,</if>
                    <if test="path != null">path,</if>
                    <if test="isDeleted != null">is_deleted,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="name != null and name != ''">#{name},</if>
                    <if test="type != null">#{type},</if>
                    <if test="num != null">#{num},</if>
                    <if test="probability != null">#{probability},</if>
                    <if test="img != null and img != ''">#{img},</if>
                    <if test="sort != null">#{sort},</if>
                    <if test="appId != null">#{appId},</if>
                    <if test="path != null">#{path},</if>
                    <if test="isDeleted != null">#{isDeleted},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateShortplayTurntablePrize" parameterType="com.ruoyi.system.entity.turntable.ShortplayTurntablePrizeEntity">
        update tb_shortplay_turntable_prize
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null and name != ''">name = #{name},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="num != null">num = #{num},</if>
                    <if test="probability != null">probability = #{probability},</if>
                    <if test="img != null and img != ''">img = #{img},</if>
                    <if test="sort != null">sort = #{sort},</if>
                    <if test="appId != null">app_id = #{appId},</if>
                    <if test="path != null">path = #{path},</if>
                    <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayTurntablePrizeById" parameterType="Long">
        delete from tb_shortplay_turntable_prize where id = #{id}
    </delete>

    <delete id="deleteShortplayTurntablePrizeByIds" parameterType="String">
        delete from tb_shortplay_turntable_prize where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>