<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.turntable.ShortplayTurntableUserRecordMapper">

    <resultMap type="com.ruoyi.system.entity.turntable.ShortplayTurntableUserRecordEntity" id="ShortplayTurntableUserRecordResult">
            <result property="id"    column="id"    />
            <result property="userId"    column="user_id"    />
            <result property="prizeId"    column="prize_id"    />
            <result property="name"    column="name"    />
            <result property="type"    column="type"    />
            <result property="num"    column="num"    />
            <result property="appId"    column="app_id"    />
            <result property="status"    column="status"    />
            <result property="path"    column="path"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayTurntableUserRecordVo">
        select id, user_id, prize_id, name, type, num,status, app_id, path, gmt_create, gmt_modified from tb_shortplay_turntable_user_record
    </sql>

    <select id="selectShortplayTurntableUserRecordList" parameterType="com.ruoyi.system.entity.turntable.ShortplayTurntableUserRecordEntity" resultMap="ShortplayTurntableUserRecordResult">
        <include refid="selectShortplayTurntableUserRecordVo"/>
        <where>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayTurntableUserRecordById" parameterType="Long" resultMap="ShortplayTurntableUserRecordResult">
            <include refid="selectShortplayTurntableUserRecordVo"/>
            where id = #{id}
    </select>
    <select id="selectListByUserId"
            resultMap="ShortplayTurntableUserRecordResult">
        <include refid="selectShortplayTurntableUserRecordVo"/>
        where user_id = #{userId} and status = 1
        order by id desc
    </select>

    <insert id="insertShortplayTurntableUserRecord" parameterType="com.ruoyi.system.entity.turntable.ShortplayTurntableUserRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_turntable_user_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,</if>
                    <if test="prizeId != null">prize_id,</if>
                    <if test="name != null">name,</if>
                    <if test="type != null">type,</if>
                    <if test="num != null">num,</if>
                    <if test="appId != null">app_id,</if>
                    <if test="path != null">path,</if>
                    <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},</if>
                    <if test="prizeId != null">#{prizeId},</if>
                    <if test="name != null">#{name},</if>
                    <if test="type != null">#{type},</if>
                    <if test="num != null">#{num},</if>
                    <if test="appId != null">#{appId},</if>
                    <if test="path != null">#{path},</if>
                    <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateShortplayTurntableUserRecord" parameterType="com.ruoyi.system.entity.turntable.ShortplayTurntableUserRecordEntity">
        update tb_shortplay_turntable_user_record
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="prizeId != null">prize_id = #{prizeId},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="num != null">num = #{num},</if>
                    <if test="appId != null">app_id = #{appId},</if>
                    <if test="path != null">path = #{path},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayTurntableUserRecordById" parameterType="Long">
        delete from tb_shortplay_turntable_user_record where id = #{id}
    </delete>

    <delete id="deleteShortplayTurntableUserRecordByIds" parameterType="String">
        delete from tb_shortplay_turntable_user_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>