<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.juliang.AgtAccountJuliangMapper">

    <resultMap type="com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity" id="AgtAccountJuliangResult">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="accountNameCo" column="account_name_co"/>
        <result property="accountAgent" column="account_agent"/>
        <result property="commission" column="commission"/>
        <result property="advertiserCount" column="advertiser_count"/>
        <result property="accessToken" column="access_token"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="expiresIn" column="expires_in"/>
        <result property="expiresInRefresh" column="expires_in_refresh"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectAgtAccountJuliangVo">
        select id, account_id, account_name, account_name_co, account_agent, commission, advertiser_count, access_token, refresh_token, expires_in,
        expires_in_refresh, gmt_create, gmt_modified from tb_agt_account_juliang
    </sql>

    <select id="selectAgtAccountJuliangList" parameterType="com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity"
            resultMap="AgtAccountJuliangResult">
        <include refid="selectAgtAccountJuliangVo"/>
        <where>
            <if test="accountId != null ">and account_id = #{accountId}</if>
            <if test="accountName != null  and accountName != ''">and account_name like concat('%', #{accountName},
                '%')
            </if>
            <if test="accountNameCo != null  and accountNameCo != ''">and account_name_co = #{accountNameCo}</if>
            <if test="accountAgent != null  and accountAgent != ''">and account_agent = #{accountAgent}</if>
            <if test="commission != null  and commission != ''">and commission = #{commission}</if>
            <if test="advertiserCount != null  and advertiserCount != ''">and advertiser_count = #{advertiserCount}</if>
            <if test="accessToken != null  and accessToken != ''">and access_token = #{accessToken}</if>
            <if test="refreshToken != null  and refreshToken != ''">and refresh_token = #{refreshToken}</if>
            <if test="expiresIn != null ">and expires_in &lt;= #{expiresIn}</if>
            <if test="expiresInRefresh != null ">and expires_in_refresh &lt;= #{expiresInRefresh}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtAccountJuliangById" parameterType="String" resultMap="AgtAccountJuliangResult">
        <include refid="selectAgtAccountJuliangVo"/>
        where id = #{id}
    </select>

    <select id="selectByAccountId" resultType="com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity">
        <include refid="selectAgtAccountJuliangVo"/>
        where account_id = #{accountId}
    </select>

    <insert id="insertAgtAccountJuliang" parameterType="com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_account_juliang
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="accountNameCo != null">account_name_co,</if>
            <if test="accountAgent != null and accountAgent != ''">account_agent,</if>
            <if test="commission != null and commission != ''">commission,</if>
            <if test="advertiserCount != null and advertiserCount != ''">advertiser_count,</if>
            <if test="accessToken != null and accessToken != ''">access_token,</if>
            <if test="refreshToken != null and refreshToken != ''">refresh_token,</if>
            <if test="expiresIn != null">expires_in,</if>
            <if test="expiresInRefresh != null">expires_in_refresh,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="accountNameCo != null">#{accountNameCo},</if>
            <if test="accountAgent != null and accountAgent != ''">#{accountAgent},</if>
            <if test="commission != null and commission != ''">#{commission},</if>
            <if test="advertiserCount != null and advertiserCount != ''">#{advertiserCount},</if>
            <if test="accessToken != null and accessToken != ''">#{accessToken},</if>
            <if test="refreshToken != null and refreshToken != ''">#{refreshToken},</if>
            <if test="expiresIn != null">#{expiresIn},</if>
            <if test="expiresInRefresh != null">#{expiresInRefresh},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtAccountJuliang" parameterType="com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity">
        update tb_agt_account_juliang
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="accountNameCo != null">account_name_co = #{accountNameCo},</if>
            <if test="accountAgent != null and accountAgent != ''">account_agent = #{accountAgent},</if>
            <if test="commission != null and commission != ''">commission = #{commission},</if>
            <if test="advertiserCount != null and advertiserCount != ''">advertiser_count = #{advertiserCount},</if>
            <if test="accessToken != null and accessToken != ''">access_token = #{accessToken},</if>
            <if test="refreshToken != null and refreshToken != ''">refresh_token = #{refreshToken},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="expiresInRefresh != null">expires_in_refresh = #{expiresInRefresh},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAdvertiserCount">
        UPDATE tb_agt_account_juliang
        SET advertiser_count = ( SELECT count(*) FROM tb_agt_account_juliang_advertiser WHERE account_id = #{accountId}
        )
        WHERE account_id = #{accountId}
    </update>

    <delete id="deleteAgtAccountJuliangById" parameterType="String">
        delete from tb_agt_account_juliang where id = #{id}
    </delete>

    <delete id="deleteAgtAccountJuliangByIds" parameterType="String">
        delete from tb_agt_account_juliang where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
