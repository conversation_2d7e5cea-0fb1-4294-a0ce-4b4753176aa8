<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.data.ShortplayDyMiniappDataMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.data.ShortplayDyMiniappDataEntity" id="ShortplayDyMiniappDataResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="miniappId"    column="miniapp_id"    />
            <result property="miniappName"    column="miniapp_name"    />
            <result property="income"    column="income"    />
            <result property="incentiveFundCost"    column="incentive_fund_cost"    />
            <result property="activeUserNum"    column="active_user_num"    />
            <result property="avgStayTime"    column="avg_stay_time"    />
            <result property="newUserNum"    column="new_user_num"    />
            <result property="openTime"    column="open_time"    />
            <result property="perUserOpenTime"    column="per_user_open_time"    />
            <result property="perUserStayTime"    column="per_user_stay_time"    />
            <result property="shareTime"    column="share_time"    />
            <result property="totalUserNum"    column="total_user_num"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.bo.shortplay.ShortplayDyMiniappDataBo" id="ShortplayDyMiniappDataBoResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="miniappId"    column="miniapp_id"    />
        <result property="miniappName"    column="miniapp_name"    />
        <result property="accountAgent"    column="account_agent"    />
        <result property="income"    column="income"    />
        <result property="cashIncome"    column="cash_income"    />
        <result property="realCashIncome"    column="real_cash_income"    />
        <result property="cost"    column="cost"    />
        <result property="cashCost"    column="cash_cost"    />
        <result property="roi"    column="roi"    />
        <result property="divideRate"    column="divide_rate"    />
        <result property="profit"    column="profit"    />
        <result property="adMonetizationAmount" column="adMonetization_amount" />
        <result property="checkData" column="check_data" />
        <result property="incentiveFundCost"    column="incentive_fund_cost"    />
        <result property="predictIncentiveFundCost"    column="predict_incentive_fund_cost"    />
        <result property="activeUserNum"    column="active_user_num"    />
        <result property="avgStayTime"    column="avg_stay_time"    />
        <result property="newUserNum"    column="new_user_num"    />
        <result property="openTime"    column="open_time"    />
        <result property="perUserOpenTime"    column="per_user_open_time"    />
        <result property="perUserStayTime"    column="per_user_stay_time"    />
        <result property="shareTime"    column="share_time"    />
        <result property="totalUserNum"    column="total_user_num"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
        <result property="userNickname"    column="user_nickname"    />
    </resultMap>

    <sql id="selectShortplayDyMiniappDataVo">
        select id, cur_date, miniapp_id, miniapp_name, income, incentive_fund_cost, active_user_num, avg_stay_time, new_user_num, open_time, per_user_open_time, per_user_stay_time, share_time, total_user_num, gmt_create, gmt_modified from tb_shortplay_dy_miniapp_data
    </sql>

    <select id="selectBy" resultMap="ShortplayDyMiniappDataResult">
        <include refid="selectShortplayDyMiniappDataVo"/>
        where cur_date = #{curDate} and miniapp_id = #{miniappId}
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.req.juliang.AgtDataMiniappReq" resultMap="ShortplayDyMiniappDataBoResult">
        select
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                ${column}
            </foreach>
        </if>
        sum(ifnull(o.stat_cost, 0)) as cost,
        sum(ifnull(o.cash_cost, 0)) as cash_cost,
        sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0)) as adMonetization_amount,
        sum(d.income) as income,
        sum(d.income) / 100 / sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0)) as check_data,
        sum(d.incentive_fund_cost) as incentive_fund_cost,
        sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0) / 0.7 * 0.3 * 100) as predict_incentive_fund_cost,
        sum(d.income + d.incentive_fund_cost) as cash_income,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) as real_cash_income,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) / (sum(ifnull(o.cash_cost, 0)) * 100) as roi,
        sum(ifnull(o.cash_cost, 0)) * 100 / sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) as divide_rate,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) - sum(ifnull(o.cash_cost, 0)) * 100 as profit,
        sum(d.active_user_num) as active_user_num,
        sum(d.avg_stay_time * d.open_time) / sum(d.open_time) as avg_stay_time,
        sum(d.new_user_num) as new_user_num,
        sum(d.open_time) as open_time,
        sum(d.open_time) / sum(d.active_user_num) as per_user_open_time,
        sum(d.per_user_stay_time * d.active_user_num) / sum(d.active_user_num)  as per_user_stay_time,
        sum(d.share_time) as share_time,
        sum(d.total_user_num) as total_user_num
        from tb_shortplay_dy_miniapp_data d
        left join `tb_shortplay_app` a on d.`miniapp_id` = a.`appid`
        left join (
        select cur_date, app_id, sum(stat_cost) as stat_cost, sum(cash_cost) as cash_cost, sum(stat_attribution_micro_game_24h_amount) as stat_attribution_micro_game_24h_amount
        from tb_ocean_engine_project_date_data
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and app_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        group by cur_date, app_id
        ) o on d.cur_date = o.cur_date and d.miniapp_id = o.app_id
        left join (
        select cur_date, app_id, account_agent
        from (
        select cur_date, app_id, account_agent, ROW_NUMBER() OVER (PARTITION BY cur_date, app_id ORDER BY SUM(stat_cost) DESC) AS rn
        from tb_ocean_engine_project_date_data
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and app_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        GROUP BY cur_date, app_id, account_agent
        ) rnt
        where rn = 1
        ) ag on d.cur_date = ag.cur_date and d.miniapp_id = ag.app_id
        <where>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="accountAgent != null">and ag.account_agent = #{accountAgent}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and d.miniapp_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <choose>
            <when test="dimension != null and dimension.size() > 0 and dimension.contains('d.cur_date')">
                order by d.cur_date desc, ${orderColumn} ${orderType}
            </when>
            <otherwise>
                order by ${orderColumn} ${orderType}
            </otherwise>
        </choose>
    </select>


    <select id="selectListByUserName" resultType="com.ruoyi.system.bo.shortplay.ShortplayDyMiniappDataBo">
        select
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                ${column}
            </foreach>
        </if>
        sum(ifnull(o.stat_cost, 0)) as cost,
        sum(ifnull(o.cash_cost, 0)) as cash_cost,
        sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0)) as adMonetization_amount,
        sum(d.income) as income,
        sum(d.income) / 100 / sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0)) as check_data,
        sum(d.incentive_fund_cost) as incentive_fund_cost,
        sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0) / 0.7 * 0.3 * 100) as predict_incentive_fund_cost,
        sum(d.income + d.incentive_fund_cost) as cash_income,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) as real_cash_income,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) / (sum(ifnull(o.cash_cost, 0)) * 100) as roi,
        sum(ifnull(o.cash_cost, 0)) * 100 / sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) as divide_rate,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) - sum(ifnull(o.cash_cost, 0)) * 100 as profit,
        sum(d.active_user_num) as active_user_num,
        sum(d.avg_stay_time * d.open_time) / sum(d.open_time) as avg_stay_time,
        sum(d.new_user_num) as new_user_num,
        sum(d.open_time) as open_time,
        sum(d.open_time) / sum(d.active_user_num) as per_user_open_time,
        sum(d.per_user_stay_time * d.active_user_num) / sum(d.active_user_num)  as per_user_stay_time,
        sum(d.share_time) as share_time,
        sum(d.total_user_num) as total_user_num
        from tb_shortplay_dy_miniapp_data d
        left join `tb_shortplay_app` a on d.`miniapp_id` = a.`appid`
        left join (
        select cur_date, app_id, user_nickname, sum(stat_cost) as stat_cost, sum(cash_cost) as cash_cost, sum(stat_attribution_micro_game_24h_amount) as stat_attribution_micro_game_24h_amount
        from tb_ocean_engine_project_date_data
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and app_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        group by cur_date, app_id, user_nickname
        ) o on d.cur_date = o.cur_date and d.miniapp_id = o.app_id
        left join (
        select cur_date, app_id, account_agent
        from (
        select cur_date, app_id, account_agent, ROW_NUMBER() OVER (PARTITION BY cur_date, app_id ORDER BY SUM(stat_cost) DESC) AS rn
        from tb_ocean_engine_project_date_data
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and app_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        GROUP BY cur_date, app_id, account_agent
        ) rnt
        where rn = 1
        ) ag on d.cur_date = ag.cur_date and d.miniapp_id = ag.app_id
        <where>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="accountAgent != null">and ag.account_agent = #{accountAgent}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and d.miniapp_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <choose>
            <when test="dimension != null and dimension.size() > 0 and dimension.contains('d.cur_date')">
                order by d.cur_date desc, ${orderColumn} ${orderType}
            </when>
            <otherwise>
                order by ${orderColumn} ${orderType}
            </otherwise>
        </choose>
    </select>

    <select id="selectListByUserNameNoDate"
            resultType="com.ruoyi.system.bo.shortplay.ShortplayDyMiniappDataBo">
        select
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                ${column}
            </foreach>
        </if>
        sum(ifnull(o.stat_cost, 0)) as cost,
        sum(ifnull(o.cash_cost, 0)) as cash_cost,
        sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0)) as adMonetization_amount,
        sum(d.income) as income,
        sum(d.income) / 100 / sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0)) as check_data,
        sum(d.incentive_fund_cost) as incentive_fund_cost,
        sum(ifnull(o.stat_attribution_micro_game_24h_amount, 0) / 0.7 * 0.3 * 100) as predict_incentive_fund_cost,
        sum(d.income + d.incentive_fund_cost) as cash_income,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) as real_cash_income,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) / (sum(ifnull(o.cash_cost, 0)) * 100) as roi,
        sum(ifnull(o.cash_cost, 0)) * 100 / sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) as divide_rate,
        sum((d.income + d.incentive_fund_cost) * (CASE a.component_appid is not null WHEN true THEN 0.95 ELSE 1 END)) - sum(ifnull(o.cash_cost, 0)) * 100 as profit,
        sum(d.active_user_num) as active_user_num,
        sum(d.avg_stay_time * d.open_time) / sum(d.open_time) as avg_stay_time,
        sum(d.new_user_num) as new_user_num,
        sum(d.open_time) as open_time,
        sum(d.open_time) / sum(d.active_user_num) as per_user_open_time,
        sum(d.per_user_stay_time * d.active_user_num) / sum(d.active_user_num)  as per_user_stay_time,
        sum(d.share_time) as share_time,
        sum(d.total_user_num) as total_user_num
        from tb_shortplay_dy_miniapp_data d
        left join `tb_shortplay_app` a on d.`miniapp_id` = a.`appid`
        left join (
        select cur_date, app_id, user_nickname, sum(stat_cost) as stat_cost, sum(cash_cost) as cash_cost, sum(stat_attribution_micro_game_24h_amount) as stat_attribution_micro_game_24h_amount
        from tb_ocean_engine_project_date_data
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and app_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        group by cur_date, app_id, user_nickname
        ) o on d.cur_date = o.cur_date and d.miniapp_id = o.app_id
        left join (
        select cur_date, app_id
        from (
        select cur_date, app_id, ROW_NUMBER() OVER (PARTITION BY cur_date, app_id ORDER BY SUM(stat_cost) DESC) AS rn
        from tb_ocean_engine_project_date_data
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and app_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        GROUP BY cur_date, app_id
        ) rnt
        where rn = 1
        ) ag on d.cur_date = ag.cur_date and d.miniapp_id = ag.app_id
        <where>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="accountAgent != null">and ag.account_agent = #{accountAgent}</if>
            <if test="miniappIds != null and miniappIds.size() > 0">
                and d.miniapp_id in
                <foreach collection="miniappIds" item="miniappId" separator="," open="(" close=")">
                    #{miniappId}
                </foreach>
            </if>
        </where>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <choose>
            <when test="dimension != null and dimension.size() > 0 and dimension.contains('d.cur_date')">
                order by d.cur_date desc, ${orderColumn} ${orderType}
            </when>
            <otherwise>
                order by ${orderColumn} ${orderType}
            </otherwise>
        </choose>
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayDyMiniappDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_shortplay_dy_miniapp_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="miniappId != null and miniappId != ''">miniapp_id,</if>
            <if test="miniappName != null and miniappName != ''">miniapp_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="miniappId != null and miniappId != ''">#{miniappId},</if>
            <if test="miniappName != null and miniappName != ''">#{miniappName},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayDyMiniappDataEntity">
        update tb_shortplay_dy_miniapp_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="miniappName != null and miniappName != ''">miniapp_name = #{miniappName},</if>
            <if test="income != null">income = #{income},</if>
            <if test="incentiveFundCost != null">incentive_fund_cost = #{incentiveFundCost},</if>
            <if test="activeUserNum != null">active_user_num = #{activeUserNum},</if>
            <if test="avgStayTime != null">avg_stay_time = #{avgStayTime},</if>
            <if test="newUserNum != null">new_user_num = #{newUserNum},</if>
            <if test="openTime != null">open_time = #{openTime},</if>
            <if test="perUserOpenTime != null">per_user_open_time = #{perUserOpenTime},</if>
            <if test="perUserStayTime != null">per_user_stay_time = #{perUserStayTime},</if>
            <if test="shareTime != null">share_time = #{shareTime},</if>
            <if test="totalUserNum != null">total_user_num = #{totalUserNum},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
