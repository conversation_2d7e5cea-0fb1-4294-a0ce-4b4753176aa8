<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.data.ShortplayAdDataMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity" id="ShortplayAdDataResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="miniappId"    column="miniapp_id"    />
            <result property="miniappName"    column="miniapp_name"    />
            <result property="tvId"    column="tv_id"    />
            <result property="tvName"    column="tv_name"    />
            <result property="series"    column="series"    />
            <result property="pv"    column="pv"    />
            <result property="uv"    column="uv"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayAdDataVo">
        select id, cur_date, miniapp_id, miniapp_name, tv_id, tv_name, series, pv, uv, gmt_create, gmt_modified from tb_shortplay_ad_data
    </sql>

    <select id="selectIdBy" resultType="Long">
        select id
        from tb_shortplay_ad_data
        where cur_date = #{curDate} and miniapp_id = #{miniappId} and tv_id = #{tvId} and series = #{series}
    </select>

    <select id="selectShortplayAdDataList" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity" resultMap="ShortplayAdDataResult">
        <include refid="selectShortplayAdDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="tvId != null "> and tv_id = #{tvId}</if>
            <if test="tvName != null  and tvName != ''"> and tv_name like concat('%', #{tvName}, '%')</if>
            <if test="miniappId != null  and miniappId != ''"> and miniapp_id = #{miniappId}</if>
            <if test="miniappName != null  and miniappName != ''"> and miniapp_name like concat('%', #{miniappName}, '%')</if>
            <if test="series != null "> and series = #{series}</if>
            <if test="pv != null "> and pv = #{pv}</if>
            <if test="uv != null "> and uv = #{uv}</if>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayAdDataById" parameterType="Long" resultMap="ShortplayAdDataResult">
            <include refid="selectShortplayAdDataVo"/>
            where id = #{id}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_shortplay_ad_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="miniappId != null and miniappId != ''">miniapp_id,</if>
            <if test="miniappName != null and miniappName != ''">miniapp_name,</if>
            <if test="tvId != null">tv_id,</if>
            <if test="tvName != null and tvName != ''">tv_name,</if>
            <if test="series != null">series,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="miniappId != null and miniappId != ''">#{miniappId},</if>
            <if test="miniappName != null and miniappName != ''">#{miniappName},</if>
            <if test="tvId != null">#{tvId},</if>
            <if test="tvName != null and tvName != ''">#{tvName},</if>
            <if test="series != null">#{series},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity">
        update tb_shortplay_ad_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="pvAdd != null">pv = pv + #{pvAdd},</if>
            <if test="uvAdd != null">uv = uv + #{uvAdd},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
