<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.data.ShortplayActionDataCustomerMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.data.ShortplayActionDataCustomerEntity" id="ShortplayActionDataResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="miniappId"    column="miniapp_id"    />
            <result property="miniappName"    column="miniapp_name"    />
            <result property="exposurePv"    column="exposure_pv"    />
            <result property="exposureUv"    column="exposure_uv"    />
            <result property="clickPv"    column="click_pv"    />
            <result property="clickUv"    column="click_uv"    />
            <result property="focusPv"    column="focus_pv"    />
            <result property="focusUv"    column="focus_uv"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayActionDataVo">
        select id, cur_date, miniapp_id, miniapp_name, exposure_pv, exposure_uv, click_pv, click_uv, focus_pv, focus_uv, gmt_create, gmt_modified from tb_shortplay_action_data
    </sql>

    <select id="selectIdBy" resultType="Long">
        select id
        from tb_shortplay_action_data
        where cur_date = #{curDate} and miniapp_id = #{miniappId}
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayActionDataCustomerEntity" resultMap="ShortplayActionDataResult">
        <include refid="selectShortplayActionDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="miniappId != null  and miniappId != ''"> and miniapp_id = #{miniappId}</if>
            <if test="miniappName != null  and miniappName != ''"> and miniapp_name like concat('%', #{miniappName}, '%')</if>
        </where>
        order by id desc
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_shortplay_action_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="miniappId != null and miniappId != ''">miniapp_id,</if>
            <if test="miniappName != null and miniappName != ''">miniapp_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="miniappId != null and miniappId != ''">#{miniappId},</if>
            <if test="miniappName != null and miniappName != ''">#{miniappName},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity">
        update tb_shortplay_action_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="exposurePvAdd != null">exposure_pv = exposure_pv + #{exposurePvAdd},</if>
            <if test="exposureUvAdd != null">exposure_uv = exposure_uv + #{exposureUvAdd},</if>
            <if test="clickPvAdd != null">click_pv = click_pv + #{clickPvAdd},</if>
            <if test="clickUvAdd != null">click_uv = click_uv + #{clickUvAdd},</if>
            <if test="focusPvAdd != null">focus_pv = focus_pv + #{focusPvAdd},</if>
            <if test="focusUvAdd != null">focus_uv = focus_uv + #{focusUvAdd},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
