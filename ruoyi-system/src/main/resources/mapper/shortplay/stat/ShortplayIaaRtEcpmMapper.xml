<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.stat.ShortplayIaaRtEcpmMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.stat.ShortplayIaaRtEcpm" id="ShortplayIaaRtEcpmResult">
        <result property="id"    column="id"    />
        <result property="keyId"    column="key_id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="mpId"    column="mp_id"    />
        <result property="cost"    column="cost"    />
        <result property="openId"    column="open_id"    />
        <result property="configId"    column="config_id"    />
        <result property="eventTime"    column="event_time"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="promotionId"    column="promotion_id"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="tfid"    column="tfid"    />
        <result property="registerTime"    column="register_time"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbDouyinIaaRtEcpmVo">
        select id, key_id, cur_date, mp_id, cost, open_id, config_id, event_time, advertiser_id, promotion_id, invite_code, tfid, register_time, gmt_create, gmt_modified from tb_shortplay_iaa_rt_ecpm
    </sql>

    <select id="selectShortplayIaaRtEcpmList" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayIaaRtEcpm" resultMap="ShortplayIaaRtEcpmResult">
        <include refid="selectTbDouyinIaaRtEcpmVo"/>
        <where>
            <if test="keyId != null"> and key_id = #{keyId}</if>
            <if test="mpId != null"> and mp_id = #{mpId}</if>
            <if test="cost != null"> and cost = #{cost}</if>
            <if test="openId != null"> and open_id = #{openId}</if>
            <if test="eventTime != null "> and event_time = #{eventTime}</if>
        </where>
    </select>

    <select id="selectListBy"  resultMap="ShortplayIaaRtEcpmResult">
        <include refid="selectTbDouyinIaaRtEcpmVo"/>
        <where>
            <if test="openId != null"> and open_id = #{openId}</if>
            <if test="promotionId != null"> and promotion_id = #{promotionId}</if>
            <if test="startTime != null"> and event_time &gt;= #{startTime}</if>
            <if test="endTime != null"> and event_time &lt; #{endTime}</if>
        </where>
    </select>

    <select id="sumCostForFixedCost" resultType="com.ruoyi.system.bo.shortplay.ShortplayIaaFixedCostUser">
        select promotion_id as promotionId, SUM(ifnull(cost, 0)) as costSum
        from tb_shortplay_iaa_rt_ecpm e
        where e.config_id = #{configId} and e.event_time &gt;= #{startTime} and e.event_time &lt;= #{endTime} and promotion_id != ''
        group by promotion_id
    </select>

    <select id="selectFixedCostUserListByIpu" resultType="com.ruoyi.system.bo.shortplay.ShortplayIaaFixedCostUser">
        select e.open_id as openId, count(1) as ipu, max(e.event_time) as max_event_time
        from tb_shortplay_iaa_rt_ecpm e
        where e.config_id = #{configId} and e.event_time &gt;= #{startTime} and e.event_time &lt;= #{endTime} and e.promotion_id = #{promotionId} and cost >= #{ecpmPrice}
            and e.open_id not in (
                select `open_id`
                from `tb_shortplay_iaa_conversion_record`
                where gmt_create &gt;= #{startTime} and gmt_create &lt;= #{endTime} and gmt_create &gt;= DATE_SUB(#{endTime}, INTERVAL 8 HOUR)
            )
            and e.open_id not in (
                select `open_id`
                from `tb_shortplay_iaa_rt_ecpm`
                where event_time &gt;= #{startTime} and event_time &gt;= DATE_SUB(#{endTime}, INTERVAL 2 HOUR)
                GROUP BY open_id
                HAVING COUNT(DISTINCT `promotion_id`) > 1
            )
        GROUP BY e.open_id
        HAVING ipu >= #{ipu}
        ORDER BY max_event_time desc
        limit #{count}
    </select>

    <select id="selectFixedCostUserList1" resultType="com.ruoyi.system.bo.shortplay.ShortplayIaaFixedCostUser">
        select e.open_id as openId, sum(e.cost) as costSum, COUNT(1) as ecpmTimes
        from tb_shortplay_iaa_rt_ecpm e
        where e.config_id = #{configId} and e.event_time &gt;= #{startTime} and e.event_time &lt;= #{endTime} and cost >= #{ecpmPrice} and e.promotion_id = #{promotionId}
            and e.open_id in (
                SELECT DISTINCT `open_id`  FROM `tb_shortplay_iaa_rt_ecpm` WHERE `event_time`  >= DATE_SUB(NOW(), INTERVAL #{recentUser} MINUTE)
            )
            and e.open_id not in (
                select `open_id`
                from `tb_shortplay_iaa_conversion_record`
                where gmt_create &gt;= #{startTime} and gmt_create &lt;= #{endTime} and gmt_create &gt;= DATE_SUB(#{endTime}, INTERVAL 8 HOUR)
            )
            and e.open_id not in (
                select `open_id`
                from `tb_shortplay_iaa_rt_ecpm`
                where event_time &gt;= #{startTime} and event_time &gt;= DATE_SUB(#{endTime}, INTERVAL 2 HOUR)
                GROUP BY open_id
                HAVING COUNT(DISTINCT `promotion_id`) > 1
            )
        GROUP BY e.open_id
        HAVING ecpmTimes >= #{ecpmTimes}
        ORDER BY costSum desc
        limit #{count}
    </select>

    <select id="selectFixedCostUserList2" resultType="com.ruoyi.system.bo.shortplay.ShortplayIaaFixedCostUser">
        select e.open_id as openId, SUM(cost) as costSum
        from tb_shortplay_iaa_rt_ecpm e
        where e.config_id = #{configId} and e.event_time &gt;= #{startTime} and e.event_time &lt;= #{endTime} and e.promotion_id = #{promotionId}
            and e.open_id not in (
                select `open_id`
                from `tb_shortplay_iaa_conversion_record`
                where gmt_create &gt;= #{startTime} and gmt_create &lt;= #{endTime} and gmt_create &gt;= DATE_SUB(#{endTime}, INTERVAL 8 HOUR)
            )
          and e.open_id not in (
            select `open_id`
            from `tb_shortplay_iaa_rt_ecpm`
            where event_time &gt;= #{startTime} and event_time &gt;= DATE_SUB(#{endTime}, INTERVAL 2 HOUR)
            GROUP BY open_id
            HAVING COUNT(DISTINCT `promotion_id`) > 1
        )
        GROUP BY e.open_id
        ORDER BY costSum desc
        limit #{count}
    </select>
    <select id="selectRealTimeDataReportList" resultType="com.ruoyi.system.vo.juliang.AgtRealTimeDataVo">
        SELECT
            a.mp_id AS appId,
            SUM(a.cost) AS totalCost,
            <if test="req.dimension != null and req.dimension.contains('dateStr')">
                a.cur_date AS dateStr,
            </if>
            <if test="req.dimension != null and req.dimension.contains('hourStr')">
                HOUR(a.event_time) AS hourStr,
            </if>
            COUNT(1) AS adNum
        FROM
            tb_shortplay_iaa_rt_ecpm AS a FORCE index(idx_date,idx_mp_date)
        WHERE
            1=1
            <if test="req.miniappIds != null and req.miniappIds.size() > 0">
                AND a.mp_id IN
                <foreach item="item" collection="req.miniappIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            AND a.cur_date BETWEEN #{req.startDate} AND #{req.endDate}
            <if test="req.hourStr != null">
                AND HOUR(a.event_time) = #{req.hourStr}
            </if>
        GROUP BY
            <if test="req.dimension != null and req.dimension.contains('dateStr')">
                dateStr,
            </if>
            <if test="req.dimension != null and req.dimension.contains('hourStr')">
                hourStr,
            </if>
            a.mp_id
        ORDER BY
            <if test="req.dimension != null and req.dimension.contains('dateStr')">
                dateStr desc,
            </if>
            <if test="req.dimension != null and req.dimension.contains('hourStr')">
                hourStr desc,
            </if>
            ${req.orderColumn} ${req.orderType}
    </select>

    <insert id="insertShortplayIaaRtEcpm" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayIaaRtEcpm" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_shortplay_iaa_rt_ecpm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="keyId != null">key_id,</if>
            <if test="curDate != null">cur_date,</if>
            <if test="mpId != null">mp_id,</if>
            <if test="cost != null">cost,</if>
            <if test="openId != null ">open_id,</if>
            <if test="configId != null ">config_id,</if>
            <if test="eventTime != null">event_time,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="promotionId != null">promotion_id,</if>
            <if test="inviteCode != null">invite_code,</if>
            <if test="tfid != null">tfid,</if>
            <if test="registerTime != null">register_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="keyId != null">#{keyId},</if>
            <if test="curDate != null">#{curDate},</if>
            <if test="mpId != null">#{mpId},</if>
            <if test="cost != null">#{cost},</if>
            <if test="openId != null">#{openId},</if>
            <if test="configId != null">#{configId},</if>
            <if test="eventTime != null">#{eventTime},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="promotionId != null">#{promotionId},</if>
            <if test="inviteCode != null">#{inviteCode},</if>
            <if test="tfid != null">#{tfid},</if>
            <if test="registerTime != null">#{registerTime},</if>
         </trim>
    </insert>

    <update id="updateShortplayIaaRtEcpm" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayIaaRtEcpm">
        update tb_shortplay_iaa_rt_ecpm
        <trim prefix="SET" suffixOverrides=",">
            <if test="keyId != null">key_id = #{keyId},</if>
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="mpId != null">mp_id = #{mpId},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="eventTime != null">event_time = #{eventTime},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="promotionId != null">promotion_id = #{promotionId},</if>
            <if test="inviteCode != null">invite_code = #{inviteCode},</if>
            <if test="tfid != null">tfid = #{tfid},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
