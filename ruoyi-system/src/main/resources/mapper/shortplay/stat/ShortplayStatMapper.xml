<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.stat.ShortplayStatMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.stat.ShortplayStatEntity" id="ShortplayStatResult">
            <result property="id"    column="id"    />
            <result property="rid"    column="rid"    />
            <result property="type"    column="type"    />
            <result property="userId"    column="user_id"    />
            <result property="wxOpenid"    column="wx_openid"    />
            <result property="wxAppId"    column="wx_app_id"    />
            <result property="aliOpenid"    column="ali_openid"    />
            <result property="aliAppId"    column="ali_app_id"    />
            <result property="ip"    column="ip"    />
            <result property="userAgent"    column="user_agent"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayStatVo">
        select id, rid, type, user_id, wx_openid, wx_app_id, ali_openid, ali_app_id, ip, user_agent, gmt_create, gmt_modified from tb_shortplay_stat
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayStatEntity" resultMap="ShortplayStatResult">
        <include refid="selectShortplayStatVo"/>
        <where>
            <if test="rid != null  and rid != ''"> and rid = #{rid}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="wxOpenid != null  and wxOpenid != ''"> and wx_openid = #{wxOpenid}</if>
            <if test="wxAppId != null  and wxAppId != ''"> and wx_app_id = #{wxAppId}</if>
            <if test="aliOpenid != null and aliOpenid != ''"> and ali_openid = #{aliOpenid}</if>
            <if test="aliAppId != null and aliAppId != ''"> and ali_app_id = #{aliAppId}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="userAgent != null  and userAgent != ''"> and user_agent = #{userAgent}</if>
        </where>
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayStatEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_stat
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="rid != null and rid != ''">rid,</if>
                    <if test="type != null">type,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="wxOpenid != null">wx_openid,</if>
                    <if test="wxAppId != null">wx_app_id,</if>
                    <if test="aliOpenid != null">ali_openid,</if>
                    <if test="aliAppId != null">ali_app_id,</if>
                    <if test="ip != null">ip,</if>
                    <if test="userAgent != null">user_agent,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="rid != null and rid != ''">#{rid},</if>
                    <if test="type != null">#{type},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="wxOpenid != null">#{wxOpenid},</if>
                    <if test="wxAppId != null">#{wxAppId},</if>
                    <if test="aliOpenid != null">#{aliOpenid},</if>
                    <if test="aliAppId != null">#{aliAppId},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="userAgent != null">#{userAgent},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayStatEntity">
        update tb_shortplay_stat
        <trim prefix="SET" suffixOverrides=",">
                    <if test="rid != null and rid != ''">rid = #{rid},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="wxOpenid != null">wx_openid = #{wxOpenid},</if>
                    <if test="wxAppId != null">wx_app_id = #{wxAppId},</if>
                    <if test="aliOpenid != null">ali_openid = #{aliOpenid},</if>
                    <if test="aliAppId != null">ali_app_id = #{aliAppId},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="userAgent != null">user_agent = #{userAgent},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
