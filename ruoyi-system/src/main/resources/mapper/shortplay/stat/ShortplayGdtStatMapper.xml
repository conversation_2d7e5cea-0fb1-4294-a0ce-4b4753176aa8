<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.stat.ShortplayGdtStatMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.stat.ShortplayGdtStatEntity" id="ShortplayGdtStatResult">
            <result property="id"    column="id"    />
            <result property="wechatOpenid"    column="wechat_openid"    />
            <result property="clickId"    column="click_id"    />
            <result property="callback"    column="callback"    />
            <result property="clickTime"    column="click_time"    />
            <result property="accountId"    column="account_id"    />
            <result property="deviceOsType"    column="device_os_type"    />
            <result property="adgroupId"    column="adgroup_id"    />
            <result property="adId"    column="ad_id"    />
            <result property="ip"    column="ip"    />
            <result property="userAgent"    column="user_agent"    />
        <result property="convSuccess"    column="conv_success"    />
        <result property="convTime"    column="conv_time"    />
        <result property="resp"    column="resp"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="siteSetName"    column="site_set_name"    />
    </resultMap>

    <sql id="selectShortplayGdtStatVo">
        select id, wechat_openid, click_id, callback, click_time, account_id, device_os_type, adgroup_id, ad_id, ip, user_agent, conv_success, conv_time, resp, gmt_create, gmt_modified, site_set_name from tb_shortplay_gdt_stat
    </sql>
    <delete id="deleteGdtStatByDate">
        delete from tb_shortplay_gdt_stat where gmt_create &lt;= #{date}
    </delete>

    <select id="selectShortplayGdtStatList" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayGdtStatEntity" resultMap="ShortplayGdtStatResult">
        <include refid="selectShortplayGdtStatVo"/>
        <where>
                        <if test="wechatOpenid != null  and wechatOpenid != ''"> and wechat_openid = #{wechatOpenid}</if>
                        <if test="clickId != null  and clickId != ''"> and click_id = #{clickId}</if>
                        <if test="callback != null  and callback != ''"> and callback = #{callback}</if>
                        <if test="clickTime != null  and clickTime != ''"> and click_time = #{clickTime}</if>
                        <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
                        <if test="deviceOsType != null  and deviceOsType != ''"> and device_os_type = #{deviceOsType}</if>
                        <if test="adgroupId != null  and adgroupId != ''"> and adgroup_id = #{adgroupId}</if>
                        <if test="adId != null  and adId != ''"> and ad_id = #{adId}</if>
                        <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
                        <if test="userAgent != null  and userAgent != ''"> and user_agent = #{userAgent}</if>
        </where>
    </select>

    <select id="selectByWechatOpenid" parameterType="String" resultMap="ShortplayGdtStatResult">
            <include refid="selectShortplayGdtStatVo"/>
            where wechat_openid = #{wechatOpenid}
            order by id desc
            limit 1
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayGdtStatEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_gdt_stat
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="wechatOpenid != null and wechatOpenid != ''">wechat_openid,</if>
                    <if test="clickId != null and clickId != ''">click_id,</if>
                    <if test="callback != null and callback != ''">callback,</if>
                    <if test="clickTime != null and clickTime != ''">click_time,</if>
                    <if test="accountId != null">account_id,</if>
                    <if test="deviceOsType != null">device_os_type,</if>
                    <if test="adgroupId != null">adgroup_id,</if>
                    <if test="adId != null">ad_id,</if>
                    <if test="ip != null">ip,</if>
                    <if test="userAgent != null">user_agent,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="siteSetName != null">site_set_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="wechatOpenid != null and wechatOpenid != ''">#{wechatOpenid},</if>
                    <if test="clickId != null and clickId != ''">#{clickId},</if>
                    <if test="callback != null and callback != ''">#{callback},</if>
                    <if test="clickTime != null and clickTime != ''">#{clickTime},</if>
                    <if test="accountId != null">#{accountId},</if>
                    <if test="deviceOsType != null">#{deviceOsType},</if>
                    <if test="adgroupId != null">#{adgroupId},</if>
                    <if test="adId != null">#{adId},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="userAgent != null">#{userAgent},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="siteSetName != null">#{siteSetName},</if>
        </trim>
    </insert>

    <update id="updateShortplayGdtStat" parameterType="com.ruoyi.system.entity.shortplay.stat.ShortplayGdtStatEntity">
        update tb_shortplay_gdt_stat
        <trim prefix="SET" suffixOverrides=",">
                    <if test="wechatOpenid != null and wechatOpenid != ''">wechat_openid = #{wechatOpenid},</if>
                    <if test="clickId != null and clickId != ''">click_id = #{clickId},</if>
                    <if test="callback != null and callback != ''">callback = #{callback},</if>
                    <if test="clickTime != null and clickTime != ''">click_time = #{clickTime},</if>
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="deviceOsType != null">device_os_type = #{deviceOsType},</if>
                    <if test="adgroupId != null">adgroup_id = #{adgroupId},</if>
                    <if test="adId != null">ad_id = #{adId},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="userAgent != null">user_agent = #{userAgent},</if>
                    <if test="convSuccess != null">conv_success = #{convSuccess},</if>
                    <if test="convTime != null">conv_time = #{convTime},</if>
                    <if test="resp != null">resp = #{resp},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
