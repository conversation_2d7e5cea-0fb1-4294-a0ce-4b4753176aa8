<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.stat.GdtSiteSetStatMapper">
    
    <resultMap type="com.ruoyi.system.entity.shortplay.stat.GdtSiteSetStatEntity" id="GdtSiteSetStatResult">
        <id     property="id"          column="id"           />
        <result property="curDate"     column="cur_date"    />
        <result property="siteSetCode"  column="site_set_code"/>
        <result property="appId"       column="app_id"      />
        <result property="totalUsers"   column="total_users"  />
        <result property="validUsers"   column="valid_users"  />
        <result property="ipu1Count"    column="ipu1_count"   />
        <result property="ipu2Count"    column="ipu2_count"   />
        <result property="ipu3Count"    column="ipu3_count"   />
        <result property="ipu4Count"    column="ipu4_count"   />
        <result property="ipu5Count"    column="ipu5_count"   />
        <result property="ipuGt5Count"  column="ipu_gt5_count"/>
        <result property="gmtCreate"   column="gmt_create"  />
        <result property="gmtModified"   column="gmt_modified"  />
    </resultMap>
    
    <sql id="selectGdtSiteSetStatVo">
        select id, cur_date, site_set_code, app_id, total_users, valid_users,
               ipu1_count, ipu2_count, ipu3_count, ipu4_count, ipu5_count, ipu_gt5_count,
               gmt_create, gmt_modified
        from tb_gdt_site_set_stat
    </sql>
    
    <select id="selectGdtSiteSetStatList" parameterType="com.ruoyi.system.entity.shortplay.stat.GdtSiteSetStatEntity" resultMap="GdtSiteSetStatResult">
        <include refid="selectGdtSiteSetStatVo"/>
        <where>
            <if test="curDate != null ">and cur_date = #{curDate}</if>
            <if test="siteSetCode != null  and siteSetCode != ''">and site_set_code = #{siteSetCode}</if>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
        </where>
        order by cur_date desc, site_set_code asc, app_id asc
    </select>
    
    <select id="selectGdtSiteSetStatById" parameterType="Long" resultMap="GdtSiteSetStatResult">
        <include refid="selectGdtSiteSetStatVo"/>
        where id = #{id}
    </select>
    
    <select id="selectGdtSiteSetStatByDateAndCode" resultMap="GdtSiteSetStatResult">
        <include refid="selectGdtSiteSetStatVo"/>
        where cur_date = #{curDate} and site_set_code = #{siteSetCode} and app_id = #{appId}
    </select>
    
    <select id="selectGdtSiteSetStatByDateRange" resultMap="GdtSiteSetStatResult">
        <include refid="selectGdtSiteSetStatVo"/>
        where cur_date between #{startDate} and #{endDate}
        order by cur_date desc, site_set_code asc, app_id asc
    </select>
    
    <insert id="insertGdtSiteSetStat" parameterType="com.ruoyi.system.entity.shortplay.stat.GdtSiteSetStatEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_site_set_stat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="siteSetCode != null">site_set_code,</if>
            <if test="appId != null">app_id,</if>
            <if test="totalUsers != null">total_users,</if>
            <if test="validUsers != null">valid_users,</if>
            <if test="ipu1Count != null">ipu1_count,</if>
            <if test="ipu2Count != null">ipu2_count,</if>
            <if test="ipu3Count != null">ipu3_count,</if>
            <if test="ipu4Count != null">ipu4_count,</if>
            <if test="ipu5Count != null">ipu5_count,</if>
            <if test="ipuGt5Count != null">ipu_gt5_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="siteSetCode != null">#{siteSetCode},</if>
            <if test="appId != null">#{appId},</if>
            <if test="totalUsers != null">#{totalUsers},</if>
            <if test="validUsers != null">#{validUsers},</if>
            <if test="ipu1Count != null">#{ipu1Count},</if>
            <if test="ipu2Count != null">#{ipu2Count},</if>
            <if test="ipu3Count != null">#{ipu3Count},</if>
            <if test="ipu4Count != null">#{ipu4Count},</if>
            <if test="ipu5Count != null">#{ipu5Count},</if>
            <if test="ipuGt5Count != null">#{ipuGt5Count},</if>
        </trim>
    </insert>
    
    <update id="updateGdtSiteSetStat" parameterType="com.ruoyi.system.entity.shortplay.stat.GdtSiteSetStatEntity">
        update tb_gdt_site_set_stat
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="siteSetCode != null">site_set_code = #{siteSetCode},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="totalUsers != null">total_users = #{totalUsers},</if>
            <if test="validUsers != null">valid_users = #{validUsers},</if>
            <if test="ipu1Count != null">ipu1_count = #{ipu1Count},</if>
            <if test="ipu2Count != null">ipu2_count = #{ipu2Count},</if>
            <if test="ipu3Count != null">ipu3_count = #{ipu3Count},</if>
            <if test="ipu4Count != null">ipu4_count = #{ipu4Count},</if>
            <if test="ipu5Count != null">ipu5_count = #{ipu5Count},</if>
            <if test="ipuGt5Count != null">ipu_gt5_count = #{ipuGt5Count},</if>
        </trim>
        where id = #{id}
    </update>
    
    <delete id="deleteGdtSiteSetStatById" parameterType="Long">
        delete from tb_gdt_site_set_stat where id = #{id}
    </delete>
    
    <delete id="deleteGdtSiteSetStatByIds" parameterType="Long">
        delete from tb_gdt_site_set_stat where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 插入或更新广点通版位用户IPU统计 -->
    <insert id="insertOrUpdateGdtSiteSetStat" parameterType="com.ruoyi.system.entity.shortplay.stat.GdtSiteSetStatEntity">
        INSERT INTO tb_gdt_site_set_stat
        (
            cur_date,
            site_set_code,
            app_id,
            total_users,
            valid_users,
            ipu1_count,
            ipu2_count,
            ipu3_count,
            ipu4_count,
            ipu5_count,
            ipu_gt5_count
        )
        VALUES
        (
            #{curDate},
            #{siteSetCode},
            #{appId},
            #{totalUsers},
            #{validUsers},
            #{ipu1Count},
            #{ipu2Count},
            #{ipu3Count},
            #{ipu4Count},
            #{ipu5Count},
            #{ipuGt5Count}
        )
        ON DUPLICATE KEY UPDATE
            total_users = VALUES(total_users),
            valid_users = VALUES(valid_users),
            ipu1_count = VALUES(ipu1_count),
            ipu2_count = VALUES(ipu2_count),
            ipu3_count = VALUES(ipu3_count),
            ipu4_count = VALUES(ipu4_count),
            ipu5_count = VALUES(ipu5_count),
            ipu_gt5_count = VALUES(ipu_gt5_count)
    </insert>

    <!-- 批量插入或更新广点通版位用户IPU统计 -->
    <insert id="batchInsertOrUpdateGdtSiteSetStat" parameterType="java.util.List">
        INSERT INTO tb_gdt_site_set_stat
        (
            cur_date,
            site_set_code,
            app_id,
            total_users,
            valid_users,
            ipu1_count,
            ipu2_count,
            ipu3_count,
            ipu4_count,
            ipu5_count,
            ipu_gt5_count
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.curDate},
            #{item.siteSetCode},
            #{item.appId},
            #{item.totalUsers},
            #{item.validUsers},
            #{item.ipu1Count},
            #{item.ipu2Count},
            #{item.ipu3Count},
            #{item.ipu4Count},
            #{item.ipu5Count},
            #{item.ipuGt5Count}
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
            total_users = VALUES(total_users),
            valid_users = VALUES(valid_users),
            ipu1_count = VALUES(ipu1_count),
            ipu2_count = VALUES(ipu2_count),
            ipu3_count = VALUES(ipu3_count),
            ipu4_count = VALUES(ipu4_count),
            ipu5_count = VALUES(ipu5_count),
            ipu_gt5_count = VALUES(ipu_gt5_count)
    </insert>
    
    <!-- 根据筛选条件查询版位统计数据 -->
    <select id="selectGdtSiteSetStatWithFilter" resultType="com.ruoyi.system.vo.shortplay.GdtSiteSetStatVO">
        SELECT
            <if test="withDateDimension == true">
                DATE_FORMAT(cur_date, '%Y-%m-%d') AS date,
            </if>
            <if test="withDateDimension == false">
                NULL AS date,
            </if>
            app_id AS appId,
            <if test="withSiteSetDimension == true">
                site_set_code AS siteSetCode,
            </if>
            <if test="withSiteSetDimension == false">
                NULL AS siteSetCode,
            </if>
            <if test="withSiteSetDimension == false or withDateDimension == false">
                SUM(ipu1_count) AS ipu1,
                SUM(ipu2_count) AS ipu2,
                SUM(ipu3_count) AS ipu3,
                SUM(ipu4_count) AS ipu4,
                SUM(ipu5_count) AS ipu5,
                SUM(ipu_gt5_count) AS ipuGt5
            </if>
            <if test="withSiteSetDimension == true and withDateDimension == true">
                ipu1_count AS ipu1,
                ipu2_count AS ipu2,
                ipu3_count AS ipu3,
                ipu4_count AS ipu4,
                ipu5_count AS ipu5,
                ipu_gt5_count AS ipuGt5
            </if>
        FROM
            tb_gdt_site_set_stat
        WHERE
            cur_date BETWEEN #{startDate} AND #{endDate}
            <if test="appIds != null and appIds.size() > 0">
                AND app_id IN
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="siteSetCodes != null and siteSetCodes.size() > 0">
                AND site_set_code IN
                <foreach collection="siteSetCodes" item="siteSetCode" open="(" separator="," close=")">
                    #{siteSetCode}
                </foreach>
            </if>
        <!-- 只有当至少有一个维度需要聚合时才使用GROUP BY -->
        <if test="withSiteSetDimension == false or withDateDimension == false">
            GROUP BY
            <!-- 如果保留日期维度，则按日期分组 -->
            <if test="withDateDimension == true">
                cur_date,
            </if>
            <!-- 始终按应用ID分组 -->
            app_id
            <!-- 如果保留版位维度，则按版位分组 -->
            <if test="withSiteSetDimension == true">
                , site_set_code
            </if>
        </if>
        ORDER BY
            <if test="withDateDimension == true">
                cur_date DESC,
            </if>
            app_id ASC
            <if test="withSiteSetDimension == true">
                , site_set_code ASC
            </if>
    </select>
</mapper> 