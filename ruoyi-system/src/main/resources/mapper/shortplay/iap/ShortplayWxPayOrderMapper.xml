<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.iap.ShortplayWxPayOrderMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.iap.ShortplayWxPayOrderEntity" id="ShortplayWxPayOrderResult">
            <result property="id"    column="id"    />
            <result property="orderType"    column="order_type"    />
            <result property="outTradeNo"    column="out_trade_no"    />
            <result property="userId"    column="user_id"    />
            <result property="openId"    column="open_id"    />
            <result property="appId"    column="app_id"    />
            <result property="mchId"    column="mch_id"    />
            <result property="description"    column="description"    />
            <result property="tradeType"    column="trade_type"    />
            <result property="totalFee"    column="total_fee"    />
            <result property="ip"    column="ip"    />
            <result property="referer"    column="referer"    />
            <result property="userAgent"    column="user_agent"    />
            <result property="payStatus"    column="pay_status"    />
            <result property="tradeState"    column="trade_state"    />
            <result property="transactionId"    column="transaction_id"    />
            <result property="payerTotal"    column="payer_total"    />
            <result property="currency"    column="currency"    />
            <result property="bankType"    column="bank_type"    />
            <result property="successTime"    column="success_time"    />
            <result property="unlockTvId"    column="unlock_tv_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShorplayWxPayOrderVo">
        select id, order_type, out_trade_no, user_id, open_id, app_id, mch_id, description, trade_type, total_fee, ip, referer, user_agent, pay_status, trade_state, transaction_id, payer_total, currency, bank_type, success_time, unlock_tv_id, gmt_create, gmt_modified from tb_shortplay_wx_pay_order
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.iap.ShortplayWxPayOrderEntity" resultMap="ShortplayWxPayOrderResult">
        <include refid="selectShorplayWxPayOrderVo"/>
        <where>
            <if test="orderType != null "> and order_type = #{orderType}</if>
            <if test="outTradeNo != null  and outTradeNo != ''"> and out_trade_no = #{outTradeNo}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="mchId != null  and mchId != ''"> and mch_id = #{mchId}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="tradeType != null  and tradeType != ''"> and trade_type = #{tradeType}</if>
            <if test="totalFee != null "> and total_fee = #{totalFee}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="referer != null  and referer != ''"> and referer = #{referer}</if>
            <if test="userAgent != null  and userAgent != ''"> and user_agent = #{userAgent}</if>
            <if test="payStatus != null "> and pay_status = #{payStatus}</if>
            <if test="tradeState != null  and tradeState != ''"> and trade_state = #{tradeState}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="payerTotal != null "> and payer_total = #{payerTotal}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="bankType != null  and bankType != ''"> and bank_type = #{bankType}</if>
            <if test="successTime != null "> and success_time = #{successTime}</if>
        </where>
    </select>

    <select id="selectByOutTradeNo" parameterType="String" resultMap="ShortplayWxPayOrderResult">
            <include refid="selectShorplayWxPayOrderVo"/>
            where out_trade_no = #{outTradeNo}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.iap.ShortplayWxPayOrderEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_wx_pay_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="orderType != null">order_type,</if>
                    <if test="outTradeNo != null and outTradeNo != ''">out_trade_no,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="openId != null and openId != ''">open_id,</if>
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="mchId != null and mchId != ''">mch_id,</if>
                    <if test="description != null and description != ''">description,</if>
                    <if test="tradeType != null and tradeType != ''">trade_type,</if>
                    <if test="totalFee != null">total_fee,</if>
                    <if test="ip != null and ip != ''">ip,</if>
                    <if test="referer != null">referer,</if>
                    <if test="userAgent != null">user_agent,</if>
                    <if test="payStatus != null">pay_status,</if>
                    <if test="tradeState != null">trade_state,</if>
                    <if test="transactionId != null">transaction_id,</if>
                    <if test="payerTotal != null">payer_total,</if>
                    <if test="currency != null">currency,</if>
                    <if test="bankType != null">bank_type,</if>
                    <if test="successTime != null">success_time,</if>
                    <if test="unlockTvId != null">unlock_tv_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="orderType != null">#{orderType},</if>
                    <if test="outTradeNo != null and outTradeNo != ''">#{outTradeNo},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="openId != null and openId != ''">#{openId},</if>
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="mchId != null and mchId != ''">#{mchId},</if>
                    <if test="description != null and description != ''">#{description},</if>
                    <if test="tradeType != null and tradeType != ''">#{tradeType},</if>
                    <if test="totalFee != null">#{totalFee},</if>
                    <if test="ip != null and ip != ''">#{ip},</if>
                    <if test="referer != null">#{referer},</if>
                    <if test="userAgent != null">#{userAgent},</if>
                    <if test="payStatus != null">#{payStatus},</if>
                    <if test="tradeState != null">#{tradeState},</if>
                    <if test="transactionId != null">#{transactionId},</if>
                    <if test="payerTotal != null">#{payerTotal},</if>
                    <if test="currency != null">#{currency},</if>
                    <if test="bankType != null">#{bankType},</if>
                    <if test="successTime != null">#{successTime},</if>
                    <if test="unlockTvId != null">#{unlockTvId},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.iap.ShortplayWxPayOrderEntity">
        update tb_shortplay_wx_pay_order
        <trim prefix="SET" suffixOverrides=",">
                    <if test="orderType != null">order_type = #{orderType},</if>
                    <if test="outTradeNo != null and outTradeNo != ''">out_trade_no = #{outTradeNo},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="openId != null and openId != ''">open_id = #{openId},</if>
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="mchId != null and mchId != ''">mch_id = #{mchId},</if>
                    <if test="description != null and description != ''">description = #{description},</if>
                    <if test="tradeType != null and tradeType != ''">trade_type = #{tradeType},</if>
                    <if test="totalFee != null">total_fee = #{totalFee},</if>
                    <if test="ip != null and ip != ''">ip = #{ip},</if>
                    <if test="referer != null">referer = #{referer},</if>
                    <if test="userAgent != null">user_agent = #{userAgent},</if>
                    <if test="payStatus != null">pay_status = #{payStatus},</if>
                    <if test="tradeState != null">trade_state = #{tradeState},</if>
                    <if test="transactionId != null">transaction_id = #{transactionId},</if>
                    <if test="payerTotal != null">payer_total = #{payerTotal},</if>
                    <if test="currency != null">currency = #{currency},</if>
                    <if test="bankType != null">bank_type = #{bankType},</if>
                    <if test="successTime != null">success_time = #{successTime},</if>
                    <if test="unlockTvId != null">unlock_tv_id = #{unlockTvId},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
