<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.app.ShortplayAppMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.app.ShortplayAppEntity" id="ShortplayAppResult">
            <result property="id"    column="id"    />
            <result property="tbId"    column="tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="dataJson"    column="data_json"    />
            <result property="opensearch"    column="opensearch"    />
            <result property="rechargeInstructions"    column="recharge_instructions"    />
            <result property="wxAdAdid"    column="wx_ad_adid"    />
            <result property="wxAdRecordNum"    column="wx_ad_record_num"    />
            <result property="ttAdAdid"    column="tt_ad_adid"    />
            <result property="ttAdRecordNum"    column="tt_ad_record_num"    />
            <result property="ttAdSwitch"    column="tt_ad_switch"    />
            <result property="ttImId"    column="tt_im_id"    />
            <result property="isEnabledPay"    column="is_enabled_pay"    />
            <result property="originid"    column="originid"    />
            <result property="qrurl"    column="qrurl"    />
            <result property="beannumber"    column="beannumber"    />
            <result property="appname"    column="appname"    />
            <result property="appsecret"    column="appsecret"    />
            <result property="indexTipsSwitch"    column="index_tips_switch"    />
            <result property="indexTipsText"    column="index_tips_text"    />
            <result property="workwxappid"    column="workwxappid"    />
            <result property="workwxurl"    column="workwxurl"    />
            <result property="xspsx"    column="xspsx"    />
            <result property="AddTime"    column="_add_time"    />
            <result property="appid"    column="appid"    />
            <result property="iospay"    column="iospay"    />
            <result property="openIndexTTAdvertModal"    column="openIndexTTAdvertModal"    />
            <result property="payMoney"    column="payMoney"    />
            <result property="pid"    column="pid"    />
            <result property="receiveTime"    column="receiveTime"    />
            <result property="tipsText"    column="tips_text"    />
            <result property="AddTimeStr"    column="_add_time_str"    />
            <result property="wxAdvertIdConfig"    column="wxAdvertIdConfig"    />
            <result property="flhd"    column="flhd"    />
            <result property="jjgx"    column="jjgx"    />
            <result property="tgemail"    column="tgemail"    />
            <result property="twoInRow"    column="twoInRow"    />
            <result property="appplatform"    column="appplatform"    />
            <result property="wxAdSwitch"    column="wx_ad_switch"    />
            <result property="gzgzhTip"    column="gzgzh_tip"    />
            <result property="gzhTip"    column="gzhTip"    />
            <result property="gzhTipText"    column="gzhTipText"    />
            <result property="freeBean"    column="freeBean"    />
            <result property="signinReward"    column="signin_reward"    />
            <result property="staticinviteCode"    column="staticInvite_code"    />
            <result property="tipsSwitch"    column="tips_switch"    />
            <result property="indexPhoneModal"    column="indexPhoneModal"    />
            <result property="interceptModalBg"    column="interceptModalBg"    />
            <result property="interceptModalRecord"    column="interceptModalRecord"    />
            <result property="aliAdAdid"    column="ali_ad_adid"    />
            <result property="aliAdRecordNum"    column="ali_ad_record_num"    />
            <result property="aliAdSwitch"    column="ali_ad_switch"    />
        <result property="componentAppid"    column="component_appid"    />
        <result property="componentAppname"    column="component_appname"    />
    </resultMap>

    <sql id="selectShortplayAppVo">
        select id, tb_id, gmt_create, gmt_modified, data_json, opensearch, recharge_instructions, wx_ad_adid, wx_ad_record_num, tt_ad_adid, tt_ad_record_num, tt_ad_switch, tt_im_id, is_enabled_pay, originid, qrurl, beannumber, appname, appsecret, index_tips_switch, index_tips_text, workwxappid, workwxurl, xspsx, _add_time, appid, iospay, openIndexTTAdvertModal, payMoney, pid, receiveTime, tips_text, _add_time_str, wxAdvertIdConfig, flhd, jjgx, tgemail, twoInRow, appplatform, wx_ad_switch, gzgzh_tip, gzhTip, gzhTipText, freeBean, signin_reward, staticInvite_code, tips_switch, indexPhoneModal, interceptModalBg, interceptModalRecord,
               ali_ad_adid, ali_ad_record_num, ali_ad_switch, component_appid, component_appname
        from tb_shortplay_app
    </sql>

    <select id="selectByAppid" parameterType="String" resultMap="ShortplayAppResult">
        <include refid="selectShortplayAppVo"/>
        where appid = #{appid}
    </select>

    <select id="selectShortplayAppList" parameterType="com.ruoyi.system.entity.shortplay.app.ShortplayAppEntity" resultMap="ShortplayAppResult">
        <include refid="selectShortplayAppVo"/>
        <where>
                        <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
                        <if test="dataJson != null  and dataJson != ''"> and data_json = #{dataJson}</if>
                        <if test="opensearch != null "> and opensearch = #{opensearch}</if>
                        <if test="rechargeInstructions != null  and rechargeInstructions != ''"> and recharge_instructions = #{rechargeInstructions}</if>
                        <if test="wxAdAdid != null  and wxAdAdid != ''"> and wx_ad_adid = #{wxAdAdid}</if>
                        <if test="wxAdRecordNum != null "> and wx_ad_record_num = #{wxAdRecordNum}</if>
                        <if test="isEnabledPay != null "> and is_enabled_pay = #{isEnabledPay}</if>
                        <if test="originid != null  and originid != ''"> and originid = #{originid}</if>
                        <if test="qrurl != null  and qrurl != ''"> and qrurl = #{qrurl}</if>
                        <if test="beannumber != null "> and beannumber = #{beannumber}</if>
                        <if test="appname != null  and appname != ''"> and appname like concat('%', #{appname}, '%')</if>
                        <if test="appsecret != null  and appsecret != ''"> and appsecret = #{appsecret}</if>
                        <if test="indexTipsSwitch != null "> and index_tips_switch = #{indexTipsSwitch}</if>
                        <if test="indexTipsText != null  and indexTipsText != ''"> and index_tips_text = #{indexTipsText}</if>
                        <if test="workwxappid != null  and workwxappid != ''"> and workwxappid = #{workwxappid}</if>
                        <if test="workwxurl != null  and workwxurl != ''"> and workwxurl = #{workwxurl}</if>
                        <if test="xspsx != null  and xspsx != ''"> and xspsx = #{xspsx}</if>
                        <if test="AddTime != null "> and _add_time = #{AddTime}</if>
                        <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                        <if test="iospay != null "> and iospay = #{iospay}</if>
                        <if test="openIndexTTAdvertModal != null "> and openIndexTTAdvertModal = #{openIndexTTAdvertModal}</if>
                        <if test="payMoney != null "> and payMoney = #{payMoney}</if>
                        <if test="pid != null  and pid != ''"> and pid = #{pid}</if>
                        <if test="receiveTime != null "> and receiveTime = #{receiveTime}</if>
                        <if test="tipsText != null  and tipsText != ''"> and tips_text = #{tipsText}</if>
                        <if test="AddTimeStr != null  and AddTimeStr != ''"> and _add_time_str = #{AddTimeStr}</if>
                        <if test="wxAdvertIdConfig != null  and wxAdvertIdConfig != ''"> and wxAdvertIdConfig = #{wxAdvertIdConfig}</if>
                        <if test="flhd != null  and flhd != ''"> and flhd = #{flhd}</if>
                        <if test="jjgx != null  and jjgx != ''"> and jjgx = #{jjgx}</if>
                        <if test="tgemail != null  and tgemail != ''"> and tgemail = #{tgemail}</if>
                        <if test="twoInRow != null "> and twoInRow = #{twoInRow}</if>
                        <if test="appplatform != null  and appplatform != ''"> and appplatform = #{appplatform}</if>
                        <if test="wxAdSwitch != null "> and wx_ad_switch = #{wxAdSwitch}</if>
                        <if test="gzgzhTip != null  and gzgzhTip != ''"> and gzgzh_tip = #{gzgzhTip}</if>
                        <if test="gzhTip != null "> and gzhTip = #{gzhTip}</if>
                        <if test="gzhTipText != null  and gzhTipText != ''"> and gzhTipText = #{gzhTipText}</if>
                        <if test="freeBean != null "> and freeBean = #{freeBean}</if>
                        <if test="signinReward != null "> and signin_reward = #{signinReward}</if>
                        <if test="staticinviteCode != null "> and staticInvite_code = #{staticinviteCode}</if>
                        <if test="tipsSwitch != null "> and tips_switch = #{tipsSwitch}</if>
                        <if test="indexPhoneModal != null "> and indexPhoneModal = #{indexPhoneModal}</if>
        </where>
    </select>

    <select id="selectShortplayAppById" parameterType="String" resultMap="ShortplayAppResult">
            <include refid="selectShortplayAppVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplayApp" parameterType="com.ruoyi.system.entity.shortplay.app.ShortplayAppEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="dataJson != null">data_json,</if>
                    <if test="opensearch != null">opensearch,</if>
                    <if test="rechargeInstructions != null">recharge_instructions,</if>
                    <if test="wxAdAdid != null">wx_ad_adid,</if>
                    <if test="wxAdRecordNum != null">wx_ad_record_num,</if>
                    <if test="ttAdAdid != null">tt_ad_adid,</if>
                    <if test="ttAdRecordNum != null">tt_ad_record_num,</if>
                    <if test="ttAdSwitch != null">tt_ad_switch,</if>
                    <if test="ttImId != null">tt_im_id,</if>
                    <if test="isEnabledPay != null">is_enabled_pay,</if>
                    <if test="originid != null">originid,</if>
                    <if test="qrurl != null">qrurl,</if>
                    <if test="beannumber != null">beannumber,</if>
                    <if test="appname != null">appname,</if>
                    <if test="appsecret != null">appsecret,</if>
                    <if test="indexTipsSwitch != null">index_tips_switch,</if>
                    <if test="indexTipsText != null">index_tips_text,</if>
                    <if test="workwxappid != null">workwxappid,</if>
                    <if test="workwxurl != null">workwxurl,</if>
                    <if test="xspsx != null">xspsx,</if>
                    <if test="AddTime != null">_add_time,</if>
                    <if test="appid != null">appid,</if>
                    <if test="iospay != null">iospay,</if>
                    <if test="openIndexTTAdvertModal != null">openIndexTTAdvertModal,</if>
                    <if test="payMoney != null">payMoney,</if>
                    <if test="pid != null">pid,</if>
                    <if test="receiveTime != null">receiveTime,</if>
                    <if test="tipsText != null">tips_text,</if>
                    <if test="AddTimeStr != null">_add_time_str,</if>
                    <if test="wxAdvertIdConfig != null">wxAdvertIdConfig,</if>
                    <if test="flhd != null">flhd,</if>
                    <if test="jjgx != null">jjgx,</if>
                    <if test="tgemail != null">tgemail,</if>
                    <if test="twoInRow != null">twoInRow,</if>
                    <if test="appplatform != null">appplatform,</if>
                    <if test="wxAdSwitch != null">wx_ad_switch,</if>
                    <if test="gzgzhTip != null">gzgzh_tip,</if>
                    <if test="gzhTip != null">gzhTip,</if>
                    <if test="gzhTipText != null">gzhTipText,</if>
                    <if test="freeBean != null">freeBean,</if>
                    <if test="signinReward != null">signin_reward,</if>
                    <if test="staticinviteCode != null">staticInvite_code,</if>
                    <if test="tipsSwitch != null">tips_switch,</if>
                    <if test="indexPhoneModal != null">indexPhoneModal,</if>
                    <if test="interceptModalBg != null">interceptModalBg,</if>
                    <if test="interceptModalRecord != null">interceptModalRecord,</if>
                    <if test="aliAdAdid != null">ali_ad_adid,</if>
                    <if test="aliAdRecordNum != null">ali_ad_record_num,</if>
                    <if test="aliAdSwitch != null">ali_ad_switch,</if>
                    <if test="componentAppid != null">component_appid,</if>
                    <if test="componentAppname != null">component_appname,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">#{tbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="dataJson != null">#{dataJson},</if>
                    <if test="opensearch != null">#{opensearch},</if>
                    <if test="rechargeInstructions != null">#{rechargeInstructions},</if>
                    <if test="wxAdAdid != null">#{wxAdAdid},</if>
                    <if test="wxAdRecordNum != null">#{wxAdRecordNum},</if>
                    <if test="ttAdAdid != null">#{ttAdAdid},</if>
                    <if test="ttAdRecordNum != null">#{ttAdRecordNum},</if>
                    <if test="ttAdSwitch != null">#{ttAdSwitch},</if>
                    <if test="ttImId != null">#{ttImId},</if>
                    <if test="isEnabledPay != null">#{isEnabledPay},</if>
                    <if test="originid != null">#{originid},</if>
                    <if test="qrurl != null">#{qrurl},</if>
                    <if test="beannumber != null">#{beannumber},</if>
                    <if test="appname != null">#{appname},</if>
                    <if test="appsecret != null">#{appsecret},</if>
                    <if test="indexTipsSwitch != null">#{indexTipsSwitch},</if>
                    <if test="indexTipsText != null">#{indexTipsText},</if>
                    <if test="workwxappid != null">#{workwxappid},</if>
                    <if test="workwxurl != null">#{workwxurl},</if>
                    <if test="xspsx != null">#{xspsx},</if>
                    <if test="AddTime != null">#{AddTime},</if>
                    <if test="appid != null">#{appid},</if>
                    <if test="iospay != null">#{iospay},</if>
                    <if test="openIndexTTAdvertModal != null">#{openIndexTTAdvertModal},</if>
                    <if test="payMoney != null">#{payMoney},</if>
                    <if test="pid != null">#{pid},</if>
                    <if test="receiveTime != null">#{receiveTime},</if>
                    <if test="tipsText != null">#{tipsText},</if>
                    <if test="AddTimeStr != null">#{AddTimeStr},</if>
                    <if test="wxAdvertIdConfig != null">#{wxAdvertIdConfig},</if>
                    <if test="flhd != null">#{flhd},</if>
                    <if test="jjgx != null">#{jjgx},</if>
                    <if test="tgemail != null">#{tgemail},</if>
                    <if test="twoInRow != null">#{twoInRow},</if>
                    <if test="appplatform != null">#{appplatform},</if>
                    <if test="wxAdSwitch != null">#{wxAdSwitch},</if>
                    <if test="gzgzhTip != null">#{gzgzhTip},</if>
                    <if test="gzhTip != null">#{gzhTip},</if>
                    <if test="gzhTipText != null">#{gzhTipText},</if>
                    <if test="freeBean != null">#{freeBean},</if>
                    <if test="signinReward != null">#{signinReward},</if>
                    <if test="staticinviteCode != null">#{staticinviteCode},</if>
                    <if test="tipsSwitch != null">#{tipsSwitch},</if>
                    <if test="indexPhoneModal != null">#{indexPhoneModal},</if>
                    <if test="interceptModalBg != null">#{interceptModalBg},</if>
                    <if test="interceptModalRecord != null">#{interceptModalRecord},</if>
                    <if test="aliAdAdid != null">#{aliAdAdid},</if>
                    <if test="aliAdRecordNum != null">#{aliAdRecordNum},</if>
                    <if test="aliAdSwitch != null">#{aliAdSwitch},</if>
                    <if test="componentAppid != null">#{componentAppid},</if>
                    <if test="componentAppname != null">#{componentAppname},</if>
        </trim>
    </insert>

    <update id="updateShortplayApp" parameterType="com.ruoyi.system.entity.shortplay.app.ShortplayAppEntity">
        update tb_shortplay_app
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tbId != null">tb_id = #{tbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="dataJson != null">data_json = #{dataJson},</if>
                    <if test="opensearch != null">opensearch = #{opensearch},</if>
                    <if test="rechargeInstructions != null">recharge_instructions = #{rechargeInstructions},</if>
                    <if test="wxAdAdid != null">wx_ad_adid = #{wxAdAdid},</if>
                    <if test="wxAdRecordNum != null">wx_ad_record_num = #{wxAdRecordNum},</if>
                    <if test="ttAdAdid != null">tt_ad_adid = #{ttAdAdid},</if>
                    <if test="ttAdRecordNum != null">tt_ad_record_num = #{ttAdRecordNum},</if>
                    <if test="ttAdSwitch != null">tt_ad_swtich = #{ttAdSwitch},</if>
                    <if test="ttImId != null">tt_im_id = #{ttImId},</if>
                    <if test="isEnabledPay != null">is_enabled_pay = #{isEnabledPay},</if>
                    <if test="originid != null">originid = #{originid},</if>
                    <if test="qrurl != null">qrurl = #{qrurl},</if>
                    <if test="beannumber != null">beannumber = #{beannumber},</if>
                    <if test="appname != null">appname = #{appname},</if>
                    <if test="appsecret != null">appsecret = #{appsecret},</if>
                    <if test="indexTipsSwitch != null">index_tips_switch = #{indexTipsSwitch},</if>
                    <if test="indexTipsText != null">index_tips_text = #{indexTipsText},</if>
                    <if test="workwxappid != null">workwxappid = #{workwxappid},</if>
                    <if test="workwxurl != null">workwxurl = #{workwxurl},</if>
                    <if test="xspsx != null">xspsx = #{xspsx},</if>
                    <if test="AddTime != null">_add_time = #{AddTime},</if>
                    <if test="appid != null">appid = #{appid},</if>
                    <if test="iospay != null">iospay = #{iospay},</if>
                    <if test="openIndexTTAdvertModal != null">openIndexTTAdvertModal = #{openIndexTTAdvertModal},</if>
                    <if test="payMoney != null">payMoney = #{payMoney},</if>
                    <if test="pid != null">pid = #{pid},</if>
                    <if test="receiveTime != null">receiveTime = #{receiveTime},</if>
                    <if test="tipsText != null">tips_text = #{tipsText},</if>
                    <if test="AddTimeStr != null">_add_time_str = #{AddTimeStr},</if>
                    <if test="wxAdvertIdConfig != null">wxAdvertIdConfig = #{wxAdvertIdConfig},</if>
                    <if test="flhd != null">flhd = #{flhd},</if>
                    <if test="jjgx != null">jjgx = #{jjgx},</if>
                    <if test="tgemail != null">tgemail = #{tgemail},</if>
                    <if test="twoInRow != null">twoInRow = #{twoInRow},</if>
                    <if test="appplatform != null">appplatform = #{appplatform},</if>
                    <if test="wxAdSwitch != null">wx_ad_switch = #{wxAdSwitch},</if>
                    <if test="gzgzhTip != null">gzgzh_tip = #{gzgzhTip},</if>
                    <if test="gzhTip != null">gzhTip = #{gzhTip},</if>
                    <if test="gzhTipText != null">gzhTipText = #{gzhTipText},</if>
                    <if test="freeBean != null">freeBean = #{freeBean},</if>
                    <if test="signinReward != null">signin_reward = #{signinReward},</if>
                    <if test="staticinviteCode != null">staticInvite_code = #{staticinviteCode},</if>
                    <if test="tipsSwitch != null">tips_switch = #{tipsSwitch},</if>
                    <if test="indexPhoneModal != null">indexPhoneModal = #{indexPhoneModal},</if>
                    <if test="interceptModalBg != null">interceptModalBg = #{interceptModalBg},</if>
                    <if test="interceptModalRecord != null">interceptModalRecord = #{interceptModalRecord},</if>
                    <if test="aliAdAdid != null">ali_ad_adid = #{aliAdAdid},</if>
                    <if test="aliAdRecordNum != null">ali_ad_record_num = #{aliAdRecordNum},</if>
                    <if test="aliAdSwitch != null">ali_ad_switch = #{aliAdSwitch},</if>
                    <if test="componentAppid != null">component_appid = #{componentAppid},</if>
                    <if test="componentAppname != null">component_appname = #{componentAppname},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayAppById" parameterType="String">
        delete from tb_shortplay_app where id = #{id}
    </delete>
    <delete id="deleteShortplayAppByTbId" parameterType="String">
        delete from tb_shortplay_app where tb_id = #{id}
    </delete>

    <delete id="deleteShortplayAppByIds" parameterType="String">
        delete from tb_shortplay_app where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
