<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.app.ShortplayWxAppMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.app.ShortplayWxAppEntity" id="ShortplayWxAppResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="mchId"    column="mch_id"    />
            <result property="offerId"    column="offer_id"    />
            <result property="appKey"    column="app_key"    />
            <result property="secret"    column="secret"    />
            <result property="key"    column="key"    />
            <result property="pfx"    column="pfx"    />
            <result property="apiV3Key"    column="api_v3_key"    />
            <result property="certSerialNo"    column="cert_serial_no"    />
            <result property="privateCertContent"    column="private_cert_content"    />
            <result property="privateKeyContent"    column="private_key_content"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayWxAppVo">
        select id, app_id, mch_id, offer_id, app_key, secret, `key`, pfx, api_v3_key, cert_serial_no, private_cert_content, private_key_content, gmt_create, gmt_modified from tb_shortplay_wx_app
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.app.ShortplayWxAppEntity" resultMap="ShortplayWxAppResult">
        <include refid="selectShortplayWxAppVo"/>
        <where>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="mchId != null  and mchId != ''"> and mch_id = #{mchId}</if>
            <if test="offerId != null  and offerId != ''"> and offer_id = #{offerId}</if>
            <if test="appKey != null  and appKey != ''"> and app_key = #{appKey}</if>
            <if test="secret != null  and secret != ''"> and secret = #{secret}</if>
            <if test="key != null  and key != ''"> and key = #{key}</if>
            <if test="pfx != null  and pfx != ''"> and pfx = #{pfx}</if>
        </where>
    </select>

    <select id="selectByAppId" parameterType="String" resultMap="ShortplayWxAppResult">
        <include refid="selectShortplayWxAppVo"/>
        where app_id = #{appId}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.app.ShortplayWxAppEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_wx_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="mchId != null and mchId != ''">mch_id,</if>
                    <if test="offerId != null and offerId != ''">offer_id,</if>
                    <if test="appKey != null and appKey != ''">app_key,</if>
                    <if test="secret != null and secret != ''">secret,</if>
                    <if test="key != null and key != ''">`key`,</if>
                    <if test="pfx != null">pfx,</if>
                    <if test="apiV3Key != null">api_v3_key,</if>
                    <if test="certSerialNo != null">cert_serial_no,</if>
                    <if test="privateCertContent != null">private_cert_content,</if>
                    <if test="privateKeyContent != null">private_key_content,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="mchId != null and mchId != ''">#{mchId},</if>
                    <if test="offerId != null and offerId != ''">#{offerId},</if>
                    <if test="appKey != null and appKey != ''">#{appKey},</if>
                    <if test="secret != null and secret != ''">#{secret},</if>
                    <if test="key != null and key != ''">#{key},</if>
                    <if test="pfx != null">#{pfx},</if>
                    <if test="apiV3Key != null">#{apiV3Key},</if>
                    <if test="certSerialNo != null">#{certSerialNo},</if>
                    <if test="privateCertContent != null">#{privateCertContent},</if>
                    <if test="privateKeyContent != null">#{privateKeyContent},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.app.ShortplayWxAppEntity">
        update tb_shortplay_wx_app
        <trim prefix="SET" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="mchId != null and mchId != ''">mch_id = #{mchId},</if>
                    <if test="offerId != null and offerId != ''">offer_id = #{offerId},</if>
                    <if test="appKey != null and appKey != ''">app_key = #{appKey},</if>
                    <if test="secret != null and secret != ''">secret = #{secret},</if>
                    <if test="key != null and key != ''">key = #{key},</if>
                    <if test="pfx != null">pfx = #{pfx},</if>
                    <if test="apiV3Key != null">api_v3_key = #{apiV3Key},</if>
                    <if test="certSerialNo != null">cert_serial_no = #{certSerialNo},</if>
                    <if test="privateCertContent != null">private_cert_content = #{privateCertContent},</if>
                    <if test="privateKeyContent != null">private_key_content = #{privateKeyContent},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
