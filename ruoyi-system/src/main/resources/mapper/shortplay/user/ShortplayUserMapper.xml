<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.user.ShortplayUserMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.user.ShortplayUserEntity" id="ShortplayUserResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="appName"    column="app_name"    />
            <result property="nickname"    column="nickname"    />
            <result property="avatar"    column="avatar"    />
            <result property="phone"    column="phone"    />
            <result property="middlemanId"    column="middleman_id"    />
            <result property="inviteCode"    column="invite_code"    />
            <result property="tfid"    column="tfid"    />
            <result property="myInviteCode"    column="my_invite_code"    />
            <result property="platform"    column="platform"    />
            <result property="wxOpenid"    column="wx_openid"    />
            <result property="wxUnionid"    column="wx_unionid"    />
            <result property="ttOpenid"    column="tt_openid"    />
            <result property="ttUnionid"    column="tt_unionid"    />
            <result property="aliOpenid"    column="ali_openid"    />
            <result property="osType"    column="os_type"    />
            <result property="status"    column="status"    />
            <result property="registerDate"    column="register_date"    />
            <result property="lastLoginTime"    column="last_login_time"    />
            <result property="lastLoginIp"    column="last_login_ip"    />
            <result property="tgPt"    column="tg_pt"    />
            <result property="rebackTime"    column="reback_time"    />
            <result property="clickid"    column="clickid"    />
            <result property="clueToken"    column="clue_token"    />
            <result property="reqId"    column="req_id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="promotionId"    column="promotion_id"    />
            <result property="pathParamStr"    column="path_param_str"    />
            <result property="reportStat"    column="report_stat"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayUserVo">
        select id, app_id, app_name, nickname, avatar, phone, middleman_id, invite_code, tfid, my_invite_code, platform, wx_openid, wx_unionid, tt_openid, tt_unionid, ali_openid,
               os_type, status, register_date,report_stat, last_login_time, last_login_ip, tg_pt, reback_time, clickid, clue_token, req_id, advertiser_id, promotion_id, path_param_str, gmt_create, gmt_modified
        from tb_shortplay_user
    </sql>

    <select id="selectById" parameterType="Long" resultMap="ShortplayUserResult">
        <include refid="selectShortplayUserVo"/>
        where id = #{id}
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.user.ShortplayUserEntity" resultMap="ShortplayUserResult">
        <include refid="selectShortplayUserVo"/>
        <where>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="middlemanId != null  and middlemanId != ''"> and middleman_id = #{middlemanId}</if>
            <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
            <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
            <if test="myInviteCode != null  and myInviteCode != ''"> and my_invite_code = #{myInviteCode}</if>
            <if test="platform != null "> and platform = #{platform}</if>
            <if test="wxOpenid != null  and wxOpenid != ''"> and wx_openid = #{wxOpenid}</if>
            <if test="wxUnionid != null  and wxUnionid != ''"> and wx_unionid = #{wxUnionid}</if>
            <if test="ttOpenid != null and ttOpenid != ''"> and tt_openid = #{ttOpenid}</if>
            <if test="ttUnionid != null and ttUnionid != ''"> and tt_unionid = #{ttUnionid}</if>
            <if test="aliOpenid != null and aliOpenid != ''"> and ali_openid = #{aliOpenid}</if>
            <if test="osType != null  and osType != ''"> and os_type = #{osType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="registerDate != null "> and register_date = #{registerDate}</if>
            <if test="lastLoginTime != null "> and last_login_time = #{lastLoginTime}</if>
            <if test="lastLoginIp != null  and lastLoginIp != ''"> and last_login_ip = #{lastLoginIp}</if>
            <if test="tgPt != null  and tgPt != ''"> and tg_pt = #{tgPt}</if>
            <if test="rebackTime != null "> and reback_time = #{rebackTime}</if>
            <if test="clickid != null "> and clickid = #{clickid}</if>
        </where>
    </select>

    <select id="selectByWxOpenid" parameterType="String" resultMap="ShortplayUserResult">
        <include refid="selectShortplayUserVo"/>
        where wx_openid = #{wxOpenid}
        limit 1
    </select>

    <select id="selectByTtOpenid" parameterType="String" resultMap="ShortplayUserResult">
        <include refid="selectShortplayUserVo"/>
        where tt_openid = #{ttOpenid}
        limit 1
    </select>

    <select id="selectByAliOpenid" parameterType="String" resultMap="ShortplayUserResult">
        <include refid="selectShortplayUserVo"/>
        where ali_openid = #{aliOpenid}
        limit 1
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.user.ShortplayUserEntity" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_shortplay_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="appName != null and appName != ''">app_name,</if>
                    <if test="nickname != null and nickname != ''">nickname,</if>
                    <if test="avatar != null and avatar != ''">avatar,</if>
                    <if test="phone != null and phone != ''">phone,</if>
                    <if test="middlemanId != null and middlemanId != ''">middleman_id,</if>
                    <if test="inviteCode != null and inviteCode != ''">invite_code,</if>
                    <if test="tfid != null and tfid != ''">tfid,</if>
                    <if test="myInviteCode != null and myInviteCode != ''">my_invite_code,</if>
                    <if test="platform != null">platform,</if>
                    <if test="wxOpenid != null and wxOpenid != ''">wx_openid,</if>
                    <if test="wxUnionid != null and wxUnionid != ''">wx_unionid,</if>
                    <if test="ttOpenid != null and ttOpenid != ''">tt_openid,</if>
                    <if test="ttUnionid != null and ttUnionid != ''">tt_unionid,</if>
                    <if test="aliOpenid != null and aliOpenid != ''">ali_openid,</if>
                    <if test="osType != null and osType != ''">os_type,</if>
                    <if test="status != null">status,</if>
                    <if test="registerDate != null">register_date,</if>
                    <if test="lastLoginTime != null">last_login_time,</if>
                    <if test="lastLoginIp != null and lastLoginIp != ''">last_login_ip,</if>
                    <if test="tgPt != null">tg_pt,</if>
                    <if test="rebackTime != null">reback_time,</if>
                    <if test="clickid != null">clickid,</if>
                    <if test="clueToken != null and clueToken != ''">clue_token,</if>
                    <if test="reqId != null and reqId != ''">req_id,</if>
                    <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
                    <if test="promotionId != null and promotionId != ''">promotion_id,</if>
                    <if test="pathParamStr != null and pathParamStr != ''">path_param_str,</if>
                    <if test="reportStat != null ">report_stat,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="appName != null and appName != ''">#{appName},</if>
                    <if test="nickname != null and nickname != ''">#{nickname},</if>
                    <if test="avatar != null and avatar != ''">#{avatar},</if>
                    <if test="phone != null and phone != ''">#{phone},</if>
                    <if test="middlemanId != null and middlemanId != ''">#{middlemanId},</if>
                    <if test="inviteCode != null and inviteCode != ''">#{inviteCode},</if>
                    <if test="tfid != null and tfid != ''">#{tfid},</if>
                    <if test="myInviteCode != null and myInviteCode != ''">#{myInviteCode},</if>
                    <if test="platform != null">#{platform},</if>
                    <if test="wxOpenid != null and wxOpenid != ''">#{wxOpenid},</if>
                    <if test="wxUnionid != null and wxUnionid != ''">#{wxUnionid},</if>
                    <if test="ttOpenid != null and ttOpenid != ''">#{ttOpenid},</if>
                    <if test="ttUnionid != null and ttUnionid != ''">#{ttUnionid},</if>
                    <if test="aliOpenid != null and aliOpenid != ''">#{aliOpenid},</if>
                    <if test="osType != null and osType != ''">#{osType},</if>
                    <if test="status != null">#{status},</if>
                    <if test="registerDate != null">#{registerDate},</if>
                    <if test="lastLoginTime != null">#{lastLoginTime},</if>
                    <if test="lastLoginIp != null and lastLoginIp != ''">#{lastLoginIp},</if>
                    <if test="tgPt != null">#{tgPt},</if>
                    <if test="rebackTime != null">#{rebackTime},</if>
                    <if test="clickid != null">#{clickid},</if>
                    <if test="clueToken != null and clueToken != ''">#{clueToken},</if>
                    <if test="reqId != null and reqId != ''">#{reqId},</if>
                    <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
                    <if test="promotionId != null and promotionId != ''">#{promotionId},</if>
                    <if test="pathParamStr != null and pathParamStr != ''">#{pathParamStr},</if>
                    <if test="reportStat != null ">#{reportStat},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.user.ShortplayUserEntity">
        update tb_shortplay_user
        <trim prefix="SET" suffixOverrides=",">
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="appName != null and appName != ''">app_name = #{appName},</if>
                    <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
                    <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
                    <if test="phone != null and phone != ''">phone = #{phone},</if>
                    <if test="middlemanId != null and middlemanId != ''">middleman_id = #{middlemanId},</if>
                    <if test="inviteCode != null and inviteCode != ''">invite_code = #{inviteCode},</if>
                    <if test="tfid != null and tfid != ''">tfid = #{tfid},</if>
                    <if test="myInviteCode != null and myInviteCode != ''">my_invite_code = #{myInviteCode},</if>
                    <if test="platform != null">platform = #{platform},</if>
                    <if test="wxOpenid != null and wxOpenid != ''">wx_openid = #{wxOpenid},</if>
                    <if test="wxUnionid != null and wxUnionid != ''">wx_unionid = #{wxUnionid},</if>
                    <if test="ttOpenid != null and ttOpenid != ''">tt_openid = #{ttOpenid},</if>
                    <if test="ttUnionid != null and ttUnionid != ''">tt_unionid = #{ttUnionid},</if>
                    <if test="aliOpenid != null and aliOpenid != ''">ali_openid = #{aliOpenid},</if>
                    <if test="osType != null and osType != ''">os_type = #{osType},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="registerDate != null">register_date = #{registerDate},</if>
                    <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
                    <if test="lastLoginIp != null and lastLoginIp != ''">last_login_ip = #{lastLoginIp},</if>
                    <if test="tgPt != null and tgPt != ''">tg_pt = #{tgPt},</if>
                    <if test="rebackTime != null">reback_time = #{rebackTime},</if>
                    <if test="clickid != null">clickid = #{clickid},</if>
                    <if test="clueToken != null and clueToken != ''">clue_token = #{clueToken},</if>
                    <if test="reqId != null and reqId != ''">req_id = #{reqId},</if>
                    <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
                    <if test="promotionId != null and promotionId != ''">promotion_id = #{promotionId},</if>
                    <if test="pathParamStr != null and pathParamStr != ''">path_param_str = #{pathParamStr},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateRebackTime">
        UPDATE
            tb_shortplay_user
        SET
            reback_time = now()
        WHERE
            id = #{userId}
            AND reback_time IS NULL
    </update>
</mapper>
