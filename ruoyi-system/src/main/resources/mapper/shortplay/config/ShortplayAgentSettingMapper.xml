<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.config.ShortplayAgentSettingMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.config.ShortplayAgentSettingEntity" id="ShortplayAgentSettingResult">
            <result property="id"    column="id"    />
            <result property="tbId"    column="tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="dataJson"    column="data_json"    />
            <result property="inviteCode"    column="invite_code"    />
            <result property="tfid"    column="tfid"    />
            <result property="tvId"    column="tv_id"    />
            <result property="tvName"    column="tv_name"    />
            <result property="agentPay"    column="agent_pay"    />
            <result property="agentSeries"    column="agent_series"    />
            <result property="AddTime"    column="_add_time"    />
            <result property="AddTimeStr"    column="_add_time_str"    />
            <result property="freeEpisode"    column="free_episode"    />
            <result property="payEpisodeType"    column="pay_episode_type"    />
    </resultMap>

    <sql id="selectShortplayAgentSettingVo">
        select id, tb_id, gmt_create, gmt_modified, data_json, free_episode,pay_episode_type,invite_code, tfid, tv_id, tv_name, agent_pay, agent_series, _add_time, _add_time_str from tb_shortplay_agent_setting
    </sql>

    <select id="selectByTfid" resultMap="ShortplayAgentSettingResult">
        <include refid="selectShortplayAgentSettingVo"/>
        where tfid = #{tfid}
        order by id desc
        limit 1
    </select>

    <select id="selectByTfidAndTvId" resultMap="ShortplayAgentSettingResult">
        <include refid="selectShortplayAgentSettingVo"/>
        where tfid = #{tfid} and tv_id = #{tvId}
        limit 1
    </select>

    <select id="selectShortplayAgentSettingList" parameterType="com.ruoyi.system.entity.shortplay.config.ShortplayAgentSettingEntity" resultMap="ShortplayAgentSettingResult">
        <include refid="selectShortplayAgentSettingVo"/>
        <where>
                        <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
                        <if test="dataJson != null  and dataJson != ''"> and data_json = #{dataJson}</if>
                        <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
                        <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                        <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
                        <if test="tvName != null  and tvName != ''"> and tv_name like concat('%', #{tvName}, '%')</if>
                        <if test="agentPay != null "> and agent_pay = #{agentPay}</if>
                        <if test="agentSeries != null "> and agent_series = #{agentSeries}</if>
                        <if test="AddTime != null "> and _add_time = #{AddTime}</if>
                        <if test="AddTimeStr != null  and AddTimeStr != ''"> and _add_time_str = #{AddTimeStr}</if>
        </where>
    </select>

    <select id="selectShortplayAgentSettingById" parameterType="String" resultMap="ShortplayAgentSettingResult">
            <include refid="selectShortplayAgentSettingVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplayAgentSetting" parameterType="com.ruoyi.system.entity.shortplay.config.ShortplayAgentSettingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_agent_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="dataJson != null">data_json,</if>
                    <if test="inviteCode != null">invite_code,</if>
                    <if test="tfid != null">tfid,</if>
                    <if test="tvId != null">tv_id,</if>
                    <if test="tvName != null">tv_name,</if>
                    <if test="agentPay != null">agent_pay,</if>
                    <if test="agentSeries != null">agent_series,</if>
                    <if test="AddTime != null">_add_time,</if>
                    <if test="payEpisodeType != null">pay_episode_type,</if>
                    <if test="freeEpisode != null">free_episode,</if>
                    <if test="AddTimeStr != null and AddTimeStr != ''">_add_time_str,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">#{tbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="dataJson != null">#{dataJson},</if>
                    <if test="inviteCode != null">#{inviteCode},</if>
                    <if test="tfid != null">#{tfid},</if>
                    <if test="tvId != null">#{tvId},</if>
                    <if test="tvName != null">#{tvName},</if>
                    <if test="agentPay != null">#{agentPay},</if>
                    <if test="agentSeries != null">#{agentSeries},</if>
                    <if test="AddTime != null">#{AddTime},</if>
                    <if test="payEpisodeType != null">#{payEpisodeType},</if>
                    <if test="freeEpisode != null">#{freeEpisode},</if>
                    <if test="AddTimeStr != null and AddTimeStr != ''">#{AddTimeStr},</if>
        </trim>
    </insert>

    <update id="updateShortplayAgentSetting" parameterType="com.ruoyi.system.entity.shortplay.config.ShortplayAgentSettingEntity">
        update tb_shortplay_agent_setting
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tbId != null">tb_id = #{tbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="dataJson != null">data_json = #{dataJson},</if>
                    <if test="inviteCode != null">invite_code = #{inviteCode},</if>
                    <if test="tfid != null">tfid = #{tfid},</if>
                    <if test="tvId != null">tv_id = #{tvId},</if>
                    <if test="tvName != null">tv_name = #{tvName},</if>
                    <if test="agentPay != null">agent_pay = #{agentPay},</if>
                    <if test="agentSeries != null">agent_series = #{agentSeries},</if>
                    <if test="AddTime != null">_add_time = #{AddTime},</if>
                    <if test="AddTimeStr != null and AddTimeStr != ''">_add_time_str = #{AddTimeStr},</if>
                    <if test="payEpisodeType != null">pay_episode_type= #{payEpisodeType},</if>
                    <if test="freeEpisode != null">free_episode= #{freeEpisode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayAgentSettingById" parameterType="String">
        delete from tb_shortplay_agent_setting where id = #{id}
    </delete>
    <delete id="deleteShortplayAgentSettingByTbId" parameterType="String">
        delete from tb_shortplay_agent_setting where tb_id = #{id}
    </delete>

    <delete id="deleteShortplayAgentSettingByIds" parameterType="String">
        delete from tb_shortplay_agent_setting where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
