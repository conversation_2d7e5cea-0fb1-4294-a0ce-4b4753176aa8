<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.config.ShortplayConfigMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.config.ShortplayConfigEntity" id="ShortplayConfigResult">
            <result property="id"    column="id"    />
            <result property="tbId"    column="tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="dataJson"    column="data_json"    />
            <result property="name"    column="name"    />
            <result property="tfid"    column="tfid"    />
            <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectShortplayConfigVo">
        select id, tb_id, gmt_create, gmt_modified, data_json, name, user_id,tfid from tb_shortplay_config
    </sql>

    <select id="selectShortplayConfigList" parameterType="com.ruoyi.system.entity.shortplay.config.ShortplayConfigEntity" resultMap="ShortplayConfigResult">
        <include refid="selectShortplayConfigVo"/>
        <where>
                        <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
                        <if test="dataJson != null  and dataJson != ''"> and data_json = #{dataJson}</if>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                        <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
        </where>
    </select>

    <select id="selectByUserIdAndName" resultMap="ShortplayConfigResult">
            <include refid="selectShortplayConfigVo"/>
            where user_id = #{userId} and `name` = #{name}
    </select>

    <select id="selectFixedCostConfigList" resultMap="ShortplayConfigResult">
        <include refid="selectShortplayConfigVo"/>
        WHERE JSON_EXTRACT(data_json, '$.fixed_cost') > 0
    </select>

    <insert id="insertShortplayConfig" parameterType="com.ruoyi.system.entity.shortplay.config.ShortplayConfigEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="dataJson != null">data_json,</if>
                    <if test="name != null">`name`,</if>
                    <if test="tfid != null">tfid,</if>
                    <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">#{tbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="dataJson != null">#{dataJson},</if>
                    <if test="name != null">#{name},</if>
                    <if test="tfid != null">#{tfid},</if>
                    <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateShortplayConfig" parameterType="com.ruoyi.system.entity.shortplay.config.ShortplayConfigEntity">
        update tb_shortplay_config
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tbId != null">tb_id = #{tbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="dataJson != null">data_json = #{dataJson},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="tfid != null">tfid = #{tfid},</if>
                    <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayConfigByTbId" parameterType="String">
        delete from tb_shortplay_config where tb_id = #{tbId}
    </delete>

    <delete id="deleteShortplayConfigByIds" parameterType="String">
        delete from tb_shortplay_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
