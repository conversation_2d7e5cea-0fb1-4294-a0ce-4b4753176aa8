<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.swiper.ShortplaySwiperMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.swiper.ShortplaySwiperEntity" id="ShortplaySwiperResult">
            <result property="id"    column="id"    />
            <result property="tbId"    column="tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="dataJson"    column="data_json"    />
            <result property="image"    column="image"    />
            <result property="title"    column="title"    />
            <result property="tvId"    column="tv_id"    />
            <result property="tvName"    column="tv_name"    />
            <result property="tvImage"    column="tv_image"    />
            <result property="appid"    column="appid"    />
            <result property="appname"    column="appname"    />
            <result property="AddTime"    column="_add_time"    />
            <result property="AddTimeStr"    column="_add_time_str"    />
            <result property="sort"    column="sort"    />
    </resultMap>

    <sql id="selectShortplaySwiperVo">
        select id, tb_id, gmt_create, gmt_modified, data_json, image, title, tv_id, tv_name, tv_image, appid, appname, _add_time, _add_time_str, sort from tb_shortplay_swiper
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.swiper.ShortplaySwiperEntity" resultMap="ShortplaySwiperResult">
        <include refid="selectShortplaySwiperVo"/>
        <where>
            <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
            <if test="dataJson != null  and dataJson != ''"> and data_json = #{dataJson}</if>
            <if test="image != null  and image != ''"> and image = #{image}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
            <if test="tvName != null  and tvName != ''"> and tv_name like concat('%', #{tvName}, '%')</if>
            <if test="tvImage != null  and tvImage != ''"> and tv_image = #{tvImage}</if>
            <if test="appid != null  and appid != ''"> and (appid like '[]' or appid like concat('%', #{appid}, '%'))</if>
            <if test="appname != null  and appname != ''"> and appname like concat('%', #{appname}, '%')</if>
            <if test="AddTime != null "> and _add_time = #{AddTime}</if>
            <if test="AddTimeStr != null  and AddTimeStr != ''"> and _add_time_str = #{AddTimeStr}</if>
            <if test="sort != null  and sort != ''"> and sort = #{sort}</if>
        </where>
        order by sort is null, _add_time desc
    </select>

    <select id="selectShortplaySwiperById" parameterType="String" resultMap="ShortplaySwiperResult">
            <include refid="selectShortplaySwiperVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplaySwiper" parameterType="com.ruoyi.system.entity.shortplay.swiper.ShortplaySwiperEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_swiper
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="dataJson != null">data_json,</if>
                    <if test="image != null">image,</if>
                    <if test="title != null">title,</if>
                    <if test="tvId != null">tv_id,</if>
                    <if test="tvName != null">tv_name,</if>
                    <if test="tvImage != null">tv_image,</if>
                    <if test="appid != null">appid,</if>
                    <if test="appname != null">appname,</if>
                    <if test="AddTime != null">_add_time,</if>
                    <if test="AddTimeStr != null">_add_time_str,</if>
                    <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">#{tbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="dataJson != null">#{dataJson},</if>
                    <if test="image != null">#{image},</if>
                    <if test="title != null">#{title},</if>
                    <if test="tvId != null">#{tvId},</if>
                    <if test="tvName != null">#{tvName},</if>
                    <if test="tvImage != null">#{tvImage},</if>
                    <if test="appid != null">#{appid},</if>
                    <if test="appname != null">#{appname},</if>
                    <if test="AddTime != null">#{AddTime},</if>
                    <if test="AddTimeStr != null">#{AddTimeStr},</if>
                    <if test="sort != null">#{sort},</if>
        </trim>
    </insert>

    <update id="updateShortplaySwiper" parameterType="com.ruoyi.system.entity.shortplay.swiper.ShortplaySwiperEntity">
        update tb_shortplay_swiper
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tbId != null">tb_id = #{tbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="dataJson != null">data_json = #{dataJson},</if>
                    <if test="image != null">image = #{image},</if>
                    <if test="title != null">title = #{title},</if>
                    <if test="tvId != null">tv_id = #{tvId},</if>
                    <if test="tvName != null">tv_name = #{tvName},</if>
                    <if test="tvImage != null">tv_image = #{tvImage},</if>
                    <if test="appid != null">appid = #{appid},</if>
                    <if test="appname != null">appname = #{appname},</if>
                    <if test="AddTime != null">_add_time = #{AddTime},</if>
                    <if test="AddTimeStr != null">_add_time_str = #{AddTimeStr},</if>
                    <if test="sort != null">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplaySwiperById" parameterType="String">
        delete from tb_shortplay_swiper where id = #{id}
    </delete>
    <delete id="deleteShortplaySwiperByTbId" parameterType="String">
        delete from tb_shortplay_swiper where tb_id = #{id}
    </delete>

    <delete id="deleteShortplaySwiperByIds" parameterType="String">
        delete from tb_shortplay_swiper where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
