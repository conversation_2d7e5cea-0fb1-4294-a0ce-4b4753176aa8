<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.record.ShortplayIaaConversionRecordMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.record.ShortplayIaaConversionRecord" id="TbDouyinIaaConversionRecordResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="openId"    column="open_id"    />
        <result property="clickId"    column="click_id"    />
        <result property="configId"    column="config_id"    />
        <result property="fixedCost"    column="fixed_cost"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="promotionId"    column="promotion_id"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="tfid"    column="tfid"    />
        <result property="registerTime"    column="register_time"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbDouyinIaaConversionRecordVo">
        select id, cur_date, open_id, click_id, config_id, fixed_cost, advertiser_id, promotion_id, invite_code, tfid, register_time, gmt_create, gmt_modified from tb_shortplay_iaa_conversion_record
    </sql>

    <select id="selectShortplayIaaConversionRecordList" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayIaaConversionRecord" resultMap="TbDouyinIaaConversionRecordResult">
        <include refid="selectTbDouyinIaaConversionRecordVo"/>
        <where>
            <if test="openId != null"> and open_id = #{openId}</if>
            <if test="clickId != null"> and click_id = #{clickId}</if>
        </where>
    </select>

    <select id="selectShortplayIaaConversionRecordById" parameterType="Long" resultMap="TbDouyinIaaConversionRecordResult">
        <include refid="selectTbDouyinIaaConversionRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectListByConfigId" resultMap="TbDouyinIaaConversionRecordResult">
        <include refid="selectTbDouyinIaaConversionRecordVo"/>
        where config_id = #{configId} and gmt_create &gt;= #{startTime} and gmt_create &lt;= #{endTime} and promotion_id = #{promotionId}
    </select>

    <insert id="insertShortplayIaaConversionRecord" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayIaaConversionRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_iaa_conversion_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="openId != null">open_id,</if>
            <if test="clickId != null">click_id,</if>
            <if test="configId != null">config_id,</if>
            <if test="fixedCost != null">fixed_cost,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="promotionId != null">promotion_id,</if>
            <if test="inviteCode != null">invite_code,</if>
            <if test="tfid != null">tfid,</if>
            <if test="registerTime != null">register_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="openId != null">#{openId},</if>
            <if test="clickId != null">#{clickId},</if>
            <if test="configId != null">#{configId},</if>
            <if test="fixedCost != null">#{fixedCost},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="promotionId != null">#{promotionId},</if>
            <if test="inviteCode != null">#{inviteCode},</if>
            <if test="tfid != null">#{tfid},</if>
            <if test="registerTime != null">#{registerTime},</if>
         </trim>
    </insert>

    <update id="updateShortplayIaaConversionRecord" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayIaaConversionRecord">
        update tb_shortplay_iaa_conversion_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="clickId != null">click_id = #{clickId},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="fixedCost != null">fixed_cost = #{fixedCost},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="promotionId != null">promotion_id = #{promotionId},</if>
            <if test="inviteCode != null">invite_code = #{inviteCode},</if>
            <if test="tfid != null">tfid = #{tfid},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="sumConvCostForFixedCost" resultType="java.math.BigDecimal">
        select ifnull(SUM(fixed_cost), 0) as fixed_cost
        from tb_shortplay_iaa_conversion_record
        where config_id = #{configId} and gmt_create &gt;= #{startTime} and gmt_create &lt;= #{endTime} and promotion_id = #{promotionId}
    </select>

</mapper>
