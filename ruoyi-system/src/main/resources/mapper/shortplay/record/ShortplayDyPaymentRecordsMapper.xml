<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.record.ShortplayDyPaymentRecordsMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.record.ShortplayDyPaymentRecordsEntity" id="ShortplayDyPaymentRecordsResult">
            <result property="id"    column="id"    />
            <result property="tbId"    column="tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="tvId"    column="tv_id"    />
            <result property="userId"    column="user_id"    />
            <result property="payStatus"    column="pay_status"    />
            <result property="subject"    column="subject"    />
    </resultMap>

    <sql id="selectShortplayDyPaymentRecordsVo">
        select id, tb_id, gmt_create, gmt_modified, tv_id, user_id, pay_status, subject from tb_shortplay_dy_payment_records
    </sql>

    <select id="selectShortplayDyPaymentRecordsList" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDyPaymentRecordsEntity" resultMap="ShortplayDyPaymentRecordsResult">
        <include refid="selectShortplayDyPaymentRecordsVo"/>
        <where>
                        <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
                        <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
                        <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                        <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
                        <if test="subject != null  and subject != ''"> and subject = #{subject}</if>
        </where>
    </select>

    <select id="selectShortplayDyPaymentRecordsById" parameterType="String" resultMap="ShortplayDyPaymentRecordsResult">
            <include refid="selectShortplayDyPaymentRecordsVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplayDyPaymentRecords" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDyPaymentRecordsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_dy_payment_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="tbId != null and tbId != ''">tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="tvId != null and tvId != ''">tv_id,</if>
                    <if test="userId != null and userId != ''">user_id,</if>
                    <if test="payStatus != null and payStatus != ''">pay_status,</if>
                    <if test="subject != null and subject != ''">subject,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="tbId != null and tbId != ''">#{tbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="tvId != null and tvId != ''">#{tvId},</if>
                    <if test="userId != null and userId != ''">#{userId},</if>
                    <if test="payStatus != null and payStatus != ''">#{payStatus},</if>
                    <if test="subject != null and subject != ''">#{subject},</if>
        </trim>
    </insert>

    <update id="updateShortplayDyPaymentRecords" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDyPaymentRecordsEntity">
        update tb_shortplay_dy_payment_records
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tbId != null and tbId != ''">tv_id = #{tbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="tvId != null and tvId != ''">tv_id = #{tvId},</if>
                    <if test="userId != null and userId != ''">user_id = #{userId},</if>
                    <if test="payStatus != null and payStatus != ''">pay_status = #{payStatus},</if>
                    <if test="subject != null and subject != ''">subject = #{subject},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayDyPaymentRecordsById" parameterType="String">
        delete from tb_shortplay_dy_payment_records where id = #{id}
    </delete>

    <delete id="deleteShortplayDyPaymentRecordsByIds" parameterType="String">
        delete from tb_shortplay_dy_payment_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteShortplayDyPaymentRecordsByTbId" parameterType="String">
        delete from tb_shortplay_dy_payment_records where tb_id = #{tbId}
    </delete>
</mapper>