<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.record.ShortplayDySyncRecordsMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.record.ShortplayDySyncRecordsEntity" id="ShortplayDySyncRecordsResult">
            <result property="id"    column="id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="userId"    column="user_id"    />
            <result property="syncStatus"    column="sync_status"    />
    </resultMap>

    <sql id="selectShortplayDySyncRecordsVo">
        select id, gmt_create, gmt_modified, user_id, sync_status from tb_shortplay_dy_sync_records
    </sql>

    <select id="selectShortplayDySyncRecordsList" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDySyncRecordsEntity" resultMap="ShortplayDySyncRecordsResult">
        <include refid="selectShortplayDySyncRecordsVo"/>
        <where>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="syncStatus != null "> and sync_status = #{syncStatus}</if>
        </where>
    </select>

    <select id="selectShortplayDySyncRecordsById" parameterType="Long" resultMap="ShortplayDySyncRecordsResult">
            <include refid="selectShortplayDySyncRecordsVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplayDySyncRecords" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDySyncRecordsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_dy_sync_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="syncStatus != null">sync_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="syncStatus != null">#{syncStatus},</if>
        </trim>
    </insert>

    <update id="updateShortplayDySyncRecords" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDySyncRecordsEntity">
        update tb_shortplay_dy_sync_records
        <trim prefix="SET" suffixOverrides=",">
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="syncStatus != null">sync_status = #{syncStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayDySyncRecordsById" parameterType="Long">
        delete from tb_shortplay_dy_sync_records where id = #{id}
    </delete>

    <delete id="deleteShortplayDySyncRecordsByIds" parameterType="String">
        delete from tb_shortplay_dy_sync_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>