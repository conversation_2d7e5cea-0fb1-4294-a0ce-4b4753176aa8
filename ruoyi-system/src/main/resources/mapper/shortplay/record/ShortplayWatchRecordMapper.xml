<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.record.ShortplayWatchRecordMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.record.ShortplayWatchRecordEntity" id="ShortplayWatchRecordResult">
            <result property="id"    column="id"    />
            <result property="userId"    column="user_id"    />
            <result property="tvId"    column="tv_id"    />
            <result property="series"    column="series"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="appId"    column="app_id"    />
    </resultMap>

    <sql id="selectShortplayWatchRecordVo">
        select id, user_id, tv_id, series, gmt_create, gmt_modified from tb_shortplay_watch_record
    </sql>

    <select id="selectNewestSeries" resultType="Integer">
        select series
        from tb_shortplay_watch_record
        where user_id = #{userId} and tv_id = #{tvId}
        order by id desc limit 1
    </select>

    <select id="selectAllNewestSeries" resultMap="ShortplayWatchRecordResult">
        SELECT yt.*
        FROM tb_shortplay_watch_record yt
                 INNER JOIN (
            SELECT tv_id, MAX(id) AS max_id
            FROM tb_shortplay_watch_record
            WHERE user_id = #{userId}
            GROUP BY tv_id
        ) grouped_yt ON yt.tv_id = grouped_yt.tv_id AND yt.id = grouped_yt.max_id;
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayWatchRecordEntity" resultMap="ShortplayWatchRecordResult">
        <include refid="selectShortplayWatchRecordVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
            <if test="series != null "> and series = #{series}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayWatchRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_watch_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,</if>
                    <if test="tvId != null and tvId != ''">tv_id,</if>
                    <if test="series != null">series,</if>
                    <if test="appId != null and appId !=''">app_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},</if>
                    <if test="tvId != null and tvId != ''">#{tvId},</if>
                    <if test="series != null">#{series},</if>
                    <if test="appId != null and appId !=''">#{appId},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayWatchRecordEntity">
        update tb_shortplay_watch_record
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="tvId != null and tvId != ''">tv_id = #{tvId},</if>
                    <if test="series != null">series = #{series},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
