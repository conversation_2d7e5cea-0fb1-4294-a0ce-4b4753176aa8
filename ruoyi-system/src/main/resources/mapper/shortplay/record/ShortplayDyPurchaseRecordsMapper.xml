<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.record.ShortplayDyPurchaseRecordsMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.record.ShortplayDyPurchaseRecordsEntity" id="ShortplayDyPurchaseRecordsResult">
        <result property="id"    column="id"    />
        <result property="tbId"    column="tb_id"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
        <result property="AddTime"    column="_add_time"    />
        <result property="AddTimeStr"    column="_add_time_str"    />
        <result property="appid"    column="appid"    />
        <result property="appname"    column="appname"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="middlemanId"    column="middleman_id"    />
        <result property="nickname"    column="nickname"    />
        <result property="osName"    column="os_name"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="series"    column="series"    />
        <result property="seriesId"    column="series_id"    />
        <result property="tfid"    column="tfid"    />
        <result property="tvId"    column="tv_id"    />
        <result property="tvName"    column="tv_name"    />
        <result property="userId"    column="user_id"    />
        <result property="ttOpenid"    column="tt_openid"    />
    </resultMap>

    <sql id="selectShortplayDyPurchaseRecordsVo">
        select id, tb_id, gmt_create, gmt_modified, _add_time, _add_time_str, appid, appname, invite_code, middleman_id, nickname, os_name, pay_amount, series, series_id, tfid, tv_id, tv_name, user_id, tt_openid from tb_shortplay_dy_purchase_records
    </sql>

    <select id="selectShortplayDyPurchaseRecordsList" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDyPurchaseRecordsEntity" resultMap="ShortplayDyPurchaseRecordsResult">
        <include refid="selectShortplayDyPurchaseRecordsVo"/>
        <where>
            <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
            <if test="AddTime != null "> and _add_time = #{AddTime}</if>
            <if test="AddTimeStr != null "> and _add_time_str = #{AddTimeStr}</if>
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="appname != null  and appname != ''"> and appname like concat('%', #{appname}, '%')</if>
            <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
            <if test="middlemanId != null  and middlemanId != ''"> and middleman_id = #{middlemanId}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="osName != null  and osName != ''"> and os_name like concat('%', #{osName}, '%')</if>
            <if test="payAmount != null "> and pay_amount = #{payAmount}</if>
            <if test="series != null "> and series = #{series}</if>
            <if test="seriesId != null  and seriesId != ''"> and series_id = #{seriesId}</if>
            <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
            <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
            <if test="tvName != null  and tvName != ''"> and tv_name like concat('%', #{tvName}, '%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="ttOpenid != null  and ttOpenid != ''"> and tt_openid = #{ttOpenid}</if>
        </where>
    </select>

    <select id="selectShortplayDyPurchaseRecordsById" parameterType="String" resultMap="ShortplayDyPurchaseRecordsResult">
        <include refid="selectShortplayDyPurchaseRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortplayDyPurchaseRecords" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDyPurchaseRecordsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_dy_purchase_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tbId != null and tbId != ''">tb_id,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="AddTime != null">_add_time,</if>
            <if test="AddTimeStr != null">_add_time_str,</if>
            <if test="appid != null and appid != ''">appid,</if>
            <if test="appname != null and appname != ''">appname,</if>
            <if test="inviteCode != null">invite_code,</if>
            <if test="middlemanId != null">middleman_id,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="osName != null and osName != ''">os_name,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="series != null">series,</if>
            <if test="seriesId != null and seriesId != ''">series_id,</if>
            <if test="tfid != null and tfid != ''">tfid,</if>
            <if test="tvId != null and tvId != ''">tv_id,</if>
            <if test="tvName != null and tvName != ''">tv_name,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="ttOpenid != null and ttOpenid != ''">tt_openid,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tbId != null and tbId != ''">#{tbId},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
            <if test="AddTime != null">#{AddTime},</if>
            <if test="AddTimeStr != null">#{AddTimeStr},</if>
            <if test="appid != null and appid != ''">#{appid},</if>
            <if test="appname != null and appname != ''">#{appname},</if>
            <if test="inviteCode != null">#{inviteCode},</if>
            <if test="middlemanId != null">#{middlemanId},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="osName != null and osName != ''">#{osName},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="series != null">#{series},</if>
            <if test="seriesId != null and seriesId != ''">#{seriesId},</if>
            <if test="tfid != null and tfid != ''">#{tfid},</if>
            <if test="tvId != null and tvId != ''">#{tvId},</if>
            <if test="tvName != null and tvName != ''">#{tvName},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="ttOpenid != null and ttOpenid != ''">#{ttOpenid},</if>
        </trim>
    </insert>

    <update id="updateShortplayDyPurchaseRecords" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayDyPurchaseRecordsEntity">
        update tb_shortplay_dy_purchase_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="tbId != null and tbId != ''">tb_id = #{tbId},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
            <if test="AddTime != null">_add_time = #{AddTime},</if>
            <if test="AddTimeStr != null">_add_time_str = #{AddTimeStr},</if>
            <if test="appid != null and appid != ''">appid = #{appid},</if>
            <if test="appname != null and appname != ''">appname = #{appname},</if>
            <if test="inviteCode != null">invite_code = #{inviteCode},</if>
            <if test="middlemanId != null">middleman_id = #{middlemanId},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="osName != null and osName != ''">os_name = #{osName},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="series != null">series = #{series},</if>
            <if test="seriesId != null and seriesId != ''">series_id = #{seriesId},</if>
            <if test="tfid != null and tfid != ''">tfid = #{tfid},</if>
            <if test="tvId != null and tvId != ''">tv_id = #{tvId},</if>
            <if test="tvName != null and tvName != ''">tv_name = #{tvName},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="ttOpenid != null and ttOpenid != ''">tt_openid = #{ttOpenid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayDyPurchaseRecordsById" parameterType="String">
        delete from tb_shortplay_dy_purchase_records where id = #{id}
    </delete>

    <delete id="deleteShortplayDyPurchaseRecordsByIds" parameterType="String">
        delete from tb_shortplay_dy_purchase_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteShortplayDyPurchaseRecordsByTbId" parameterType="String">
        delete from tb_shortplay_dy_purchase_records where tb_id = #{tbId}
    </delete>
</mapper>