<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.record.ShortplayTvUnlockRecordMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.record.ShortplayTvUnlockRecordEntity" id="ShortplayTvUnlockRecordResult">
            <result property="id"    column="id"    />
            <result property="userId"    column="user_id"    />
            <result property="tvId"    column="tv_id"    />
            <result property="tvTbId"    column="tv_tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayTvUnlockRecordVo">
        select id, user_id, tv_id, tv_tb_id, gmt_create, gmt_modified from tb_shortplay_tv_unlock_record
    </sql>

    <select id="isExistByUserIdAndTvId" resultType="Integer">
        select 1
        from tb_shortplay_tv_unlock_record
        where user_id = #{userId} and tv_id = #{tvId}
        limit 1
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayTvUnlockRecordEntity" resultMap="ShortplayTvUnlockRecordResult">
        <include refid="selectShortplayTvUnlockRecordVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="tvId != null "> and tv_id = #{tvId}</if>
            <if test="tvTbId != null  and tvTbId != ''"> and tv_tb_id = #{tvTbId}</if>
        </where>
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayTvUnlockRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_shortplay_tv_unlock_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,</if>
                    <if test="tvId != null">tv_id,</if>
                    <if test="tvTbId != null and tvTbId != ''">tv_tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},</if>
                    <if test="tvId != null">#{tvId},</if>
                    <if test="tvTbId != null and tvTbId != ''">#{tvTbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayTvUnlockRecordEntity">
        update tb_shortplay_tv_unlock_record
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="tvId != null">tv_id = #{tvId},</if>
                    <if test="tvTbId != null and tvTbId != ''">tv_tb_id = #{tvTbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
