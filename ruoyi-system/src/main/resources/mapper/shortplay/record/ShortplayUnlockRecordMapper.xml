<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.record.ShortplayUnlockRecordMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity" id="ShortplayUnlockRecordResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="userId"    column="user_id"    />
            <result property="tvId"    column="tv_id"    />
            <result property="series"    column="series"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayUnlockRecordVo">
        select id, app_id, user_id, tv_id, series, gmt_create, gmt_modified from tb_shortplay_unlock_record
    </sql>
    <delete id="truncateTable">
        TRUNCATE TABLE `tb_shortplay_unlock_record`;
    </delete>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity" resultMap="ShortplayUnlockRecordResult">
        <include refid="selectShortplayUnlockRecordVo"/>
        <where>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
            <if test="series != null "> and series = #{series}</if>
        </where>
    </select>

    <select id="selectSeriesByUserIdAndTvId" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity" resultType="Integer">
        select series
        from tb_shortplay_unlock_record
        where user_id = #{userId} and tv_id = #{tvId} and gmt_modified &gt;= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
        order by series
    </select>

    <select id="countByUserIdAndDate" resultType="Integer">
        select count(1)
        from tb_shortplay_unlock_record
        where user_id = #{userId} and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_unlock_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="appId != null">app_id,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="tvId != null">tv_id,</if>
                    <if test="series != null">series,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="appId != null">#{appId},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="tvId != null">#{tvId},</if>
                    <if test="series != null">#{series},</if>
        </trim>
        on duplicate key update
        gmt_modified = now()
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into tb_shortplay_unlock_record (app_id, user_id, tv_id, series)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.appId}, #{record.userId}, #{record.tvId}, #{record.series})
        </foreach>
        on duplicate key update
        gmt_modified = now()
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity">
        update tb_shortplay_unlock_record
        <trim prefix="SET" suffixOverrides=",">
                    <if test="appId != null">app_id = #{appId},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="tvId != null and tvId != ''">tv_id = #{tvId},</if>
                    <if test="series != null">series = #{series},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
