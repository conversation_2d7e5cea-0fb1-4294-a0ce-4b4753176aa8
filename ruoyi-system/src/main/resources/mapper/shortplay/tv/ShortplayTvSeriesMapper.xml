<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.tv.ShortplayTvSeriesMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.tv.ShortplayTvSeriesEntity" id="ShortplayTvSeriesResult">
            <result property="id"    column="id"    />
            <result property="tbId"    column="tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="AddTime"    column="_add_time"    />
            <result property="AddTimeStr"    column="_add_time_str"    />
            <result property="appid"    column="appid"    />
            <result property="appname"    column="appname"    />
            <result property="isRecommend"    column="is_recommend"    />
            <result property="likeNum"    column="like_num"    />
            <result property="mediaId"    column="media_id"    />
            <result property="originalVideoSrc"    column="original_video_src"    />
            <result property="pay"    column="pay"    />
            <result property="series"    column="series"    />
            <result property="title"    column="title"    />
            <result property="tvId"    column="tv_id"    />
            <result property="tvImage"    column="tv_image"    />
            <result property="tvName"    column="tv_name"    />
            <result property="ttSeriesId"    column="tt_series_id"    />
            <result property="ttVideoId"    column="tt_video_id"    />
            <result property="videoSrc"    column="video_src"    />
    </resultMap>

    <sql id="selectShortplayTvSeriesVo">
        select id, tb_id, gmt_create, gmt_modified, _add_time, _add_time_str, appid, appname, is_recommend, like_num, media_id, original_video_src, pay, series, title, tv_id, tv_image, tv_name, tt_series_id, tt_video_id, video_src from tb_shortplay_tv_series
    </sql>

    <select id="selectShortplayTvSeriesList" parameterType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvSeriesEntity" resultMap="ShortplayTvSeriesResult">
        <include refid="selectShortplayTvSeriesVo"/>
        <where>
                        <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
                        <if test="AddTime != null "> and _add_time = #{AddTime}</if>
                        <if test="AddTimeStr != null  and AddTimeStr != ''"> and _add_time_str = #{AddTimeStr}</if>
                        <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                        <if test="appname != null  and appname != ''"> and appname like concat('%', #{appname}, '%')</if>
                        <if test="isRecommend != null "> and is_recommend = #{isRecommend}</if>
                        <if test="likeNum != null "> and like_num = #{likeNum}</if>
                        <if test="mediaId != null "> and media_id = #{mediaId}</if>
                        <if test="originalVideoSrc != null  and originalVideoSrc != ''"> and original_video_src = #{originalVideoSrc}</if>
                        <if test="pay != null "> and pay = #{pay}</if>
                        <if test="series != null "> and series = #{series}</if>
                        <if test="title != null  and title != ''"> and title = #{title}</if>
                        <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
                        <if test="tvImage != null  and tvImage != ''"> and tv_image = #{tvImage}</if>
                        <if test="tvName != null  and tvName != ''"> and tv_name like concat('%', #{tvName}, '%')</if>
                        <if test="ttSeriesId != null  and ttSeriesId != ''"> and tt_series_id = #{ttSeriesId}</if>
                        <if test="ttVideoId != null  and ttVideoId != ''"> and tt_video_id = #{ttVideoId}</if>
                        <if test="videoSrc != null  and videoSrc != ''"> and video_src = #{videoSrc}</if>
        </where>
    </select>

    <select id="selectShortplayTvSeriesById" parameterType="String" resultMap="ShortplayTvSeriesResult">
            <include refid="selectShortplayTvSeriesVo"/>
            where id = #{id}
    </select>

    <select id="selectByTtSeriesId" parameterType="String" resultMap="ShortplayTvSeriesResult">
        <include refid="selectShortplayTvSeriesVo"/>
        where tt_series_id = #{ttSeriesId}
        limit 1
    </select>

    <select id="selectByTvIdAndSeries" resultMap="ShortplayTvSeriesResult">
        <include refid="selectShortplayTvSeriesVo"/>
        where tv_id = #{tvId} and series = #{series}
        limit 1
    </select>

    <insert id="insertShortplayTvSeries" parameterType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvSeriesEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_tv_series
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="AddTime != null">_add_time,</if>
                    <if test="AddTimeStr != null">_add_time_str,</if>
                    <if test="appid != null">appid,</if>
                    <if test="appname != null">appname,</if>
                    <if test="isRecommend != null">is_recommend,</if>
                    <if test="likeNum != null">like_num,</if>
                    <if test="mediaId != null">media_id,</if>
                    <if test="originalVideoSrc != null">original_video_src,</if>
                    <if test="pay != null">pay,</if>
                    <if test="series != null">series,</if>
                    <if test="title != null">title,</if>
                    <if test="tvId != null">tv_id,</if>
                    <if test="tvImage != null">tv_image,</if>
                    <if test="tvName != null">tv_name,</if>
                    <if test="ttSeriesId != null">tt_series_id,</if>
                    <if test="ttVideoId != null">tt_video_id,</if>
                    <if test="videoSrc != null">video_src,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">#{tbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="AddTime != null">#{AddTime},</if>
                    <if test="AddTimeStr != null">#{AddTimeStr},</if>
                    <if test="appid != null">#{appid},</if>
                    <if test="appname != null">#{appname},</if>
                    <if test="isRecommend != null">#{isRecommend},</if>
                    <if test="likeNum != null">#{likeNum},</if>
                    <if test="mediaId != null">#{mediaId},</if>
                    <if test="originalVideoSrc != null">#{originalVideoSrc},</if>
                    <if test="pay != null">#{pay},</if>
                    <if test="series != null">#{series},</if>
                    <if test="title != null">#{title},</if>
                    <if test="tvId != null">#{tvId},</if>
                    <if test="tvImage != null">#{tvImage},</if>
                    <if test="tvName != null">#{tvName},</if>
                    <if test="ttSeriesId != null">#{ttSeriesId},</if>
                    <if test="ttVideoId != null">#{ttVideoId},</if>
                    <if test="videoSrc != null">#{videoSrc},</if>
        </trim>
    </insert>

    <update id="updateShortplayTvSeries" parameterType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvSeriesEntity">
        update tb_shortplay_tv_series
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tbId != null">tb_id = #{tbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="AddTime != null">_add_time = #{AddTime},</if>
                    <if test="AddTimeStr != null">_add_time_str = #{AddTimeStr},</if>
                    <if test="appid != null">appid = #{appid},</if>
                    <if test="appname != null">appname = #{appname},</if>
                    <if test="isRecommend != null">is_recommend = #{isRecommend},</if>
                    <if test="likeNum != null">like_num = #{likeNum},</if>
                    <if test="mediaId != null">media_id = #{mediaId},</if>
                    <if test="originalVideoSrc != null">original_video_src = #{originalVideoSrc},</if>
                    <if test="pay != null">pay = #{pay},</if>
                    <if test="series != null">series = #{series},</if>
                    <if test="title != null">title = #{title},</if>
                    <if test="tvId != null">tv_id = #{tvId},</if>
                    <if test="tvImage != null">tv_image = #{tvImage},</if>
                    <if test="tvName != null">tv_name = #{tvName},</if>
                    <if test="ttSeriesId != null">tt_series_id = #{ttSeriesId},</if>
                    <if test="ttVideoId != null">tt_video_id = #{ttVideoId},</if>
                    <if test="videoSrc != null">video_src = #{videoSrc},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayTvSeriesByTbId" parameterType="String">
        delete from tb_shortplay_tv_series where tb_id = #{tbId}
    </delete>

    <delete id="deleteShortplayTvSeriesByIds" parameterType="String">
        delete from tb_shortplay_tv_series where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByTtVideoId" parameterType="String" resultMap="ShortplayTvSeriesResult">
        <include refid="selectShortplayTvSeriesVo"/>
        where tt_video_id = #{ttVideoId}
        limit 1
    </select>

</mapper>
