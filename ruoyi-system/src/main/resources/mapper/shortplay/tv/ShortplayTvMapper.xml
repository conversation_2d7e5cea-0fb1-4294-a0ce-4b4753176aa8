<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.tv.ShortplayTvMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity" id="ShortplayTvResult">
        <result property="id" column="id"/>
        <result property="tbId" column="tb_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="dataJson" column="data_json"/>
        <result property="AddTime" column="_add_time"/>
        <result property="AddTimeStr" column="_add_time_str"/>
        <result property="allLike" column="all_like"/>
        <result property="allSeries" column="all_series"/>
        <result property="allowAds" column="allow_ads"/>
        <result property="appid" column="appid"/>
        <result property="appname" column="appname"/>
        <result property="baseurl" column="baseurl"/>
        <result property="cate" column="cate"/>
        <result property="desc" column="desc"/>
        <result property="gzhboxEnd" column="gzhbox_end"/>
        <result property="gzhboxStart" column="gzhbox_start"/>
        <result property="howMuch" column="how_much"/>
        <result property="indexBox" column="index_box"/>
        <result property="indexCate" column="index_cate"/>
        <result property="isEnd" column="is_end"/>
        <result property="querykey" column="querykey"/>
        <result property="rank" column="rank"/>
        <result property="sort" column="sort"/>
        <result property="startNeedpay" column="start_needpay"/>
        <result property="status" column="status"/>
        <result property="tagColor" column="tag_color"/>
        <result property="tagText" column="tag_text"/>
        <result property="tvClass" column="tv_class"/>
        <result property="tvImage" column="tv_image"/>
        <result property="tvName" column="tv_name"/>
        <result property="updateWitch" column="update_witch"/>
        <result property="appplatform" column="appplatform"/>
        <result property="ttAuthAppid" column="tt_auth_appid"/>
        <result property="wxAuthAppid" column="wx_auth_appid"/>
        <result property="wxVideoId" column="wx_video_id"/>
        <result property="ttVideoId" column="tt_video_id"/>
        <result property="dataSources" column="data_sources"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <sql id="selectShortplayTvVo">
        select id, tb_id, gmt_create, gmt_modified, data_json, _add_time, _add_time_str, all_like, all_series,
        allow_ads, appid, appname, baseurl, cate, `desc`, gzhbox_end, gzhbox_start, how_much, index_box, index_cate,
        is_end, querykey, `rank`, `sort`, start_needpay, status, tag_color, tag_text, tv_class, tv_image, tv_name,
        update_witch, appplatform, tt_auth_appid, wx_auth_appid, wx_video_id,tt_video_id,data_sources,is_deleted from
        tb_shortplay_tv
    </sql>

    <select id="selectById" parameterType="Long" resultMap="ShortplayTvResult">
        <include refid="selectShortplayTvVo"/>
        where id = #{id}
        limit 1
    </select>

    <select id="selectByIds" resultMap="ShortplayTvResult">
        <include refid="selectShortplayTvVo"/>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByTbId" parameterType="String" resultMap="ShortplayTvResult">
        <include refid="selectShortplayTvVo"/>
        where tb_id = #{tvId}
        limit 1
    </select>

    <select id="selectByWxVideoId" parameterType="String" resultMap="ShortplayTvResult">
        <include refid="selectShortplayTvVo"/>
        where wx_video_id = #{wxVideoId} and is_deleted = 0
        limit 1
    </select>

    <select id="selectByTtVideoId" parameterType="String" resultMap="ShortplayTvResult">
        <include refid="selectShortplayTvVo"/>
        where tt_video_id = #{ttVideoId} and is_deleted = 0
        and appplatform like '%tt%'
        limit 1
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity"
            resultMap="ShortplayTvResult">
        <include refid="selectShortplayTvVo"/>
        <where>
            <if test="tbId != null  and tbId != ''">and tb_id = #{tbId}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
            <if test="dataJson != null  and dataJson != ''">and data_json = #{dataJson}</if>
            <if test="AddTime != null ">and _add_time = #{AddTime}</if>
            <if test="AddTimeStr != null  and AddTimeStr != ''">and _add_time_str = #{AddTimeStr}</if>
            <if test="allLike != null ">and all_like = #{allLike}</if>
            <if test="allSeries != null ">and all_series = #{allSeries}</if>
            <if test="allowAds != null ">and allow_ads = #{allowAds}</if>
            <if test="appid != null  and appid != ''">and (appid like '[]' or appid like concat('%', #{appid}, '%'))
            </if>
            <if test="appname != null  and appname != ''">and appname like concat('%', #{appname}, '%')</if>
            <if test="baseurl != null  and baseurl != ''">and baseurl = #{baseurl}</if>
            <if test="cate != null  and cate != ''">and cate = #{cate}</if>
            <if test="desc != null  and desc != ''">and `desc` = #{desc}</if>
            <if test="gzhboxEnd != null ">and gzhbox_end = #{gzhboxEnd}</if>
            <if test="gzhboxStart != null ">and gzhbox_start = #{gzhboxStart}</if>
            <if test="howMuch != null ">and how_much = #{howMuch}</if>
            <if test="indexBox != null ">and index_box = #{indexBox}</if>
            <if test="indexCate != null  and indexCate != ''">and index_cate = #{indexCate}</if>
            <if test="isEnd != null ">and is_end = #{isEnd}</if>
            <if test="querykey != null  and querykey != ''">and querykey = #{querykey}</if>
            <if test="rank != null ">and `rank` = #{rank}</if>
            <if test="sort != null ">and `sort` = #{sort}</if>
            <if test="startNeedpay != null ">and start_needpay = #{startNeedpay}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="tagColor != null  and tagColor != ''">and tag_color = #{tagColor}</if>
            <if test="tagText != null  and tagText != ''">and tag_text = #{tagText}</if>
            <if test="tvClass != null  and tvClass != ''">and tv_class = #{tvClass}</if>
            <if test="tvImage != null  and tvImage != ''">and tv_image = #{tvImage}</if>
            <if test="tvName != null  and tvName != ''">and tv_name like concat('%', #{tvName}, '%')</if>
            <if test="updateWitch != null ">and update_witch = #{updateWitch}</if>
            <if test="appplatform != null  and appplatform != ''">and appplatform like concat('%', #{appplatform},
                '%')
            </if>
            <if test="ttAuthAppid != null  and ttAuthAppid != ''">and tt_auth_appid = #{ttAuthAppid}</if>
            <if test="wxAuthAppid != null  and wxAuthAppid != ''">and wx_auth_appid = #{wxAuthAppid}</if>
            <if test="wxVideoId != null  and wxVideoId != ''">and wx_video_id = #{wxVideoId}</if>
            <if test="ttVideoId != null  and ttVideoId != ''">and tt_video_id = #{ttVideoId}</if>
            <if test="dataSources != null  and dataSources != ''">and data_sources = #{dataSources}</if>
            <if test="isDeleted != null ">and is_deleted = #{isDeleted}</if>
            <if test="bannedTbIds != null and bannedTbIds.size() > 0">
                and tb_id not in
                <foreach item="bannedTbId" collection="bannedTbIds" open="(" separator="," close=")">
                    #{bannedTbId}
                </foreach>
            </if>
            <if test="isCsj != null and isCsj != 0">and data_sources = 'csj'</if>
            <if test="isCsj != null and isCsj == 0">and (data_sources is null or data_sources != 'csj')</if>
        </where>
        order by sort, _add_time desc,id desc
    </select>

    <select id="selectShortplayTvById" parameterType="String" resultMap="ShortplayTvResult">
        <include refid="selectShortplayTvVo"/>
        where id = #{id}
    </select>

    <select id="selectMapByTvIds" resultType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity">
        select tb_id, tv_name from tb_shortplay_tv
        where tb_id in
        <foreach item="tvId" collection="tvIds" open="(" separator="," close=")">
            #{tvId}
        </foreach>
    </select>

    <select id="selectByTvName" resultType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity">
        select tb_id, tv_name from tb_shortplay_tv where tv_name like concat('%', #{tvName}, '%')
    </select>

    <insert id="insertShortplayTv" parameterType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_tv
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tbId != null">tb_id,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="dataJson != null">data_json,</if>
            <if test="AddTime != null">_add_time,</if>
            <if test="AddTimeStr != null">_add_time_str,</if>
            <if test="allLike != null">all_like,</if>
            <if test="allSeries != null">all_series,</if>
            <if test="allowAds != null">allow_ads,</if>
            <if test="appid != null">appid,</if>
            <if test="appname != null">appname,</if>
            <if test="baseurl != null">baseurl,</if>
            <if test="cate != null">cate,</if>
            <if test="desc != null">`desc`,</if>
            <if test="gzhboxEnd != null">gzhbox_end,</if>
            <if test="gzhboxStart != null">gzhbox_start,</if>
            <if test="howMuch != null">how_much,</if>
            <if test="indexBox != null">index_box,</if>
            <if test="indexCate != null">index_cate,</if>
            <if test="isEnd != null">is_end,</if>
            <if test="querykey != null">querykey,</if>
            <if test="rank != null">`rank`,</if>
            <if test="sort != null">`sort`,</if>
            <if test="startNeedpay != null">start_needpay,</if>
            <if test="status != null">`status`,</if>
            <if test="tagColor != null">tag_color,</if>
            <if test="tagText != null">tag_text,</if>
            <if test="tvClass != null">tv_class,</if>
            <if test="tvImage != null">tv_image,</if>
            <if test="tvName != null">tv_name,</if>
            <if test="updateWitch != null">update_witch,</if>
            <if test="appplatform != null">appplatform,</if>
            <if test="ttAuthAppid != null">tt_auth_appid,</if>
            <if test="wxAuthAppid != null">wx_auth_appid,</if>
            <if test="wxVideoId != null">wx_video_id,</if>
            <if test="ttVideoId != null">tt_video_id,</if>
            <if test="dataSources != null">data_sources,</if>
            <if test="isDeleted != null">is_deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tbId != null">#{tbId},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
            <if test="dataJson != null">#{dataJson},</if>
            <if test="AddTime != null">#{AddTime},</if>
            <if test="AddTimeStr != null">#{AddTimeStr},</if>
            <if test="allLike != null">#{allLike},</if>
            <if test="allSeries != null">#{allSeries},</if>
            <if test="allowAds != null">#{allowAds},</if>
            <if test="appid != null">#{appid},</if>
            <if test="appname != null">#{appname},</if>
            <if test="baseurl != null">#{baseurl},</if>
            <if test="cate != null">#{cate},</if>
            <if test="desc != null">#{desc},</if>
            <if test="gzhboxEnd != null">#{gzhboxEnd},</if>
            <if test="gzhboxStart != null">#{gzhboxStart},</if>
            <if test="howMuch != null">#{howMuch},</if>
            <if test="indexBox != null">#{indexBox},</if>
            <if test="indexCate != null">#{indexCate},</if>
            <if test="isEnd != null">#{isEnd},</if>
            <if test="querykey != null">#{querykey},</if>
            <if test="rank != null">#{rank},</if>
            <if test="sort != null">#{sort},</if>
            <if test="startNeedpay != null">#{startNeedpay},</if>
            <if test="status != null">#{status},</if>
            <if test="tagColor != null">#{tagColor},</if>
            <if test="tagText != null">#{tagText},</if>
            <if test="tvClass != null">#{tvClass},</if>
            <if test="tvImage != null">#{tvImage},</if>
            <if test="tvName != null">#{tvName},</if>
            <if test="updateWitch != null">#{updateWitch},</if>
            <if test="appplatform != null">#{appplatform},</if>
            <if test="ttAuthAppid != null">#{ttAuthAppid},</if>
            <if test="wxAuthAppid != null">#{wxAuthAppid},</if>
            <if test="wxVideoId != null">#{wxVideoId},</if>
            <if test="ttVideoId != null">#{ttVideoId},</if>
            <if test="dataSources != null">#{dataSources},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
        </trim>
    </insert>

    <update id="updateShortplayTv" parameterType="com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity">
        update tb_shortplay_tv
        <trim prefix="SET" suffixOverrides=",">
            <if test="tbId != null">tb_id = #{tbId},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
            <if test="dataJson != null">data_json = #{dataJson},</if>
            <if test="AddTime != null">_add_time = #{AddTime},</if>
            <if test="AddTimeStr != null">_add_time_str = #{AddTimeStr},</if>
            <if test="allLike != null">all_like = #{allLike},</if>
            <if test="allSeries != null">all_series = #{allSeries},</if>
            <if test="allowAds != null">allow_ads = #{allowAds},</if>
            <if test="appid != null">appid = #{appid},</if>
            <if test="appname != null">appname = #{appname},</if>
            <if test="baseurl != null">baseurl = #{baseurl},</if>
            <if test="cate != null">cate = #{cate},</if>
            <if test="desc != null">`desc` = #{desc},</if>
            <if test="gzhboxEnd != null">gzhbox_end = #{gzhboxEnd},</if>
            <if test="gzhboxStart != null">gzhbox_start = #{gzhboxStart},</if>
            <if test="howMuch != null">how_much = #{howMuch},</if>
            <if test="indexBox != null">index_box = #{indexBox},</if>
            <if test="indexCate != null">index_cate = #{indexCate},</if>
            <if test="isEnd != null">is_end = #{isEnd},</if>
            <if test="querykey != null">querykey = #{querykey},</if>
            <if test="rank != null">`rank` = #{rank},</if>
            <if test="sort != null">`sort` = #{sort},</if>
            <if test="startNeedpay != null">start_needpay = #{startNeedpay},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tagColor != null">tag_color = #{tagColor},</if>
            <if test="tagText != null">tag_text = #{tagText},</if>
            <if test="tvClass != null">tv_class = #{tvClass},</if>
            <if test="tvImage != null">tv_image = #{tvImage},</if>
            <if test="tvName != null">tv_name = #{tvName},</if>
            <if test="updateWitch != null">update_witch = #{updateWitch},</if>
            <if test="appplatform != null">appplatform = #{appplatform},</if>
            <if test="ttAuthAppid != null">tt_auth_appid = #{ttAuthAppid},</if>
            <if test="wxAuthAppid != null">wx_auth_appid = #{wxAuthAppid},</if>
            <if test="wxVideoId != null">wx_video_id = #{wxVideoId},</if>
            <if test="ttVideoId != null">tt_video_id = #{ttVideoId},</if>
            <if test="dataSources != null">data_sources = #{dataSources},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayTvByTbId" parameterType="String">
        update tb_shortplay_tv set is_deleted = 1 where tb_id = #{tbId}
    </delete>

    <delete id="deleteShortplayTvByIds" parameterType="String">
        delete from tb_shortplay_tv where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


</mapper>
