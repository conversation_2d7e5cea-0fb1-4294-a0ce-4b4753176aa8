<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.tf.ShortplayDyTfurlMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.tf.ShortplayDyTfurlEntity" id="ShortplayDyTfurlResult">
            <result property="id"    column="id"    />
            <result property="tbId"    column="tb_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="dataJson"    column="data_json"    />
            <result property="bindFirstRecharge"    column="bind_first_recharge"    />
            <result property="bindFirstRechargeId"    column="bind_first_recharge_id"    />
            <result property="bindAgainRecharge"    column="bind_again_recharge"    />
            <result property="bindAgainRechargeId"    column="bind_again_recharge_id"    />
            <result property="adId"    column="ad_id"    />
            <result property="desc"    column="desc"    />
            <result property="tfurl"    column="tfurl"    />
            <result property="agentId"    column="agentId"    />
            <result property="agentUsername"    column="agent_username"    />
            <result property="agentNickname"    column="agent_nickname"    />
            <result property="middlemanId"    column="middleman_id"    />
            <result property="middlemanUsername"    column="middleman_username"    />
            <result property="middlemanNickname"    column="middleman_nickname"    />
            <result property="appid"    column="appid"    />
            <result property="appname"    column="appname"    />
            <result property="appsecret"    column="appsecret"    />
            <result property="tvId"    column="tv_id"    />
            <result property="tvName"    column="tv_name"    />
            <result property="series"    column="series"    />
            <result property="follow"    column="follow"    />
            <result property="gzhId"    column="gzhId"    />
            <result property="path"    column="path"    />
            <result property="platform"    column="platform"    />
            <result property="AddTime"    column="_add_time"    />
            <result property="AddTimeStr"    column="_add_time_str"    />
            <result property="urlLink"    column="url_link"    />
            <result property="urlLinkExpireTime"    column="url_link_expire_time"    />
            <result property="xcxpath"    column="xcxpath"    />
            <result property="isComputed"    column="isComputed"    />
            <result property="openWxBannerAdvert"    column="openWxBannerAdvert"    />
            <result property="openWxInterstitialAdvert"    column="openWxInterstitialAdvert"    />
            <result property="openWxVideoAdvert"    column="openWxVideoAdvert"    />
        <result property="payAmount"    column="pay_amount"    />
    </resultMap>

    <sql id="selectShortplayDyTfurlVo">
        select id, tb_id, gmt_create, gmt_modified, data_json, bind_first_recharge, bind_first_recharge_id, bind_again_recharge, bind_again_recharge_id, ad_id, `desc`, tfurl, agentId, agent_username, agent_nickname, middleman_id, middleman_username, middleman_nickname, appid, appname, appsecret, tv_id, tv_name, series, follow, gzhId, path, platform, _add_time, _add_time_str, url_link, url_link_expire_time, xcxpath, isComputed, openWxBannerAdvert,openWxInterstitialAdvert,openWxVideoAdvert,pay_amount from tb_shortplay_dy_tfurl
    </sql>

    <select id="selectByTfid" parameterType="String" resultMap="ShortplayDyTfurlResult">
        <include refid="selectShortplayDyTfurlVo"/>
        where tb_id = #{tfid}
    </select>

    <select id="selectShortplayDyTfurlList" parameterType="com.ruoyi.system.entity.shortplay.tf.ShortplayDyTfurlEntity" resultMap="ShortplayDyTfurlResult">
        <include refid="selectShortplayDyTfurlVo"/>
        <where>
                        <if test="tbId != null  and tbId != ''"> and tb_id = #{tbId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
                        <if test="dataJson != null  and dataJson != ''"> and data_json = #{dataJson}</if>
                        <if test="bindFirstRecharge != null  and bindFirstRecharge != ''"> and bind_first_recharge = #{bindFirstRecharge}</if>
                        <if test="bindFirstRechargeId != null  and bindFirstRechargeId != ''"> and bind_first_recharge_id = #{bindFirstRechargeId}</if>
                        <if test="bindAgainRecharge != null  and bindAgainRecharge != ''"> and bind_again_recharge = #{bindAgainRecharge}</if>
                        <if test="bindAgainRechargeId != null  and bindAgainRechargeId != ''"> and bind_again_recharge_id = #{bindAgainRechargeId}</if>
                        <if test="adId != null  and adId != ''"> and ad_id = #{adId}</if>
                        <if test="desc != null  and desc != ''"> and `desc` = #{desc}</if>
                        <if test="tfurl != null  and tfurl != ''"> and tfurl = #{tfurl}</if>
                        <if test="agentId != null  and agentId != ''"> and agentId = #{agentId}</if>
                        <if test="agentUsername != null  and agentUsername != ''"> and agent_username like concat('%', #{agentUsername}, '%')</if>
                        <if test="agentNickname != null  and agentNickname != ''"> and agent_nickname like concat('%', #{agentNickname}, '%')</if>
                        <if test="middlemanId != null  and middlemanId != ''"> and middleman_id = #{middlemanId}</if>
                        <if test="middlemanUsername != null  and middlemanUsername != ''"> and middleman_username like concat('%', #{middlemanUsername}, '%')</if>
                        <if test="middlemanNickname != null  and middlemanNickname != ''"> and middleman_nickname like concat('%', #{middlemanNickname}, '%')</if>
                        <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                        <if test="appname != null  and appname != ''"> and appname like concat('%', #{appname}, '%')</if>
                        <if test="appsecret != null  and appsecret != ''"> and appsecret = #{appsecret}</if>
                        <if test="tvId != null  and tvId != ''"> and tv_id = #{tvId}</if>
                        <if test="tvName != null  and tvName != ''"> and tv_name like concat('%', #{tvName}, '%')</if>
                        <if test="series != null "> and series = #{series}</if>
                        <if test="follow != null "> and follow = #{follow}</if>
                        <if test="gzhId != null  and gzhId != ''"> and gzhId = #{gzhId}</if>
                        <if test="path != null  and path != ''"> and path = #{path}</if>
                        <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
                        <if test="AddTime != null "> and _add_time = #{AddTime}</if>
                        <if test="AddTimeStr != null  and AddTimeStr != ''"> and _add_time_str = #{AddTimeStr}</if>
                        <if test="urlLink != null  and urlLink != ''"> and url_link = #{urlLink}</if>
                        <if test="urlLinkExpireTime != null "> and url_link_expire_time = #{urlLinkExpireTime}</if>
                        <if test="xcxpath != null  and xcxpath != ''"> and xcxpath = #{xcxpath}</if>
                        <if test="isComputed != null "> and isComputed = #{isComputed}</if>
                        <if test="openWxBannerAdvert != null "> and openWxBannerAdvert = #{openWxBannerAdvert}</if>
                        <if test="openWxInterstitialAdvert != null "> and openWxInterstitialAdvert = #{openWxInterstitialAdvert}</if>
                        <if test="openWxVideoAdvert != null "> and openWxVideoAdvert = #{openWxVideoAdvert}</if>
        </where>
    </select>

    <select id="selectShortplayDyTfurlById" parameterType="String" resultMap="ShortplayDyTfurlResult">
            <include refid="selectShortplayDyTfurlVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplayDyTfurl" parameterType="com.ruoyi.system.entity.shortplay.tf.ShortplayDyTfurlEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_dy_tfurl
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">tb_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
                    <if test="dataJson != null">data_json,</if>
                    <if test="bindFirstRecharge != null">bind_first_recharge,</if>
                    <if test="bindFirstRechargeId != null">bind_first_recharge_id,</if>
                    <if test="bindAgainRecharge != null">bind_again_recharge,</if>
                    <if test="bindAgainRechargeId != null">bind_again_recharge_id,</if>
                    <if test="adId != null">ad_id,</if>
                    <if test="desc != null">`desc`,</if>
                    <if test="tfurl != null">tfurl,</if>
                    <if test="agentId != null">agentId,</if>
                    <if test="agentUsername != null">agent_username,</if>
                    <if test="agentNickname != null">agent_nickname,</if>
                    <if test="middlemanId != null">middleman_id,</if>
                    <if test="middlemanUsername != null">middleman_username,</if>
                    <if test="middlemanNickname != null">middleman_nickname,</if>
                    <if test="appid != null">appid,</if>
                    <if test="appname != null">appname,</if>
                    <if test="appsecret != null">appsecret,</if>
                    <if test="tvId != null">tv_id,</if>
                    <if test="tvName != null">tv_name,</if>
                    <if test="series != null">series,</if>
                    <if test="follow != null">follow,</if>
                    <if test="gzhId != null">gzhId,</if>
                    <if test="path != null">path,</if>
                    <if test="platform != null">platform,</if>
                    <if test="AddTime != null">_add_time,</if>
                    <if test="AddTimeStr != null">_add_time_str,</if>
                    <if test="urlLink != null">url_link,</if>
                    <if test="urlLinkExpireTime != null">url_link_expire_time,</if>
                    <if test="xcxpath != null">xcxpath,</if>
                    <if test="isComputed != null">isComputed,</if>
                    <if test="openWxBannerAdvert != null">openWxBannerAdvert,</if>
                    <if test="openWxInterstitialAdvert != null">openWxInterstitialAdvert,</if>
                    <if test="openWxVideoAdvert != null">openWxVideoAdvert,</if>
                    <if test="payAmount != null">pay_amount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="tbId != null">#{tbId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
                    <if test="dataJson != null">#{dataJson},</if>
                    <if test="bindFirstRecharge != null">#{bindFirstRecharge},</if>
                    <if test="bindFirstRechargeId != null">#{bindFirstRechargeId},</if>
                    <if test="bindAgainRecharge != null">#{bindAgainRecharge},</if>
                    <if test="bindAgainRechargeId != null">#{bindAgainRechargeId},</if>
                    <if test="adId != null">#{adId},</if>
                    <if test="desc != null">#{desc},</if>
                    <if test="tfurl != null">#{tfurl},</if>
                    <if test="agentId != null">#{agentId},</if>
                    <if test="agentUsername != null">#{agentUsername},</if>
                    <if test="agentNickname != null">#{agentNickname},</if>
                    <if test="middlemanId != null">#{middlemanId},</if>
                    <if test="middlemanUsername != null">#{middlemanUsername},</if>
                    <if test="middlemanNickname != null">#{middlemanNickname},</if>
                    <if test="appid != null">#{appid},</if>
                    <if test="appname != null">#{appname},</if>
                    <if test="appsecret != null">#{appsecret},</if>
                    <if test="tvId != null">#{tvId},</if>
                    <if test="tvName != null">#{tvName},</if>
                    <if test="series != null">#{series},</if>
                    <if test="follow != null">#{follow},</if>
                    <if test="gzhId != null">#{gzhId},</if>
                    <if test="path != null">#{path},</if>
                    <if test="platform != null">#{platform},</if>
                    <if test="AddTime != null">#{AddTime},</if>
                    <if test="AddTimeStr != null">#{AddTimeStr},</if>
                    <if test="urlLink != null">#{urlLink},</if>
                    <if test="urlLinkExpireTime != null">#{urlLinkExpireTime},</if>
                    <if test="xcxpath != null">#{xcxpath},</if>
                    <if test="isComputed != null">#{isComputed},</if>
                    <if test="openWxBannerAdvert != null">#{openWxBannerAdvert},</if>
                    <if test="openWxInterstitialAdvert != null">#{openWxInterstitialAdvert},</if>
                    <if test="openWxVideoAdvert != null">#{openWxVideoAdvert},</if>
                    <if test="payAmount != null">#{payAmount},</if>
        </trim>
    </insert>

    <update id="updateShortplayDyTfurl" parameterType="com.ruoyi.system.entity.shortplay.tf.ShortplayDyTfurlEntity">
        update tb_shortplay_dy_tfurl
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tbId != null">tb_id = #{tbId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
                    <if test="dataJson != null">data_json = #{dataJson},</if>
                    <if test="bindFirstRecharge != null">bind_first_recharge = #{bindFirstRecharge},</if>
                    <if test="bindFirstRechargeId != null">bind_first_recharge_id = #{bindFirstRechargeId},</if>
                    <if test="bindAgainRecharge != null">bind_again_recharge = #{bindAgainRecharge},</if>
                    <if test="bindAgainRechargeId != null">bind_again_recharge_id = #{bindAgainRechargeId},</if>
                    <if test="adId != null">ad_id = #{adId},</if>
                    <if test="desc != null">`desc` = #{desc},</if>
                    <if test="tfurl != null">tfurl = #{tfurl},</if>
                    <if test="agentId != null">agentId = #{agentId},</if>
                    <if test="agentUsername != null">agent_username = #{agentUsername},</if>
                    <if test="agentNickname != null">agent_nickname = #{agentNickname},</if>
                    <if test="middlemanId != null">middleman_id = #{middlemanId},</if>
                    <if test="middlemanUsername != null">middleman_username = #{middlemanUsername},</if>
                    <if test="middlemanNickname != null">middleman_nickname = #{middlemanNickname},</if>
                    <if test="appid != null">appid = #{appid},</if>
                    <if test="appname != null">appname = #{appname},</if>
                    <if test="appsecret != null">appsecret = #{appsecret},</if>
                    <if test="tvId != null">tv_id = #{tvId},</if>
                    <if test="tvName != null">tv_name = #{tvName},</if>
                    <if test="series != null">series = #{series},</if>
                    <if test="follow != null">follow = #{follow},</if>
                    <if test="gzhId != null">gzhId = #{gzhId},</if>
                    <if test="path != null">`path` = #{path},</if>
                    <if test="platform != null">platform = #{platform},</if>
                    <if test="AddTime != null">_add_time = #{AddTime},</if>
                    <if test="AddTimeStr != null">_add_time_str = #{AddTimeStr},</if>
                    <if test="urlLink != null">url_link = #{urlLink},</if>
                    <if test="urlLinkExpireTime != null">url_link_expire_time = #{urlLinkExpireTime},</if>
                    <if test="xcxpath != null">xcxpath = #{xcxpath},</if>
                    <if test="isComputed != null">isComputed = #{isComputed},</if>
                    <if test="openWxBannerAdvert != null">openWxBannerAdvert = #{openWxBannerAdvert},</if>
                    <if test="openWxInterstitialAdvert != null">openWxInterstitialAdvert = #{openWxInterstitialAdvert},</if>
                    <if test="openWxVideoAdvert != null">openWxVideoAdvert = #{openWxVideoAdvert},</if>
                    <if test="payAmount != null">pay_amount = #{payAmount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayDyTfurlById" parameterType="String">
        delete from tb_shortplay_dy_tfurl where id = #{id}
    </delete>
    <delete id="deleteShortplayDyTfurlByTbId" parameterType="String">
        delete from tb_shortplay_dy_tfurl where tb_id = #{id}
    </delete>

    <delete id="deleteShortplayDyTfurlByIds" parameterType="String">
        delete from tb_shortplay_dy_tfurl where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
