<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.rp.ShortplayRpProcessMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.ShortplayRpProcessEntity" id="ShortplayRpProcessResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="userId"    column="user_id"    />
            <result property="appId"    column="app_id"    />
            <result property="ladderLevel"    column="ladder_level"    />
            <result property="ladderProgress"    column="ladder_progress"    />
            <result property="curAmount"    column="cur_amount"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayRpProcessVo">
        select id, cur_date, user_id, app_id, ladder_level, ladder_progress, cur_amount, gmt_create, gmt_modified from tb_shortplay_rp_process
    </sql>

    <select id="selectShortplayRpProcessList" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpProcessEntity" resultMap="ShortplayRpProcessResult">
        <include refid="selectShortplayRpProcessVo"/>
        <where>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
                        <if test="ladderLevel != null "> and ladder_level = #{ladderLevel}</if>
                        <if test="ladderProgress != null "> and ladder_progress = #{ladderProgress}</if>
                        <if test="curAmount != null "> and cur_amount = #{curAmount}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayRpProcessById" parameterType="Long" resultMap="ShortplayRpProcessResult">
            <include refid="selectShortplayRpProcessVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplayRpProcess" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpProcessEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shortplay_rp_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="ladderLevel != null">ladder_level,</if>
                    <if test="ladderProgress != null">ladder_progress,</if>
                    <if test="curAmount != null">cur_amount,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="ladderLevel != null">#{ladderLevel},</if>
                    <if test="ladderProgress != null">#{ladderProgress},</if>
                    <if test="curAmount != null">#{curAmount},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateShortplayRpProcess" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpProcessEntity">
        update tb_shortplay_rp_process
        <trim prefix="SET" suffixOverrides=",">
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="ladderLevel != null">ladder_level = #{ladderLevel},</if>
                    <if test="ladderProgress != null">ladder_progress = #{ladderProgress},</if>
                    <if test="curAmount != null">cur_amount = #{curAmount},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayRpProcessById" parameterType="Long">
        delete from tb_shortplay_rp_process where id = #{id}
    </delete>

    <delete id="deleteShortplayRpProcessByIds" parameterType="String">
        delete from tb_shortplay_rp_process where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>