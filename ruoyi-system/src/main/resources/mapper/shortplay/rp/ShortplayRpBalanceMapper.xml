<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.rp.ShortplayRpBalanceMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.ShortplayRpBalanceEntity" id="ShortplayRpBalanceResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userBalance" column="user_balance"/>
        <result property="appId" column="app_id"/>
        <result property="lastRefresh" column="last_refresh"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectShortplayRpBalanceVo">
        select id, user_id, user_balance, app_id, last_refresh, gmt_create, gmt_modified from tb_shortplay_rp_balance
    </sql>

    <select id="selectShortplayRpBalanceList" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpBalanceEntity"
            resultMap="ShortplayRpBalanceResult">
        <include refid="selectShortplayRpBalanceVo"/>
        <where>
            <if test="userBalance != null ">and user_balance = #{userBalance}</if>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="lastRefresh != null ">and last_refresh = #{lastRefresh}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayRpBalanceById" parameterType="Long" resultMap="ShortplayRpBalanceResult">
        <include refid="selectShortplayRpBalanceVo"/>
        where id = #{id}
    </select>
    <select id="selectDaysAgo" resultType="java.lang.Long">
        select id from
        tb_shortplay_rp_balance WHERE last_refresh &lt; DATE_SUB(CURRENT_TIME(), INTERVAL 3 DAY)
    </select>

    <insert id="insertShortplayRpBalance" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpBalanceEntity">
        insert into tb_shortplay_rp_balance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userBalance != null">user_balance,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="lastRefresh != null">last_refresh,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userBalance != null">#{userBalance},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="lastRefresh != null">#{lastRefresh},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateShortplayRpBalance" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpBalanceEntity">
        update tb_shortplay_rp_balance
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userBalance != null">user_balance = #{userBalance},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="lastRefresh != null">last_refresh = #{lastRefresh},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="clearBalanceUpdateLastRefresh">
        update tb_shortplay_rp_balance set user_balance = 0.00, last_refresh = CURRENT_TIME()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteShortplayRpBalanceById" parameterType="Long">
        delete from tb_shortplay_rp_balance where id = #{id}
    </delete>

    <delete id="deleteShortplayRpBalanceByIds" parameterType="String">
        delete from tb_shortplay_rp_balance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
