<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.rp.ShortplayRpViewRecordMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.ShortplayRpViewRecordEntity" id="ShortplayRpViewRecordResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="userId"    column="user_id"    />
            <result property="tvId"    column="tv_id"    />
            <result property="series"    column="series"    />
            <result property="appId"    column="app_id"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectShortplayRpViewRecordVo">
        select id, cur_date, user_id, tv_id, series, app_id, gmt_create, gmt_modified from tb_shortplay_rp_view_record
    </sql>

    <select id="selectShortplayRpViewRecordList" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpViewRecordEntity" resultMap="ShortplayRpViewRecordResult">
        <include refid="selectShortplayRpViewRecordVo"/>
        <where>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="tvId != null "> and tv_id = #{tvId}</if>
                        <if test="series != null "> and series = #{series}</if>
                        <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayRpViewRecordById" parameterType="Long" resultMap="ShortplayRpViewRecordResult">
            <include refid="selectShortplayRpViewRecordVo"/>
            where id = #{id}
    </select>

    <insert id="insertShortplayRpViewRecord" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpViewRecordEntity">
        insert into tb_shortplay_rp_view_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="tvId != null">tv_id,</if>
                    <if test="series != null">series,</if>
                    <if test="appId != null and appId != ''">app_id,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="tvId != null">#{tvId},</if>
                    <if test="series != null">#{series},</if>
                    <if test="appId != null and appId != ''">#{appId},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateShortplayRpViewRecord" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpViewRecordEntity">
        update tb_shortplay_rp_view_record
        <trim prefix="SET" suffixOverrides=",">
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="tvId != null">tv_id = #{tvId},</if>
                    <if test="series != null">series = #{series},</if>
                    <if test="appId != null and appId != ''">app_id = #{appId},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayRpViewRecordById" parameterType="Long">
        delete from tb_shortplay_rp_view_record where id = #{id}
    </delete>

    <delete id="deleteShortplayRpViewRecordByIds" parameterType="String">
        delete from tb_shortplay_rp_view_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>