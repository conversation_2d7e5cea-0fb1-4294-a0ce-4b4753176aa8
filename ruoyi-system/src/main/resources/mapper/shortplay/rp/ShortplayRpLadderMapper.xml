<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.shortplay.rp.ShortplayRpLadderMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.ShortplayRpLadderEntity" id="ShortplayRpLadderResult">
        <result property="id" column="id"/>
        <result property="ladderLevel" column="ladder_level"/>
        <result property="ladderAmount" column="ladder_amount"/>
        <result property="ladderNumber" column="ladder_number"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectShortplayRpLadderVo">
        select id, ladder_level, ladder_number, ladder_amount, gmt_create, gmt_modified from tb_shortplay_rp_ladder
    </sql>

    <select id="selectShortplayRpLadderList" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpLadderEntity"
            resultMap="ShortplayRpLadderResult">
        <include refid="selectShortplayRpLadderVo"/>
        <where>
            <if test="ladderLevel != null ">and ladder_level = #{ladderLevel}</if>
            <if test="ladderNumber != null ">and ladder_number = #{ladderNumber}</if>
            <if test="ladderAmount != null ">and ladder_amount = #{ladderAmount}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectShortplayRpLadderById" parameterType="Long" resultMap="ShortplayRpLadderResult">
        <include refid="selectShortplayRpLadderVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortplayRpLadder" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpLadderEntity">
        insert into tb_shortplay_rp_ladder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ladderLevel != null">ladder_level,</if>
            <if test="ladderNumber != null">ladder_number,</if>
            <if test="ladderAmount != null">ladder_amount,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ladderLevel != null">#{ladderLevel},</if>
            <if test="ladderNumber != null">#{ladderNumber},</if>
            <if test="ladderAmount != null">#{ladderAmount},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateShortplayRpLadder" parameterType="com.ruoyi.system.entity.shortplay.ShortplayRpLadderEntity">
        update tb_shortplay_rp_ladder
        <trim prefix="SET" suffixOverrides=",">
            <if test="ladderLevel != null">ladder_level = #{ladderLevel},</if>
            <if test="ladderNumber != null">ladder_number = #{ladderNumber},</if>
            <if test="ladderAmount != null">ladder_amount = #{ladderAmount},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortplayRpLadderById" parameterType="Long">
        delete from tb_shortplay_rp_ladder where id = #{id}
    </delete>

    <delete id="deleteShortplayRpLadderByIds" parameterType="String">
        delete from tb_shortplay_rp_ladder where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
