<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.metrics.AgtDataMaterialMetricsMapper">

    <resultMap type="com.ruoyi.system.entity.metrics.AgtDataMaterialMetricsEntity" id="AgtDataMaterialMetricsResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="materialId" column="material_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="statCost" column="stat_cost"/>
        <result property="showCnt" column="show_cnt"/>
        <result property="cpmPlatform" column="cpm_platform"/>
        <result property="clickCnt" column="click_cnt"/>
        <result property="cpcPlatform" column="cpc_platform"/>
        <result property="ctr" column="ctr"/>
        <result property="convertCnt" column="convert_cnt"/>
        <result property="conversionRate" column="conversion_rate"/>
        <result property="playDuration3s" column="play_duration_3s"/>
        <result property="validPlay" column="valid_play"/>
        <result property="totalPlay" column="total_play"/>
        <result property="validPlayRate" column="valid_play_rate"/>
        <result property="playOverRate" column="play_over_rate"/>
        <result property="playOverCnt" column="play_over_cnt"/>
        <result property="activeCnt" column="active_cnt"/>
        <result property="activeCost" column="active_cost"/>
        <result property="payRatio" column="pay_ratio"/>
        <result property="dyLike" column="dy_like"/>
        <result property="dyShare" column="dy_share"/>
        <result property="dyComment" column="dy_comment"/>
        <result property="dyFollow" column="dy_follow"/>
        <result property="dislikeCnt" column="dislike_cnt"/>
        <result property="reportCnt" column="report_cnt"/>
        <result property="dyHomeVisited" column="dy_home_visited"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectAgtDataMaterialMetricsVo">
        select id, advertiser_id, material_id, cur_date, cur_hour, stat_cost, show_cnt, cpm_platform, click_cnt,
        cpc_platform, ctr, convert_cnt, conversion_rate, play_duration_3s, valid_play, total_play, valid_play_rate,
        play_over_rate, play_over_cnt,active_cnt,active_cost,pay_ratio, dy_like, dy_share, dy_comment, dy_follow, dislike_cnt, report_cnt,
        dy_home_visited, gmt_create, gmt_modified from tb_agt_data_material_metrics
    </sql>

    <select id="selectAgtDataMaterialMetricsList"
            parameterType="com.ruoyi.system.entity.metrics.AgtDataMaterialMetricsEntity"
            resultMap="AgtDataMaterialMetricsResult">
        <include refid="selectAgtDataMaterialMetricsVo"/>
        <where>
            <if test="advertiserId != null  and advertiserId != ''">and advertiser_id = #{advertiserId}</if>
            <if test="materialId != null  and materialId != ''">and material_id = #{materialId}</if>
            <if test="curDate != null ">and cur_date = #{curDate}</if>
            <if test="curHour != null ">and cur_hour = #{curHour}</if>
            <if test="statCost != null ">and stat_cost = #{statCost}</if>
            <if test="showCnt != null ">and show_cnt = #{showCnt}</if>
            <if test="cpmPlatform != null ">and cpm_platform = #{cpmPlatform}</if>
            <if test="clickCnt != null ">and click_cnt = #{clickCnt}</if>
            <if test="cpcPlatform != null ">and cpc_platform = #{cpcPlatform}</if>
            <if test="ctr != null ">and ctr = #{ctr}</if>
            <if test="convertCnt != null ">and convert_cnt = #{convertCnt}</if>
            <if test="conversionRate != null ">and conversion_rate = #{conversionRate}</if>
            <if test="playDuration3s != null ">and play_duration_3s = #{playDuration3s}</if>
            <if test="validPlay != null ">and valid_play = #{validPlay}</if>
            <if test="totalPlay != null ">and total_play = #{totalPlay}</if>
            <if test="validPlayRate != null ">and valid_play_rate = #{validPlayRate}</if>
            <if test="playOverRate != null ">and play_over_rate = #{playOverRate}</if>
            <if test="playOverCnt != null ">and play_over_cnt = #{playOverCnt}</if>
            <if test="activeCnt != null ">and active_cnt = #{activeCnt}</if>
            <if test="activeCost != null ">and active_cost = #{activeCost}</if>
            <if test="payRatio != null ">and pay_ratio = #{payRatio}</if>
            <if test="dyLike != null ">and dy_like = #{dyLike}</if>
            <if test="dyShare != null ">and dy_share = #{dyShare}</if>
            <if test="dyComment != null ">and dy_comment = #{dyComment}</if>
            <if test="dyFollow != null ">and dy_follow = #{dyFollow}</if>
            <if test="dislikeCnt != null ">and dislike_cnt = #{dislikeCnt}</if>
            <if test="reportCnt != null ">and report_cnt = #{reportCnt}</if>
            <if test="dyHomeVisited != null ">and dy_home_visited = #{dyHomeVisited}</if>
            <if test="gmtCreate != null ">and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null ">and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataMaterialMetricsById" parameterType="Long" resultMap="AgtDataMaterialMetricsResult">
        <include refid="selectAgtDataMaterialMetricsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAgtDataMaterialMetrics"
            parameterType="com.ruoyi.system.entity.metrics.AgtDataMaterialMetricsEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into tb_agt_data_material_metrics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
            <if test="materialId != null and materialId != ''">material_id,</if>
            <if test="curDate != null">cur_date,</if>
            <if test="curHour != null">cur_hour,</if>
            <if test="statCost != null">stat_cost,</if>
            <if test="showCnt != null">show_cnt,</if>
            <if test="cpmPlatform != null">cpm_platform,</if>
            <if test="clickCnt != null">click_cnt,</if>
            <if test="cpcPlatform != null">cpc_platform,</if>
            <if test="ctr != null">ctr,</if>
            <if test="convertCnt != null">convert_cnt,</if>
            <if test="conversionRate != null">conversion_rate,</if>
            <if test="playDuration3s != null">play_duration_3s,</if>
            <if test="validPlay != null">valid_play,</if>
            <if test="totalPlay != null">total_play,</if>
            <if test="validPlayRate != null">valid_play_rate,</if>
            <if test="playOverRate != null">play_over_rate,</if>
            <if test="playOverCnt != null">play_over_cnt,</if>
            <if test="activeCnt != null">active_cnt,</if>
            <if test="activeCost != null">active_cost,</if>
            <if test="payRatio != null">pay_ratio,</if>
            <if test="dyLike != null">dy_like,</if>
            <if test="dyShare != null">dy_share,</if>
            <if test="dyComment != null">dy_comment,</if>
            <if test="dyFollow != null">dy_follow,</if>
            <if test="dislikeCnt != null">dislike_cnt,</if>
            <if test="reportCnt != null">report_cnt,</if>
            <if test="dyHomeVisited != null">dy_home_visited,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
            <if test="materialId != null and materialId != ''">#{materialId},</if>
            <if test="curDate != null">#{curDate},</if>
            <if test="curHour != null">#{curHour},</if>
            <if test="statCost != null">#{statCost},</if>
            <if test="showCnt != null">#{showCnt},</if>
            <if test="cpmPlatform != null">#{cpmPlatform},</if>
            <if test="clickCnt != null">#{clickCnt},</if>
            <if test="cpcPlatform != null">#{cpcPlatform},</if>
            <if test="ctr != null">#{ctr},</if>
            <if test="convertCnt != null">#{convertCnt},</if>
            <if test="conversionRate != null">#{conversionRate},</if>
            <if test="playDuration3s != null">#{playDuration3s},</if>
            <if test="validPlay != null">#{validPlay},</if>
            <if test="totalPlay != null">#{totalPlay},</if>
            <if test="validPlayRate != null">#{validPlayRate},</if>
            <if test="playOverRate != null">#{playOverRate},</if>
            <if test="playOverCnt != null">#{playOverCnt},</if>
            <if test="activeCnt != null">#{activeCnt},</if>
            <if test="activeCost != null">#{activeCost},</if>
            <if test="payRatio != null">#{payRatio},</if>
            <if test="dyLike != null">#{dyLike},</if>
            <if test="dyShare != null">#{dyShare},</if>
            <if test="dyComment != null">#{dyComment},</if>
            <if test="dyFollow != null">#{dyFollow},</if>
            <if test="dislikeCnt != null">#{dislikeCnt},</if>
            <if test="reportCnt != null">#{reportCnt},</if>
            <if test="dyHomeVisited != null">#{dyHomeVisited},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataMaterialMetrics"
            parameterType="com.ruoyi.system.entity.metrics.AgtDataMaterialMetricsEntity">
        update tb_agt_data_material_metrics
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
            <if test="materialId != null and materialId != ''">material_id = #{materialId},</if>
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="curHour != null">cur_hour = #{curHour},</if>
            <if test="statCost != null">stat_cost = #{statCost},</if>
            <if test="showCnt != null">show_cnt = #{showCnt},</if>
            <if test="cpmPlatform != null">cpm_platform = #{cpmPlatform},</if>
            <if test="clickCnt != null">click_cnt = #{clickCnt},</if>
            <if test="cpcPlatform != null">cpc_platform = #{cpcPlatform},</if>
            <if test="ctr != null">ctr = #{ctr},</if>
            <if test="convertCnt != null">convert_cnt = #{convertCnt},</if>
            <if test="conversionRate != null">conversion_rate = #{conversionRate},</if>
            <if test="playDuration3s != null">play_duration_3s = #{playDuration3s},</if>
            <if test="validPlay != null">valid_play = #{validPlay},</if>
            <if test="totalPlay != null">total_play = #{totalPlay},</if>
            <if test="validPlayRate != null">valid_play_rate = #{validPlayRate},</if>
            <if test="playOverRate != null">play_over_rate = #{playOverRate},</if>
            <if test="playOverCnt != null">play_over_cnt = #{playOverCnt},</if>
            <if test="activeCnt != null">active_cnt = #{activeCnt},</if>
            <if test="activeCost != null">active_cost = #{activeCost},</if>
            <if test="payRatio != null">pay_ratio = #{payRatio},</if>
            <if test="dyLike != null">dy_like = #{dyLike},</if>
            <if test="dyShare != null">dy_share = #{dyShare},</if>
            <if test="dyComment != null">dy_comment = #{dyComment},</if>
            <if test="dyFollow != null">dy_follow = #{dyFollow},</if>
            <if test="dislikeCnt != null">dislike_cnt = #{dislikeCnt},</if>
            <if test="reportCnt != null">report_cnt = #{reportCnt},</if>
            <if test="dyHomeVisited != null">dy_home_visited = #{dyHomeVisited},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataMaterialMetricsById" parameterType="Long">
        delete from tb_agt_data_material_metrics where id = #{id}
    </delete>

    <delete id="deleteAgtDataMaterialMetricsByIds" parameterType="String">
        delete from tb_agt_data_material_metrics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>