<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.metrics.AgtDataMaterialMetricsSelfMapper">

    <resultMap type="com.ruoyi.system.entity.metrics.AgtDataMaterialMetricsEntity" id="AgtDataMaterialMetricsResult">
        <result property="id" column="id"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="materialId" column="material_id"/>
        <result property="curDate" column="cur_date"/>
        <result property="curHour" column="cur_hour"/>
        <result property="statCost" column="stat_cost"/>
        <result property="showCnt" column="show_cnt"/>
        <result property="cpmPlatform" column="cpm_platform"/>
        <result property="clickCnt" column="click_cnt"/>
        <result property="cpcPlatform" column="cpc_platform"/>
        <result property="ctr" column="ctr"/>
        <result property="convertCnt" column="convert_cnt"/>
        <result property="conversionRate" column="conversion_rate"/>
        <result property="playDuration3s" column="play_duration_3s"/>
        <result property="validPlay" column="valid_play"/>
        <result property="totalPlay" column="total_play"/>
        <result property="validPlayRate" column="valid_play_rate"/>
        <result property="playOverRate" column="play_over_rate"/>
        <result property="playOverCnt" column="play_over_cnt"/>
        <result property="activeCnt" column="active_cnt"/>
        <result property="activeCost" column="active_cost"/>
        <result property="payRatio" column="pay_ratio"/>
        <result property="dyLike" column="dy_like"/>
        <result property="dyShare" column="dy_share"/>
        <result property="dyComment" column="dy_comment"/>
        <result property="dyFollow" column="dy_follow"/>
        <result property="dislikeCnt" column="dislike_cnt"/>
        <result property="reportCnt" column="report_cnt"/>
        <result property="dyHomeVisited" column="dy_home_visited"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>


    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into
        tb_agt_data_material_metrics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].advertiserId != null">advertiser_id,</if>
            <if test="list[0].materialId != null">material_id,</if>
            <if test="list[0].curDate != null">cur_date,</if>
            <if test="list[0].curHour != null">cur_hour,</if>
            <if test="list[0].statCost != null">stat_cost,</if>
            <if test="list[0].showCnt != null">show_cnt,</if>
            <if test="list[0].cpmPlatform != null">cpm_platform,</if>
            <if test="list[0].clickCnt != null">click_cnt,</if>
            <if test="list[0].cpcPlatform != null">cpc_platform,</if>
            <if test="list[0].ctr != null">ctr,</if>
            <if test="list[0].convertCnt != null">convert_cnt,</if>
            <if test="list[0].conversionRate != null">conversion_rate,</if>
            <if test="list[0].playDuration3s != null">play_duration_3s,</if>
            <if test="list[0].validPlay != null">valid_play,</if>
            <if test="list[0].totalPlay != null">total_play,</if>
            <if test="list[0].validPlayRate != null">valid_play_rate,</if>
            <if test="list[0].playOverRate != null">play_over_rate,</if>
            <if test="list[0].playOverCnt != null">play_over_cnt,</if>
            <if test="list[0].activeCnt != null">active_cnt,</if>
            <if test="list[0].activeCost != null">active_cost,</if>
            <if test="list[0].payRatio != null">pay_ratio,</if>
            <if test="list[0].dyLike != null">dy_like,</if>
            <if test="list[0].dyShare != null">dy_share,</if>
            <if test="list[0].dyComment != null">dy_comment,</if>
            <if test="list[0].dyFollow != null">dy_follow,</if>
            <if test="list[0].dislikeCnt != null">dislike_cnt,</if>
            <if test="list[0].reportCnt != null">report_cnt,</if>
            <if test="list[0].dyHomeVisited != null">dy_home_visited,</if>
        </trim>
        values
        <foreach collection="list" separator="," item="entity">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="entity.advertiserId != null">#{entity.advertiserId},</if>
                <if test="entity.materialId != null">#{entity.materialId},</if>
                <if test="entity.curDate != null">#{entity.curDate},</if>
                <if test="entity.curHour != null">#{entity.curHour},</if>
                <if test="entity.statCost != null">#{entity.statCost},</if>
                <if test="entity.showCnt != null">#{entity.showCnt},</if>
                <if test="entity.cpmPlatform != null">#{entity.cpmPlatform},</if>
                <if test="entity.clickCnt != null">#{entity.clickCnt},</if>
                <if test="entity.cpcPlatform != null">#{entity.cpcPlatform},</if>
                <if test="entity.ctr != null">#{entity.ctr},</if>
                <if test="entity.convertCnt != null">#{entity.convertCnt},</if>
                <if test="entity.conversionRate != null">#{entity.conversionRate},</if>
                <if test="entity.playDuration3s != null">#{entity.playDuration3s},</if>
                <if test="entity.validPlay != null">#{entity.validPlay},</if>
                <if test="entity.totalPlay != null">#{entity.totalPlay},</if>
                <if test="entity.validPlayRate != null">#{entity.validPlayRate},</if>
                <if test="entity.playOverRate != null">#{entity.playOverRate},</if>
                <if test="entity.playOverCnt != null">#{entity.playOverCnt},</if>
                <if test="entity.activeCnt != null">#{entity.activeCnt},</if>
                <if test="entity.activeCost != null">#{entity.activeCost},</if>
                <if test="entity.payRatio != null">#{entity.payRatio},</if>
                <if test="entity.dyLike != null">#{entity.dyLike},</if>
                <if test="entity.dyShare != null">#{entity.dyShare},</if>
                <if test="entity.dyComment != null">#{entity.dyComment},</if>
                <if test="entity.dyFollow != null">#{entity.dyFollow},</if>
                <if test="entity.dislikeCnt != null">#{entity.dislikeCnt},</if>
                <if test="entity.reportCnt != null">#{entity.reportCnt},</if>
                <if test="entity.dyHomeVisited != null">#{entity.dyHomeVisited},</if>
            </trim>
        </foreach>
        on duplicate key update
        stat_cost = VALUES(stat_cost),
        show_cnt = VALUES(show_cnt),
        cpm_platform = VALUES(cpm_platform),
        click_cnt = VALUES(click_cnt),
        cpc_platform = VALUES(cpc_platform),
        ctr = VALUES(ctr),
        convert_cnt = VALUES(convert_cnt),
        conversion_rate = VALUES(conversion_rate),
        play_duration_3s = VALUES(play_duration_3s),
        valid_play = VALUES(valid_play),
        total_play = VALUES(total_play),
        valid_play_rate = VALUES(valid_play_rate),
        play_over_rate = VALUES(play_over_rate),
        play_over_cnt = VALUES(play_over_cnt),
        active_cnt = VALUES(active_cnt),
        active_cost = VALUES(active_cost),
        pay_ratio = VALUES(pay_ratio),
        dy_like = VALUES(dy_like),
        dy_share = VALUES(dy_share),
        dy_comment = VALUES(dy_comment),
        dy_follow = VALUES(dy_follow),
        dislike_cnt = VALUES(dislike_cnt),
        report_cnt = VALUES(report_cnt),
        dy_home_visited = VALUES(dy_home_visited)
    </insert>


</mapper>