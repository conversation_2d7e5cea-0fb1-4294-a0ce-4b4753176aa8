<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.supplier.AgtAccountSupplierMapper">

    <resultMap type="com.ruoyi.system.entity.supplier.AgtAccountSupplierEntity" id="AgtAccountSupplierResult">
            <result property="id"    column="id"    />
            <result property="supplierNameShort"    column="supplier_name_short"    />
            <result property="supplierNameReal"    column="supplier_name_real"    />
            <result property="platformDyWx"    column="platform_dy_wx"    />
            <result property="wxAndroidRatio"    column="wx_android_ratio"    />
            <result property="wxIosRatio"    column="wx_ios_ratio"    />
            <result property="wxAdRatio"    column="wx_ad_ratio"    />
            <result property="dyAndroidRatio"    column="dy_android_ratio"    />
            <result property="dyIosRatio"    column="dy_ios_ratio"    />
            <result property="dyAdRatio"    column="dy_ad_ratio"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtAccountSupplierVo">
        select id, supplier_name_short, supplier_name_real, platform_dy_wx, wx_android_ratio, wx_ios_ratio, wx_ad_ratio, dy_android_ratio, dy_ios_ratio, dy_ad_ratio, gmt_create, gmt_modified from tb_agt_account_supplier
    </sql>

    <select id="selectAgtAccountSupplierList" parameterType="com.ruoyi.system.entity.supplier.AgtAccountSupplierEntity" resultMap="AgtAccountSupplierResult">
        <include refid="selectAgtAccountSupplierVo"/>
        <where>
                        <if test="supplierNameShort != null  and supplierNameShort != ''"> and supplier_name_short = #{supplierNameShort}</if>
                        <if test="supplierNameReal != null  and supplierNameReal != ''"> and supplier_name_real = #{supplierNameReal}</if>
                        <if test="platformDyWx != null "> and platform_dy_wx = #{platformDyWx}</if>
                        <if test="wxAndroidRatio != null "> and wx_android_ratio = #{wxAndroidRatio}</if>
                        <if test="wxIosRatio != null "> and wx_ios_ratio = #{wxIosRatio}</if>
                        <if test="wxAdRatio != null "> and wx_ad_ratio = #{wxAdRatio}</if>
                        <if test="dyAndroidRatio != null "> and dy_android_ratio = #{dyAndroidRatio}</if>
                        <if test="dyIosRatio != null "> and dy_ios_ratio = #{dyIosRatio}</if>
                        <if test="dyAdRatio != null "> and dy_ad_ratio = #{dyAdRatio}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectBySupplierNameShort" parameterType="String" resultMap="AgtAccountSupplierResult">
        <include refid="selectAgtAccountSupplierVo"/>
        where supplier_name_short = #{supplierNameShort}
        limit 1
    </select>

    <select id="selectAgtAccountSupplierById" parameterType="String" resultMap="AgtAccountSupplierResult">
            <include refid="selectAgtAccountSupplierVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtAccountSupplier" parameterType="com.ruoyi.system.entity.supplier.AgtAccountSupplierEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_account_supplier
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="supplierNameShort != null">supplier_name_short,</if>
                    <if test="supplierNameReal != null">supplier_name_real,</if>
                    <if test="platformDyWx != null">platform_dy_wx,</if>
                    <if test="wxAndroidRatio != null">wx_android_ratio,</if>
                    <if test="wxIosRatio != null">wx_ios_ratio,</if>
                    <if test="wxAdRatio != null">wx_ad_ratio,</if>
                    <if test="dyAndroidRatio != null">dy_android_ratio,</if>
                    <if test="dyIosRatio != null">dy_ios_ratio,</if>
                    <if test="dyAdRatio != null">dy_ad_ratio,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="supplierNameShort != null">#{supplierNameShort},</if>
                    <if test="supplierNameReal != null">#{supplierNameReal},</if>
                    <if test="platformDyWx != null">#{platformDyWx},</if>
                    <if test="wxAndroidRatio != null">#{wxAndroidRatio},</if>
                    <if test="wxIosRatio != null">#{wxIosRatio},</if>
                    <if test="wxAdRatio != null">#{wxAdRatio},</if>
                    <if test="dyAndroidRatio != null">#{dyAndroidRatio},</if>
                    <if test="dyIosRatio != null">#{dyIosRatio},</if>
                    <if test="dyAdRatio != null">#{dyAdRatio},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtAccountSupplier" parameterType="com.ruoyi.system.entity.supplier.AgtAccountSupplierEntity">
        update tb_agt_account_supplier
        <trim prefix="SET" suffixOverrides=",">
                    <if test="supplierNameShort != null">supplier_name_short = #{supplierNameShort},</if>
                    <if test="supplierNameReal != null">supplier_name_real = #{supplierNameReal},</if>
                    <if test="platformDyWx != null">platform_dy_wx = #{platformDyWx},</if>
                    <if test="wxAndroidRatio != null">wx_android_ratio = #{wxAndroidRatio},</if>
                    <if test="wxIosRatio != null">wx_ios_ratio = #{wxIosRatio},</if>
                    <if test="wxAdRatio != null">wx_ad_ratio = #{wxAdRatio},</if>
                    <if test="dyAndroidRatio != null">dy_android_ratio = #{dyAndroidRatio},</if>
                    <if test="dyIosRatio != null">dy_ios_ratio = #{dyIosRatio},</if>
                    <if test="dyAdRatio != null">dy_ad_ratio = #{dyAdRatio},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtAccountSupplierById" parameterType="String">
        delete from tb_agt_account_supplier where id = #{id}
    </delete>

    <delete id="deleteAgtAccountSupplierByIds" parameterType="String">
        delete from tb_agt_account_supplier where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
