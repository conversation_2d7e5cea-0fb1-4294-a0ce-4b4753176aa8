<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtMiniappDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtMiniappDayDataEntity" id="GdtMiniappDayDataResult">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="miniappId" column="miniapp_id"/>
        <result property="miniappName" column="miniapp_name"/>
        <result property="cost" column="cost"/>
        <result property="cashCost" column="cash_cost"/>
        <result property="income" column="income"/>
        <result property="ecpm" column="ecpm"/>
        <result property="adMoney" column="ad_money"/>
        <result property="visitUv" column="visit_uv"/>
        <result property="exposureCount" column="exposure_count"/>
        <result property="clickCount" column="click_count"/>
        <result property="reqSuccCount" column="req_succ_count"/>
        <result property="stayTimeUv" column="stay_time_uv"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectGdtMiniappDayDataVo">
        select id,
        cur_date,
        miniapp_id,
        miniapp_name,
        cost,
        cash_cost,
        income,
        ecpm,
        ad_money,
        visit_uv,
        exposure_count,
        req_succ_count,
        click_count,
        stay_time_uv,
        gmt_create,
        gmt_modified
        from tb_gdt_miniapp_day_data
    </sql>

    <select id="selectGdtMiniappDayDataList" parameterType="com.ruoyi.system.param.wxiaa.GdtMiniappDayDataParam"
            resultMap="GdtMiniappDayDataResult">
        select
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                ${column}
            </foreach>
        </if>
        sum(cost) as cost,
        sum(cash_cost) as cash_cost,
        sum(income) as income,
        sum(ecpm) as ecpm,
        sum(ad_money) as ad_money,
        sum(visit_uv) as visit_uv,
        sum(exposure_count) as exposure_count,
        sum(click_count) as click_count,
        sum(stay_time_uv*visit_uv)/sum(visit_uv) as stay_time_uv,
        sum(req_succ_count) as req_succ_count,
        sum(exposure_count)/sum(visit_uv) as ipu,
        sum(income)/sum(visit_uv) as ad_arpu,
        sum(income)+sum(ad_money)-sum(cash_cost) as profit_estimate,
        sum(income)+sum(ad_money)-sum(cost) as profit_unrebate
        from tb_gdt_miniapp_day_data d
        left join tb_gdt_miniapp a on d.miniapp_id = a.app_id
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="miniappNames != null and miniappNames.size() > 0">
                and miniapp_name in
                <foreach collection="miniappNames" item="miniappName" separator="," open="(" close=")">
                    #{miniappName}
                </foreach>
            </if>
            <if test="appType != null">and a.app_type = #{appType}</if>
        </where>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <choose>
            <when test="dimension != null and dimension.size() > 0 and dimension.contains('cur_date')">
                order by cur_date desc, ${orderColumn} ${orderType}
            </when>
            <otherwise>
                order by ${orderColumn} ${orderType}
            </otherwise>
        </choose>
    </select>

    <select id="selectGdtMiniappDayDataById" parameterType="Long" resultMap="GdtMiniappDayDataResult">
        <include refid="selectGdtMiniappDayDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertGdtMiniappDayData" parameterType="com.ruoyi.system.entity.wxiaa.GdtMiniappDayDataEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_miniapp_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="miniappId != null and miniappId != ''">miniapp_id,</if>
            <if test="miniappName != null and miniappName != ''">miniapp_name,</if>
            <if test="cost != null">cost,</if>
            <if test="cashCost != null">cash_cost,</if>
            <if test="income != null">income,</if>
            <if test="ecpm != null">ecpm,</if>
            <if test="adMoney != null">ad_money,</if>
            <if test="visitUv != null">visit_uv,</if>
            <if test="exposureCount != null">exposure_count,</if>
            <if test="clickCount != null">click_count,</if>
            <if test="reqSuccCount != null">req_succ_count,</if>
            <if test="stayTimeUv != null">stay_time_uv,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="miniappId != null and miniappId != ''">#{miniappId},</if>
            <if test="miniappName != null and miniappName != ''">#{miniappName},</if>
            <if test="cost != null">#{cost},</if>
            <if test="cashCost != null">#{cashCost},</if>
            <if test="income != null">#{income},</if>
            <if test="adMoney != null">#{adMoney},</if>
            <if test="visitUv != null">#{visitUv},</if>
            <if test="exposureCount != null">#{exposureCount},</if>
            <if test="clickCount != null">#{clickCount},</if>
            <if test="reqSuccCount != null">#{reqSuccCount},</if>
            <if test="stayTimeUv != null">#{stayTimeUv},</if>
        </trim>
    </insert>
    <insert id="batchInsertUpdate">
        insert into tb_gdt_miniapp_day_data(`cur_date`, `miniapp_id`, `miniapp_name`, `cost`,
        `cash_cost`,`ad_monetization_amount`,
        `income`, `ecpm`, `visit_uv`, `exposure_count`, `click_count`, `req_succ_count`,`stay_time_uv`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.curDate},#{item.miniappId},#{item.miniappName},#{item.cost},#{item.cashCost},#{item.adMonetizationAmount},#{item.income},#{item.ecpm},#{item.visitUv},#{item.exposureCount},#{item.clickCount},#{item.reqSuccCount},#{item.stayTimeUv})
        </foreach>
        on duplicate key update
        cost = VALUES(cost),
        cash_cost = VALUES(cash_cost),
        ad_monetization_amount = VALUES(ad_monetization_amount),
        income = VALUES(income),
        ecpm = VALUES(ecpm),
        visit_uv= VALUES(visit_uv),
        exposure_count = VALUES(exposure_count),
        click_count = VALUES(click_count),
        stay_time_uv = VALUES(stay_time_uv),
        req_succ_count = VALUES(req_succ_count)
    </insert>

    <update id="updateGdtMiniappDayData" parameterType="com.ruoyi.system.entity.wxiaa.GdtMiniappDayDataEntity">
        update tb_gdt_miniapp_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="miniappId != null and miniappId != ''">miniapp_id = #{miniappId},</if>
            <if test="miniappName != null and miniappName != ''">miniapp_name = #{miniappName},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="cashCost != null">cash_cost = #{cashCost},</if>
            <if test="income != null">income = #{income},</if>
            <if test="ecpm != null">ecpm = #{ecpm},</if>
            <if test="adMoney != null">ad_money = #{adMoney},</if>
            <if test="visitUv != null">visit_uv = #{visitUv},</if>
            <if test="exposureCount != null">exposure_count = #{exposureCount},</if>
            <if test="clickCount != null">click_count = #{clickCount},</if>
            <if test="reqSuccCount != null">req_succ_count = #{reqSuccCount},</if>
            <if test="stayTimeUv != null">stay_time_uv = #{stayTimeUv},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtMiniappDayDataById" parameterType="Long">
        delete
        from tb_gdt_miniapp_day_data
        where id = #{id}
    </delete>

    <delete id="deleteGdtMiniappDayDataByIds" parameterType="String">
        delete from tb_gdt_miniapp_day_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
