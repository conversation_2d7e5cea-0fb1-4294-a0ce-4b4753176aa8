<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtAppMapper">

    <resultMap type="com.ruoyi.system.entity.app.GdtAppEntity" id="GdtAppResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="appSecret"    column="app_secret"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtAppVo">
        select id, app_id, app_secret, gmt_create, gmt_modified from tb_gdt_app
    </sql>

    <select id="selectGdtAppList" parameterType="com.ruoyi.system.entity.app.GdtAppEntity" resultMap="GdtAppResult">
        <include refid="selectGdtAppVo"/>
        <where>
                        <if test="appId != null "> and app_id = #{appId}</if>
                        <if test="appSecret != null  and appSecret != ''"> and app_secret = #{appSecret}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectGdtAppById" parameterType="Long" resultMap="GdtAppResult">
            <include refid="selectGdtAppVo"/>
            where id = #{id}
    </select>

    <insert id="insertGdtApp" parameterType="com.ruoyi.system.entity.app.GdtAppEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="appId != null">app_id,</if>
                    <if test="appSecret != null and appSecret != ''">app_secret,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="appId != null">#{appId},</if>
                    <if test="appSecret != null and appSecret != ''">#{appSecret},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateGdtApp" parameterType="com.ruoyi.system.entity.app.GdtAppEntity">
        update tb_gdt_app
        <trim prefix="SET" suffixOverrides=",">
                    <if test="appId != null">app_id = #{appId},</if>
                    <if test="appSecret != null and appSecret != ''">app_secret = #{appSecret},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtAppById" parameterType="Long">
        delete from tb_gdt_app where id = #{id}
    </delete>

    <delete id="deleteGdtAppByIds" parameterType="String">
        delete from tb_gdt_app where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>