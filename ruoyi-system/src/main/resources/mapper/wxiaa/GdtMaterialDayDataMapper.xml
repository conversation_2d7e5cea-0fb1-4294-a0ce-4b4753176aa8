<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtMaterialDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtMaterialDayDataEntity" id="GdtMaterialDayDataResult">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="advertisementId" column="advertisement_id"/>
        <result property="materialId" column="material_id"/>
        <result property="materialType" column="material_type"/>
        <result property="materialDesc" column="material_desc"/>
        <result property="materialEditor" column="material_editor"/>
        <result property="viewCount" column="view_count"/>
        <result property="validClickCount" column="valid_click_count"/>
        <result property="conversionsCount" column="conversions_count"/>
        <result property="appAdPayingUsers" column="app_ad_paying_users"/>
        <result property="adMonetizationAmount" column="ad_monetization_amount"/>
        <result property="cost" column="cost"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="accountId" column="account_id"/>
    </resultMap>

    <sql id="selectGdtMaterialDayDataVo">
        select id, cur_date, advertisement_id, material_id, material_type, material_desc, material_editor, view_count,
        valid_click_count, conversions_count, app_ad_paying_users, ad_monetization_amount, cost, gmt_create,
        gmt_modified from tb_gdt_material_day_data
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.param.wxiaa.GdtMaterialDayDataParam"
            resultMap="GdtMaterialDayDataResult">
        select
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                ${column}
            </foreach>
        </if>
        sum(cost) as cost, sum(ad_monetization_amount) as ad_monetization_amount,
        sum(view_count) as view_count, sum(valid_click_count) as valid_click_count, sum(conversions_count) as
        conversions_count,
        sum(app_ad_paying_users) as app_ad_paying_users
        from tb_gdt_material_day_data m
        left join tb_gdt_advertisement_account a on m.advertisement_id = a.advertisement_id
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="accountId != null ">and account_id = #{accountId}</if>
            <if test="materialType != null ">and material_type = #{materialType}</if>
            <if test="materialEditors != null and materialEditors.size() > 0">
                and material_editor in
                <foreach collection="materialEditors" item="materialEditor" open="(" close=")" separator=",">
                    #{materialEditor}
                </foreach>
            </if>
        </where>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <choose>
            <when test="dimension != null and dimension.size() > 0 and dimension.contains('cur_date')">
                order by cur_date desc, ${orderColumn} ${orderType}
            </when>
            <otherwise>
                order by ${orderColumn} ${orderType}
            </otherwise>
        </choose>
    </select>

    <select id="selectGdtMaterialDayDataById" parameterType="Long" resultMap="GdtMaterialDayDataResult">
        <include refid="selectGdtMaterialDayDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertGdtMaterialDayData" parameterType="com.ruoyi.system.entity.wxiaa.GdtMaterialDayDataEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_material_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertisementId != null">advertisement_id,</if>
            <if test="materialId != null and materialId != ''">material_id,</if>
            <if test="materialType != null">material_type,</if>
            <if test="materialDesc != null and materialDesc != ''">material_desc,</if>
            <if test="materialEditor != null and materialEditor != ''">material_editor,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="validClickCount != null">valid_click_count,</if>
            <if test="conversionsCount != null">conversions_count,</if>
            <if test="appAdPayingUsers != null">app_ad_paying_users,</if>
            <if test="adMonetizationAmount != null">ad_monetization_amount,</if>
            <if test="cost != null">cost,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertisementId != null">#{advertisementId},</if>
            <if test="materialId != null and materialId != ''">#{materialId},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="materialDesc != null and materialDesc != ''">#{materialDesc},</if>
            <if test="materialEditor != null and materialEditor != ''">#{materialEditor},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="validClickCount != null">#{validClickCount},</if>
            <if test="conversionsCount != null">#{conversionsCount},</if>
            <if test="appAdPayingUsers != null">#{appAdPayingUsers},</if>
            <if test="adMonetizationAmount != null">#{adMonetizationAmount},</if>
            <if test="cost != null">#{cost},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateGdtMaterialDayData" parameterType="com.ruoyi.system.entity.wxiaa.GdtMaterialDayDataEntity">
        update tb_gdt_material_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
            <if test="materialId != null and materialId != ''">material_id = #{materialId},</if>
            <if test="materialType != null">material_type = #{materialType},</if>
            <if test="materialDesc != null and materialDesc != ''">material_desc = #{materialDesc},</if>
            <if test="materialEditor != null and materialEditor != ''">material_editor = #{materialEditor},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="validClickCount != null">valid_click_count = #{validClickCount},</if>
            <if test="conversionsCount != null">conversions_count = #{conversionsCount},</if>
            <if test="appAdPayingUsers != null">app_ad_paying_users = #{appAdPayingUsers},</if>
            <if test="adMonetizationAmount != null">ad_monetization_amount = #{adMonetizationAmount},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtMaterialDayDataById" parameterType="Long">
        delete from tb_gdt_material_day_data where id = #{id}
    </delete>

    <delete id="deleteGdtMaterialDayDataByIds" parameterType="String">
        delete from tb_gdt_material_day_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertUpdate">
        insert into tb_gdt_material_day_data (`cur_date`, `advertisement_id`,
        `material_id`,`material_type`,`material_desc`,`material_editor`, `view_count`, `valid_click_count`,
        `conversions_count`, `app_ad_paying_users`, `ad_monetization_amount`, `cost`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.curDate},#{item.advertisementId},#{item.materialId},#{item.materialType}, #{item.materialDesc},
            #{item.materialEditor}, #{item.viewCount}, #{item.validClickCount}, #{item.conversionsCount},
            #{item.appAdPayingUsers}, #{item.adMonetizationAmount}, #{item.cost})
        </foreach>
        on duplicate key update
        advertisement_id = values(advertisement_id),
        material_desc = values(material_desc),
        material_editor = values(material_editor),
        view_count = values(view_count),
        valid_click_count = values(valid_click_count),
        conversions_count = values(conversions_count),
        app_ad_paying_users = values(app_ad_paying_users),
        ad_monetization_amount = values(ad_monetization_amount),
        cost = values(cost)
    </insert>

    <!-- 查询指定日期以"图片"开头的图片描述数据ID列表 -->
    <select id="selectImageDescStartsWithImageIds" resultType="java.lang.Long">
        select id from tb_gdt_material_day_data
        where cur_date = #{curDate}
        and material_type = 1
        and material_desc like '图片%'
        order by id
        limit #{limit} offset #{offset}
    </select>

    <!-- 根据ID列表批量删除数据 -->
    <delete id="batchDeleteByIds">
        delete from tb_gdt_material_day_data where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
