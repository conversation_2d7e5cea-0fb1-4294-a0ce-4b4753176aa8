<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtAccountMapper">

    <resultMap type="com.ruoyi.system.entity.account.GdtAccountEntity" id="GdtAccountResult">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="appId" column="app_id"/>
        <result property="accessToken" column="access_token"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="refreshTokenExp" column="refresh_token_exp"/>
        <result property="expiresIn" column="expires_in"/>
        <result property="rebate" column="rebate"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectGdtAccountVo">
        select id,
               account_id,
               account_name,
               app_id,
               rebate,
               access_token,
               refresh_token,
               refresh_token_exp,
               expires_in,
               gmt_create,
               gmt_modified
        from tb_gdt_account
    </sql>

    <select id="selectGdtAccountList" parameterType="com.ruoyi.system.entity.account.GdtAccountEntity"
            resultMap="GdtAccountResult">
        <include refid="selectGdtAccountVo"/>
        <where>
            <if test="accountId != null ">and account_id = #{accountId}</if>
            <if test="accountName != null  and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
            <if test="accessToken != null  and accessToken != ''">and access_token = #{accessToken}</if>
            <if test="refreshToken != null  and refreshToken != ''">and refresh_token = #{refreshToken}</if>
            <if test="refreshTokenExp != null ">and refresh_token_exp = #{refreshTokenExp}</if>
            <if test="expiresIn != null ">and expires_in = #{expiresIn}</if>
        </where>
    </select>

    <select id="selectGdtAccountById" parameterType="Long" resultMap="GdtAccountResult">
        <include refid="selectGdtAccountVo"/>
        where id = #{id}
    </select>
    <select id="selectGdtAccountByAccountId" resultMap="GdtAccountResult">
        <include refid="selectGdtAccountVo"/>
        where account_id = #{accountId}
    </select>
    <select id="selectGdtAccountByAccountIds" resultMap="GdtAccountResult">
        <include refid="selectGdtAccountVo"/>
        where account_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertGdtAccount" parameterType="com.ruoyi.system.entity.account.GdtAccountEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="appId != null">app_id,</if>
            <if test="accessToken != null and accessToken != ''">access_token,</if>
            <if test="refreshToken != null and refreshToken != ''">refresh_token,</if>
            <if test="refreshTokenExp != null">refresh_token_exp,</if>
            <if test="expiresIn != null">expires_in,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="appId != null">#{appId},</if>
            <if test="accessToken != null and accessToken != ''">#{accessToken},</if>
            <if test="refreshToken != null and refreshToken != ''">#{refreshToken},</if>
            <if test="refreshTokenExp != null">#{refreshTokenExp},</if>
            <if test="expiresIn != null">#{expiresIn},</if>
        </trim>
    </insert>

    <update id="updateGdtAccount" parameterType="com.ruoyi.system.entity.account.GdtAccountEntity">
        update tb_gdt_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="rebate != null">rebate = #{rebate},</if>
            <if test="accessToken != null and accessToken != ''">access_token = #{accessToken},</if>
            <if test="refreshToken != null and refreshToken != ''">refresh_token = #{refreshToken},</if>
            <if test="refreshTokenExp != null">refresh_token_exp = #{refreshTokenExp},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateGdtAccountByAppId" parameterType="com.ruoyi.system.entity.account.GdtAccountEntity">
        update tb_gdt_account
        <set>
            <if test="accessToken != null and accessToken != ''">access_token = #{accessToken},</if>
            <if test="refreshToken != null and refreshToken != ''">refresh_token = #{refreshToken},</if>
        </set>
        where app_id = #{appId} and account_id = #{accountId}
    </update>

    <delete id="deleteGdtAccountByIds" parameterType="String">
        delete from tb_gdt_account where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
