<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtAdgroupMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtAdgroupEntity" id="GdtAdgroupResult">
            <result property="id"    column="id"    />
            <result property="accountId"    column="account_id"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="advertisementName"    column="advertisement_name"    />
            <result property="adgroupId"    column="adgroup_id"    />
            <result property="adgroupName"    column="adgroup_name"    />
            <result property="targeting"    column="targeting"    />
            <result property="targetingTranslation"    column="targeting_translation"    />
            <result property="beginDate"    column="begin_date"    />
            <result property="endDate"    column="end_date"    />
            <result property="bidAmount"    column="bid_amount"    />
            <result property="dailyBudget"    column="daily_budget"    />
            <result property="createdTime"    column="created_time"    />
            <result property="lastModifiedTime"    column="last_modified_time"    />
            <result property="isDeleted"    column="is_deleted"    />
            <result property="adgroupDetail"    column="adgroup_detail"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtAdgroupVo">
        select id, account_id, advertisement_id, advertisement_name, adgroup_id, adgroup_name, targeting, targeting_translation, begin_date, end_date, bid_amount, daily_budget, created_time, last_modified_time, is_deleted, adgroup_detail, gmt_create, gmt_modified from tb_gdt_adgroup
    </sql>

    <select id="selectGdtAdgroupList" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdgroupEntity" resultMap="GdtAdgroupResult">
        <include refid="selectGdtAdgroupVo"/>
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="advertisementId != null "> and advertisement_id = #{advertisementId}</if>
            <if test="advertisementName != null  and advertisementName != ''"> and advertisement_name like concat('%', #{advertisementName}, '%')</if>
            <if test="adgroupId != null "> and adgroup_id = #{adgroupId}</if>
            <if test="adgroupName != null  and adgroupName != ''"> and adgroup_name like concat('%', #{adgroupName}, '%')</if>
            <if test="targeting != null  and targeting != ''"> and targeting = #{targeting}</if>
            <if test="targetingTranslation != null  and targetingTranslation != ''"> and targeting_translation = #{targetingTranslation}</if>
            <if test="beginDate != null "> and begin_date = #{beginDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="bidAmount != null "> and bid_amount = #{bidAmount}</if>
            <if test="dailyBudget != null "> and daily_budget = #{dailyBudget}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="lastModifiedTime != null "> and last_modified_time = #{lastModifiedTime}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
            <if test="adgroupDetail != null  and adgroupDetail != ''"> and adgroup_detail = #{adgroupDetail}</if>
            <if test="adgroupSearch != null and adgroupSearch != ''"> and (adgroup_name like concat('%', #{adgroupSearch}, '%') or adgroup_id = #{adgroupSearch})</if>
        </where>
        order by id desc
    </select>

    <select id="selectGdtAdgroupById" parameterType="Long" resultMap="GdtAdgroupResult">
        <include refid="selectGdtAdgroupVo"/>
        where id = #{id}
    </select>

    <select id="selectAdgroupCount" resultType="com.ruoyi.system.bo.wxiaa.GdtAdgroupCountBo">
        select advertisement_id as advertisementId, count(1) as adgroupCount
        from tb_gdt_adgroup
        where advertisement_id in
        <foreach item="advertisementId" collection="advertisementIds" open="(" separator="," close=")">
            #{advertisementId}
        </foreach>
        group by advertisement_id
    </select>

    <select id="selectByAdgroupId" parameterType="Long" resultMap="GdtAdgroupResult">
        <include refid="selectGdtAdgroupVo"/>
        where adgroup_id = #{adgroupId}
    </select>
    <select id="selectByAdgroupIds" resultMap="GdtAdgroupResult">
        <include refid="selectGdtAdgroupVo"/>
        where adgroup_id in
        <foreach item="adgroupId" collection="adgroupIds" open="(" separator="," close=")">
            #{adgroupId}
        </foreach>
    </select>

    <insert id="insertGdtAdgroup" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdgroupEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_adgroup
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">account_id,</if>
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name,</if>
                    <if test="adgroupId != null">adgroup_id,</if>
                    <if test="adgroupName != null and adgroupName != ''">adgroup_name,</if>
                    <if test="targeting != null">targeting,</if>
                    <if test="targetingTranslation != null">targeting_translation,</if>
                    <if test="beginDate != null">begin_date,</if>
                    <if test="endDate != null">end_date,</if>
                    <if test="bidAmount != null">bid_amount,</if>
                    <if test="dailyBudget != null">daily_budget,</if>
                    <if test="createdTime != null">created_time,</if>
                    <if test="lastModifiedTime != null">last_modified_time,</if>
                    <if test="isDeleted != null">is_deleted,</if>
                    <if test="adgroupDetail != null">adgroup_detail,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">#{accountId},</if>
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">#{advertisementName},</if>
                    <if test="adgroupId != null">#{adgroupId},</if>
                    <if test="adgroupName != null and adgroupName != ''">#{adgroupName},</if>
                    <if test="targeting != null">#{targeting},</if>
                    <if test="targetingTranslation != null">#{targetingTranslation},</if>
                    <if test="beginDate != null">#{beginDate},</if>
                    <if test="endDate != null">#{endDate},</if>
                    <if test="bidAmount != null">#{bidAmount},</if>
                    <if test="dailyBudget != null">#{dailyBudget},</if>
                    <if test="createdTime != null">#{createdTime},</if>
                    <if test="lastModifiedTime != null">#{lastModifiedTime},</if>
                    <if test="isDeleted != null">#{isDeleted},</if>
                    <if test="adgroupDetail != null">#{adgroupDetail},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateGdtAdgroup" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdgroupEntity">
        update tb_gdt_adgroup
        <trim prefix="SET" suffixOverrides=",">
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name = #{advertisementName},</if>
                    <if test="adgroupId != null">adgroup_id = #{adgroupId},</if>
                    <if test="adgroupName != null and adgroupName != ''">adgroup_name = #{adgroupName},</if>
                    <if test="targeting != null">targeting = #{targeting},</if>
                    <if test="targetingTranslation != null">targeting_translation = #{targetingTranslation},</if>
                    <if test="beginDate != null">begin_date = #{beginDate},</if>
                    <if test="endDate != null">end_date = #{endDate},</if>
                    <if test="bidAmount != null">bid_amount = #{bidAmount},</if>
                    <if test="dailyBudget != null">daily_budget = #{dailyBudget},</if>
                    <if test="createdTime != null">created_time = #{createdTime},</if>
                    <if test="lastModifiedTime != null">last_modified_time = #{lastModifiedTime},</if>
                    <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
                    <if test="adgroupDetail != null">adgroup_detail = #{adgroupDetail},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtAdgroupById" parameterType="Long">
        delete from tb_gdt_adgroup where id = #{id}
    </delete>

    <delete id="deleteGdtAdgroupByIds" parameterType="String">
        delete from tb_gdt_adgroup where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertUpdate">
        insert into tb_gdt_adgroup (`account_id`,`advertisement_id`,`advertisement_name`,`adgroup_id`,`adgroup_name`,`targeting`,`targeting_translation`,`begin_date`,`end_date`,`bid_amount`,`daily_budget`,`created_time`,`last_modified_time`,`is_deleted`,`adgroup_detail`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.accountId}, #{item.advertisementId},#{item.advertisementName}, #{item.adgroupId}, #{item.adgroupName}, #{item.targeting}, #{item.targetingTranslation}, #{item.beginDate}, #{item.endDate}, #{item.bidAmount}, #{item.dailyBudget}, #{item.createdTime}, #{item.lastModifiedTime}, #{item.isDeleted}, #{item.adgroupDetail})
        </foreach>
        on duplicate key update
        adgroup_name = values(adgroup_name),
        targeting = values(targeting),
        targeting_translation = values(targeting_translation),
        begin_date = values(begin_date),
        end_date = values(end_date),
        bid_amount = values(bid_amount),
        daily_budget = values(daily_budget),
        created_time = values(created_time),
        last_modified_time = values(last_modified_time),
        is_deleted = values(is_deleted),
        adgroup_detail = values(adgroup_detail)
    </insert>
</mapper>
