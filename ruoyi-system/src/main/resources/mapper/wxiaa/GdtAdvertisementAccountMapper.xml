<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtAdvertisementAccountMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtAdvertisementAccountEntity" id="GdtAdvertisementAccountResult">
            <result property="id"    column="id"    />
            <result property="accountId"    column="account_id"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="advertisementName"    column="advertisement_name"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtAdvertisementAccountVo">
        select id, account_id, advertisement_id, advertisement_name, gmt_create, gmt_modified from tb_gdt_advertisement_account
    </sql>

    <select id="selectGdtAdvertisementAccountList" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdvertisementAccountEntity" resultMap="GdtAdvertisementAccountResult">
        <include refid="selectGdtAdvertisementAccountVo"/>
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
            <if test="advertisementId != null "> and advertisement_id = #{advertisementId}</if>
            <if test="advertisementName != null  and advertisementName != ''"> and advertisement_name like concat('%', #{advertisementName}, '%')</if>
            <if test="advertisementSearch != null and advertisementSearch != ''"> and (advertisement_name like concat('%', #{advertisementSearch}, '%') or advertisement_id = #{advertisementSearch})</if>
            <if test="advertisementIds != null and advertisementIds.size() > 0">
                AND advertisement_id IN
                <foreach item="advertisementId" index="index" collection="advertisementIds" open="(" separator="," close=")">
                    #{advertisementId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectGdtAdvertisementAccountById" parameterType="String" resultMap="GdtAdvertisementAccountResult">
            <include refid="selectGdtAdvertisementAccountVo"/>
            where id = #{id}
    </select>

    <select id="selectByAdvertisementId" parameterType="Long" resultMap="GdtAdvertisementAccountResult">
        <include refid="selectGdtAdvertisementAccountVo"/>
        where advertisement_id = #{advertisementId}
    </select>
    <select id="selectByAdvertisementIds"
            resultMap="GdtAdvertisementAccountResult">
        <include refid="selectGdtAdvertisementAccountVo"/>
        where advertisement_id in
        <foreach collection="advertisementIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="insertGdtAdvertisementAccount" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdvertisementAccountEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_advertisement_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">account_id,</if>
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">#{accountId},</if>
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">#{advertisementName},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateGdtAdvertisementAccount" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdvertisementAccountEntity">
        update tb_gdt_advertisement_account
        <trim prefix="SET" suffixOverrides=",">
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="advertisementName != null and advertisementName != ''">advertisement_name = #{advertisementName},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtAdvertisementAccountById" parameterType="String">
        delete from tb_gdt_advertisement_account where id = #{id}
    </delete>

    <delete id="deleteGdtAdvertisementAccountByIds" parameterType="String">
        delete from tb_gdt_advertisement_account where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="deleteGdtAdvertisementAccountByAdvertisementId">
        update tb_gdt_advertisement_account set is_deleted = 1 where advertisement_id = #{advertisementId}
    </update>

    <insert id="insertGdtAdvertisementAccountList">
        insert ignore into tb_gdt_advertisement_account
        (account_id, advertisement_id, advertisement_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountId}, #{item.advertisementId}, #{item.advertisementName})
        </foreach>
    </insert>

</mapper>
