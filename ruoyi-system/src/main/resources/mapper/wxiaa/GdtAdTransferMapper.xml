<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtAdTransferMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtAdTransferEntity" id="GdtAdTransferResult">
            <result property="id"    column="id"    />
            <result property="appName"    column="app_name"    />
            <result property="transferOutAccountId"    column="transfer_out_account_id"    />
            <result property="transferOutAccountName"    column="transfer_out_account_name"    />
            <result property="transferAmount"    column="transfer_amount"    />
            <result property="transferStatus"    column="transfer_status"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtAdTransferVo">
        select id, app_name, transfer_out_account_id, transfer_out_account_name, transfer_amount, transfer_status, gmt_create, gmt_modified from tb_gdt_ad_transfer
    </sql>

    <select id="selectGdtAdTransferList" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdTransferEntity" resultMap="GdtAdTransferResult">
        <include refid="selectGdtAdTransferVo"/>
        <where>
                        <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
                        <if test="transferOutAccountId != null "> and transfer_out_account_id = #{transferOutAccountId}</if>
                        <if test="transferOutAccountName != null  and transferOutAccountName != ''"> and transfer_out_account_name like concat('%', #{transferOutAccountName}, '%')</if>
                        <if test="transferAmount != null "> and transfer_amount = #{transferAmount}</if>
                        <if test="transferStatus != null "> and transfer_status = #{transferStatus}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectGdtAdTransferById" parameterType="Long" resultMap="GdtAdTransferResult">
            <include refid="selectGdtAdTransferVo"/>
            where id = #{id}
    </select>

    <insert id="insertGdtAdTransfer" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdTransferEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_ad_transfer
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="appName != null">app_name,</if>
                    <if test="transferOutAccountId != null">transfer_out_account_id,</if>
                    <if test="transferOutAccountName != null">transfer_out_account_name,</if>
                    <if test="transferAmount != null">transfer_amount,</if>
                    <if test="transferStatus != null">transfer_status,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="appName != null">#{appName},</if>
                    <if test="transferOutAccountId != null">#{transferOutAccountId},</if>
                    <if test="transferOutAccountName != null">#{transferOutAccountName},</if>
                    <if test="transferAmount != null">#{transferAmount},</if>
                    <if test="transferStatus != null">#{transferStatus},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateGdtAdTransfer" parameterType="com.ruoyi.system.entity.wxiaa.GdtAdTransferEntity">
        update tb_gdt_ad_transfer
        <trim prefix="SET" suffixOverrides=",">
                    <if test="appName != null">app_name = #{appName},</if>
                    <if test="transferOutAccountId != null">transfer_out_account_id = #{transferOutAccountId},</if>
                    <if test="transferOutAccountName != null">transfer_out_account_name = #{transferOutAccountName},</if>
                    <if test="transferAmount != null">transfer_amount = #{transferAmount},</if>
                    <if test="transferStatus != null">transfer_status = #{transferStatus},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtAdTransferById" parameterType="Long">
        delete from tb_gdt_ad_transfer where id = #{id}
    </delete>

    <delete id="deleteGdtAdTransferByIds" parameterType="String">
        delete from tb_gdt_ad_transfer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>