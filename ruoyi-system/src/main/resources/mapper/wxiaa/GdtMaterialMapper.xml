<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtMaterialMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtMaterialEntity" id="GdtMaterialResult">
            <result property="id"    column="id"    />
            <result property="materialId"    column="material_id"    />
            <result property="materialType"    column="material_type"    />
            <result property="materialDesc"    column="material_desc"    />
            <result property="materialCreateTime"    column="material_create_time"    />
            <result property="materialLastModifiedTime"    column="material_last_modified_time"    />
            <result property="previewUrl"    column="preview_url"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="materialMd5"    column="material_md5"    />
            <result property="matrixMid"    column="matrix_mid"    />
            <result property="tagId"    column="tag_id"    />
            <result property="materialInfo"    column="material_info"    />
            <result property="clInfo"    column="cl_info"    />
            <result property="isDeleted"    column="is_deleted"    />
            <result property="isHide"    column="is_hide"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtMaterialVo">
        select id, material_id, material_type,tag_id, material_desc,material_md5,matrix_mid,material_info,advertisement_id, material_create_time, material_last_modified_time, preview_url, cl_info, is_deleted, is_hide, gmt_create, gmt_modified from tb_gdt_material
    </sql>

    <select id="selectGdtMaterialList" parameterType="com.ruoyi.system.req.matrix.GdtMaterialReq" resultMap="GdtMaterialResult">
        <include refid="selectGdtMaterialVo"/>
        <where>
            <if test="tagId != null  and tagId != 0"> and tag_id = #{tagId}</if>
            <if test="materialDesc != null and materialDesc != ''"> and material_desc like concat(#{materialDesc}, '%')</if>
            <if test="startTime != null "> and material_create_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and material_create_time &lt;= #{endTime}</if>
            <if test="materialId != null "> and material_id = #{materialId}</if>
            <if test="isHide != null "> and is_hide = #{isHide}</if>
        </where>
    </select>

    <select id="selectGdtMaterialById" parameterType="Long" resultMap="GdtMaterialResult">
            <include refid="selectGdtMaterialVo"/>
            where id = #{id}
    </select>

    <select id="selectByMaterialIds"  resultMap="GdtMaterialResult">
        <include refid="selectGdtMaterialVo"/>
        where material_id in
        <foreach item="materialId" collection="materialIds" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </select>

    <select id="selectClMaterial"  resultMap="GdtMaterialResult">
        select id, material_id, material_type,tag_id, material_desc,material_md5,matrix_mid,material_info,advertisement_id, material_create_time, material_last_modified_time, preview_url, cl_info, is_deleted
        from tb_gdt_material
        where is_deleted = 0 and material_type = 1 and tag_id = 100
    </select>

    <select id="selectByAdvertisementIdsAndMatrixMids" resultMap="GdtMaterialResult">
        <include refid="selectGdtMaterialVo"/>
        where advertisement_id in
        <foreach collection="advertisementIds" item="advertisementId" open="(" close=")" separator=",">
            #{advertisementId}
        </foreach>
        and matrix_mid in
        <foreach collection="matrixMids" item="matrixMid" open="(" close=")" separator=",">
            #{matrixMid}
        </foreach>
    </select>
    <select id="countByTag" resultType="java.lang.Integer">
        select count(*) from tb_gdt_material where tag_id = #{tagId}
    </select>
    <select id="selectUnAdvertisermentIdData" resultMap="GdtMaterialResult">
        <include refid="selectGdtMaterialVo"/>
        where material_md5 is null
    </select>
    <select id="selectListByTagId" resultType="com.ruoyi.system.entity.wxiaa.GdtMaterialEntity">
        <include refid="selectGdtMaterialVo"/>
        where tag_id = #{tagId}
    </select>

    <insert id="insertGdtMaterial" parameterType="com.ruoyi.system.entity.wxiaa.GdtMaterialEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="materialId != null and materialId != ''">material_id,</if>
                    <if test="materialType != null">material_type,</if>
                    <if test="materialDesc != null and materialDesc != ''">material_desc,</if>
                    <if test="materialCreateTime != null">material_create_time,</if>
                    <if test="materialLastModifiedTime != null">material_last_modified_time,</if>
                    <if test="previewUrl != null">preview_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="materialId != null and materialId != ''">#{materialId},</if>
                    <if test="materialType != null">#{materialType},</if>
                    <if test="materialDesc != null and materialDesc != ''">#{materialDesc},</if>
                    <if test="materialCreateTime != null">#{materialCreateTime},</if>
                    <if test="materialLastModifiedTime != null">#{materialLastModifiedTime},</if>
                    <if test="previewUrl != null">#{previewUrl},</if>
        </trim>
    </insert>

    <update id="updateGdtMaterial" parameterType="com.ruoyi.system.entity.wxiaa.GdtMaterialEntity">
        update tb_gdt_material
        <trim prefix="SET" suffixOverrides=",">
                    <if test="materialId != null and materialId != ''">material_id = #{materialId},</if>
                    <if test="materialType != null">material_type = #{materialType},</if>
                    <if test="tagId != null">tag_id = #{tagId},</if>
                    <if test="materialDesc != null and materialDesc != ''">material_desc = #{materialDesc},</if>
                    <if test="materialCreateTime != null">material_create_time = #{materialCreateTime},</if>
                    <if test="materialLastModifiedTime != null">material_last_modified_time = #{materialLastModifiedTime},</if>
                    <if test="previewUrl != null">preview_url = #{previewUrl},</if>
                    <if test="matrixMid != null">matrix_mid = #{matrixMid},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteGdtMaterialById" parameterType="Long">
        delete from tb_gdt_material where id = #{id}
    </delete>

    <delete id="deleteGdtMaterialByIds" parameterType="String">
        delete from tb_gdt_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertUpdate" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_material (`material_id`,`material_type`,`material_desc`,`material_create_time`,`material_last_modified_time`,`preview_url`,`advertisement_id`,`material_info`,`material_md5`,`matrix_mid`,`tag_id`,`cl_info`,`is_deleted`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.materialId}, #{item.materialType},#{item.materialDesc}, #{item.materialCreateTime}, #{item.materialLastModifiedTime}, #{item.previewUrl}, #{item.advertisementId}, #{item.materialInfo}, #{item.materialMd5}, #{item.matrixMid}, #{item.tagId}, #{item.clInfo}, #{item.isDeleted})
        </foreach>
        on duplicate key update
        material_desc = values(material_desc),
        material_last_modified_time = values(material_last_modified_time),
        preview_url = values(preview_url),
        is_deleted = values(is_deleted)
    </insert>

    <update id="batchUpdateMatrixMid">
        <foreach collection="list" separator=";" item="item">
            update tb_gdt_material set matrix_mid = #{item.matrixMid} where material_id = #{item.materialId}
        </foreach>
    </update>

    <select id="selectMinIdByMaterialDesc" resultType="Long">
        select min(id)
        from tb_gdt_material
        where material_desc in
            <foreach collection="materialDescList" item="materialDesc" open="(" close=")" separator=",">
                #{materialDesc}
            </foreach>
        and is_deleted = 0 and (tag_id is null or tag_id = 0)
        group by material_desc
    </select>

    <update id="batchHideMatrix">
        update tb_gdt_material m
        set m.is_hide = 1
        where m.material_desc in
            <foreach collection="materialDescList" item="materialDesc" open="(" close=")" separator=",">
                #{materialDesc}
            </foreach>
        and m.id not in
            <foreach collection="minIds" item="minId" open="(" close=")" separator=",">
                #{minId}
            </foreach>
        and m.is_hide = 0 and m.is_deleted = 0 and (m.tag_id is null or m.tag_id = 0)
    </update>

    <update id="batchUpdateTag">
        update tb_gdt_material set tag_id = #{tagId} where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>
