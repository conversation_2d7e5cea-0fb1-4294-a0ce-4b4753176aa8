<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtUserStayTimeMapper">

    <resultMap type="com.ruoyi.system.entity.shortplay.stat.GdtUserStayTimeEntity" id="GdtUserStayTimeResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="appId"    column="app_id"    />
        <result property="userId"    column="user_id"    />
        <result property="stayTime"    column="stay_time"    />
        <result property="reportInfo"    column="report_info"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtUserStayTimeVo">
        select id, cur_date, app_id, user_id, stay_time, report_info, gmt_create, gmt_modified 
        from tb_gdt_user_stay_time
    </sql>

    <select id="selectByDateAndAppIdAndUserId" resultMap="GdtUserStayTimeResult">
        <include refid="selectGdtUserStayTimeVo"/>
        where cur_date = #{curDate} and app_id = #{appId} and user_id = #{userId}
    </select>

    <insert id="insertOrUpdateStayTime">
        insert into tb_gdt_user_stay_time (cur_date, app_id, user_id, stay_time)
        values (#{curDate}, #{appId}, #{userId}, #{stayTime})
        on duplicate key update
        stay_time = stay_time + #{stayTime}
    </insert>

    <update id="updateReportInfo">
        update tb_gdt_user_stay_time
        set report_info =
            CASE
                WHEN report_info IS NULL OR report_info = '' THEN #{reportInfo}
                ELSE CONCAT(report_info, ',', #{reportInfo})
            END
        where id = #{id}
    </update>

</mapper>
