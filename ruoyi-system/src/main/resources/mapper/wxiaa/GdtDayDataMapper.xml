<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtDayDataEntity" id="GdtDayDataResult">
            <result property="id"    column="id"    />
            <result property="curDate"    column="cur_date"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="advertisementName"    column="advertisement_name"    />
            <result property="adgroupId"    column="adgroup_id"    />
            <result property="adgroupName"    column="adgroup_name"    />
            <result property="operator"    column="operator"    />
            <result property="miniappId"    column="miniapp_id"    />
            <result property="miniappName"    column="miniapp_name"    />
            <result property="appType"    column="app_type"    />
            <result property="videoName"    column="video_name"    />
            <result property="cost"    column="cost"    />
            <result property="cashCost"    column="cash_cost"    />
            <result property="adMonetizationAmount"    column="ad_monetization_amount"    />
            <result property="realAdMonetizationAmount"    column="real_ad_monetization_amount"    />
            <result property="adMoney"    column="ad_money"    />
            <result property="viewCount"    column="view_count"    />
            <result property="validClickCount"    column="valid_click_count"    />
            <result property="conversionsCount"    column="conversions_count"    />
            <result property="appAdPayingUsers"    column="app_ad_paying_users"    />
            <result property="incomeVal1"    column="income_val_1"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
            <result property="accountId"    column="account_id"    />
    </resultMap>

    <sql id="selectGdtDayDataVo">
        select id, cur_date, advertisement_id, advertisement_name, adgroup_id, adgroup_name, operator, miniapp_id, miniapp_name, video_name, cost, cash_cost, ad_monetization_amount, real_ad_monetization_amount, ad_money, view_count, valid_click_count, conversions_count, app_ad_paying_users, income_val_1, gmt_create, gmt_modified from tb_gdt_day_data
    </sql>

    <select id="selectGdtDayDataList" parameterType="com.ruoyi.system.param.wxiaa.GdtDayDataParam" resultMap="GdtDayDataResult">
        select
            <if test="dimension != null and dimension.size() > 0">
                <foreach collection="dimension" item="column" separator="," close=",">
                    ${column}
                </foreach>
            </if>
            sum(cost) as cost, sum(cash_cost) as cash_cost, sum(ad_monetization_amount) as ad_monetization_amount,
            sum(view_count) as view_count, sum(valid_click_count) as valid_click_count, sum(conversions_count) as conversions_count,
            sum(app_ad_paying_users) as app_ad_paying_users, sum(income_val_1) as income_val_1,
            ifnull(sum(ad_money), sum(real_ad_monetization_amount)*0.1/0.7) as ad_money,
            round(sum(ifnull(real_ad_monetization_amount, ad_monetization_amount*0.6/0.7)), 0) as real_ad_monetization_amount,
            sum(real_ad_monetization_amount+ad_money-cost) as feed_ad_profit,
            sum(real_ad_monetization_amount+ad_money-cash_cost) as feed_ad_profit_b,
            sum(valid_click_count)/sum(view_count) as ctr,
            sum(cost)/sum(valid_click_count) as cpc,
            sum(conversions_count)/sum(valid_click_count) as conversions_rate,
            sum(ad_monetization_amount)/sum(app_ad_paying_users) as ad_monetization_arpu,
            sum(cost)*1000/sum(view_count) as thousand_display_price,
            sum(ad_monetization_amount)/sum(cost) as ad_monetization_roi,
            sum(income_val_1)/sum(cost) as income_roi1
        from tb_gdt_day_data d
        left join tb_gdt_advertisement_account a on d.advertisement_id = a.advertisement_id
        left join tb_gdt_miniapp mp on d.miniapp_id = mp.app_id
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="accountId != null ">and a.account_id = #{accountId}</if>
            <if test="advertisementId != null ">and d.advertisement_id = #{advertisementId}</if>
            <if test="operators != null  and operators.size >0">
                and operator in
                <foreach collection="operators" item="operator" separator="," open="(" close=")">
                    #{operator}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size >0">
                and a.account_id in
                <foreach collection="accountIds" item="accountId" separator="," open="(" close=")">
                    #{accountId}
                </foreach>
            </if>
            <if test="miniappNames != null and miniappNames.size > 0">
                and miniapp_name in
                <foreach collection="miniappNames" item="miniappName" separator="," open="(" close=")">
                    #{miniappName}
                </foreach>
            </if>
            <if test="videoName != null  and videoName != ''">and video_name like concat('%', #{videoName}, '%')</if>
        </where>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <choose>
            <when test="dimension != null and dimension.size() > 0 and dimension.contains('cur_date')">
                order by cur_date desc, ${orderColumn} ${orderType}
            </when>
            <otherwise>
                order by ${orderColumn} ${orderType}
            </otherwise>
        </choose>
    </select>

    <select id="selectGdtDayDataById" parameterType="Long" resultMap="GdtDayDataResult">
            <include refid="selectGdtDayDataVo"/>
            where id = #{id}
    </select>
    <select id="selectGdtSumDataGroupDateAndApps" resultType="com.ruoyi.system.bo.wx.GdtSumDayDataBO">
        select miniapp_name as miniappName , sum(cost) as cost, sum(cash_cost) as cashCost, sum(ad_monetization_amount) as adMonetizationAmount
        from tb_gdt_day_data
        where cur_date = #{date}
        group by miniapp_name
    </select>

    <insert id="insertGdtDayData" parameterType="com.ruoyi.system.entity.wxiaa.GdtDayDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">cur_date,</if>
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="advertisementName != null">advertisement_name,</if>
                    <if test="adgroupId != null">adgroup_id,</if>
                    <if test="adgroupName != null and adgroupName != ''">adgroup_name,</if>
                    <if test="operator != null and operator != ''">operator,</if>
                    <if test="miniappId != null and miniappId != ''">miniapp_id,</if>
                    <if test="miniappName != null and miniappName != ''">miniapp_name,</if>
                    <if test="videoName != null and videoName != ''">video_name,</if>
                    <if test="cost != null">cost,</if>
                    <if test="cashCost != null">cash_cost,</if>
                    <if test="adMonetizationAmount != null">ad_monetization_amount,</if>
                    <if test="realAdMonetizationAmount != null">real_ad_monetization_amount,</if>
                    <if test="adMoney != null">ad_money,</if>
                    <if test="viewCount != null">view_count,</if>
                    <if test="validClickCount != null">valid_click_count,</if>
                    <if test="conversionsCount != null">conversions_count,</if>
                    <if test="appAdPayingUsers != null">app_ad_paying_users,</if>
                    <if test="incomeVal1 != null">income_val_1,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="curDate != null">#{curDate},</if>
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="adgroupId != null">#{adgroupId},</if>
                    <if test="adgroupName != null and adgroupName != ''">#{adgroupName},</if>
                    <if test="operator != null and operator != ''">#{operator},</if>
                    <if test="miniappId != null and miniappId != ''">#{miniappId},</if>
                    <if test="miniappName != null and miniappName != ''">#{miniappName},</if>
                    <if test="videoName != null and videoName != ''">#{videoName},</if>
                    <if test="cost != null">#{cost},</if>
                    <if test="cashCost != null">#{cashCost},</if>
                    <if test="adMonetizationAmount != null">#{adMonetizationAmount},</if>
                    <if test="realAdMonetizationAmount != null">#{realAdMonetizationAmount},</if>
                    <if test="adMoney != null">#{adMoney},</if>
                    <if test="viewCount != null">#{viewCount},</if>
                    <if test="validClickCount != null">#{validClickCount},</if>
                    <if test="conversionsCount != null">#{conversionsCount},</if>
                    <if test="appAdPayingUsers != null">#{appAdPayingUsers},</if>
                    <if test="incomeVal1 != null">#{incomeVal1},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>
    <insert id="batchInsertUpdate">
        insert into tb_gdt_day_data (`cur_date`, `advertisement_id`,`advertisement_name`,`adgroup_id`,`adgroup_name`, `operator`, `miniapp_id`, `miniapp_name`, `video_name`, `cost`, `cash_cost`, `ad_monetization_amount`, `real_ad_monetization_amount`, `ad_money`, `view_count`, `valid_click_count`, `conversions_count`, `app_ad_paying_users`, `income_val_1`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.curDate}, #{item.advertisementId},#{item.advertisementName}, #{item.adgroupId}, #{item.adgroupName}, #{item.operator}, #{item.miniappId}, #{item.miniappName}, #{item.videoName}, #{item.cost}, #{item.cashCost}, #{item.adMonetizationAmount}, #{item.realAdMonetizationAmount}, #{item.adMoney}, #{item.viewCount}, #{item.validClickCount}, #{item.conversionsCount}, #{item.appAdPayingUsers}, #{item.incomeVal1})
        </foreach>
        on duplicate key update
        adgroup_name = values(adgroup_name),
        operator = values(operator),
        miniapp_id = values(miniapp_id),
        miniapp_name = values(miniapp_name),
        video_name = values(video_name),
        cost = values(cost),
        cash_cost = values(cash_cost),
        ad_monetization_amount = values(ad_monetization_amount),
        real_ad_monetization_amount = values(real_ad_monetization_amount),
        ad_money = values(ad_money),
        view_count = values(view_count),
        valid_click_count = values(valid_click_count),
        conversions_count = values(conversions_count),
        app_ad_paying_users = values(app_ad_paying_users),
        income_val_1 = values(income_val_1)
    </insert>

    <update id="updateGdtDayData" parameterType="com.ruoyi.system.entity.wxiaa.GdtDayDataEntity">
        update tb_gdt_day_data
        <trim prefix="SET" suffixOverrides=",">
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="adgroupId != null">adgroup_id = #{adgroupId},</if>
                    <if test="adgroupName != null and adgroupName != ''">adgroup_name = #{adgroupName},</if>
                    <if test="operator != null and operator != ''">operator = #{operator},</if>
                    <if test="miniappId != null and miniappId != ''">miniapp_id = #{miniappId},</if>
                    <if test="miniappName != null and miniappName != ''">miniapp_name = #{miniappName},</if>
                    <if test="videoName != null and videoName != ''">video_name = #{videoName},</if>
                    <if test="cost != null">cost = #{cost},</if>
                    <if test="cashCost != null">cash_cost = #{cashCost},</if>
                    <if test="adMonetizationAmount != null">ad_monetization_amount = #{adMonetizationAmount},</if>
                    <if test="realAdMonetizationAmount != null">real_ad_monetization_amount = #{realAdMonetizationAmount},</if>
                    <if test="adMoney != null">ad_money = #{adMoney},</if>
                    <if test="viewCount != null">view_count = #{viewCount},</if>
                    <if test="validClickCount != null">valid_click_count = #{validClickCount},</if>
                    <if test="conversionsCount != null">conversions_count = #{conversionsCount},</if>
                    <if test="appAdPayingUsers != null">app_ad_paying_users = #{appAdPayingUsers},</if>
                    <if test="incomeVal1 != null">income_val_1 = #{incomeVal1},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtDayDataById" parameterType="Long">
        delete from tb_gdt_day_data where id = #{id}
    </delete>

    <delete id="deleteGdtDayDataByIds" parameterType="String">
        delete from tb_gdt_day_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteGdtDayDate">
        delete from tb_gdt_day_data
        <where>
            <if test="entity.curDate != null">cur_date = #{entity.curDate}</if>
            <if test="entity.advertisementId != null"> and advertisement_id = #{entity.advertisementId}</if>
        </where>
    </delete>

</mapper>
