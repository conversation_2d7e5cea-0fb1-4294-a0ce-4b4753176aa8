<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtMiniappMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtMiniappEntity" id="GdtMiniappResult">
            <result property="id"    column="id"    />
            <result property="appId"    column="app_id"    />
            <result property="originalId"    column="original_id"    />
            <result property="appName"    column="app_name"    />
            <result property="appSecret"    column="app_secret"    />
            <result property="appType"    column="app_type"    />
            <result property="platform"    column="platform"    />
            <result property="adMoneyRatio"    column="ad_money_ratio"    />
            <result property="cashRate" column="cash_rate"/>
            <result property="remark"    column="remark"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtMiniappVo">
        select id, app_id, original_id, app_name, app_secret, app_type, platform, ad_money_ratio, cash_rate, remark, gmt_create, gmt_modified from tb_gdt_miniapp
    </sql>

    <select id="selectAdMoneyRatioByAppId" parameterType="String" resultType="java.math.BigDecimal">
        select ad_money_ratio
        from tb_gdt_miniapp
        where app_id = #{appId}
    </select>

    <select id="selectByAppId" parameterType="String" resultMap="GdtMiniappResult">
        <include refid="selectGdtMiniappVo"/>
        where app_id = #{appId}
    </select>

    <select id="selectGdtMiniappList" parameterType="com.ruoyi.system.entity.wxiaa.GdtMiniappEntity" resultMap="GdtMiniappResult">
        <include refid="selectGdtMiniappVo"/>
        <where>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="originalId != null  and originalId != ''"> and original_id = #{originalId}</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
            <if test="appSecret != null  and appSecret != ''"> and app_secret = #{appSecret}</if>
            <if test="appType != null "> and app_type = #{appType}</if>
            <if test="platform != null "> and platform = #{platform}</if>
            <if test="adMoneyRatio != null "> and ad_money_ratio = #{adMoneyRatio}</if>
        </where>
        order by id desc
    </select>

    <select id="selectGdtMiniappById" parameterType="Long" resultMap="GdtMiniappResult">
            <include refid="selectGdtMiniappVo"/>
            where id = #{id}
    </select>

    <insert id="insertGdtMiniapp" parameterType="com.ruoyi.system.entity.wxiaa.GdtMiniappEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_miniapp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="originalId != null and originalId != ''">original_id,</if>
            <if test="appName != null and appName != ''">app_name,</if>
            <if test="appSecret != null and appSecret != ''">app_secret,</if>
            <if test="appType != null">app_type,</if>
            <if test="platform != null">platform,</if>
            <if test="adMoneyRatio != null">ad_money_ratio,</if>
            <if test="cashRate != null">cash_rate,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="originalId != null and originalId != ''">#{originalId},</if>
            <if test="appName != null and appName != ''">#{appName},</if>
            <if test="appSecret != null and appSecret != ''">#{appSecret},</if>
            <if test="appType != null">#{appType},</if>
            <if test="platform != null">#{platform},</if>
            <if test="adMoneyRatio != null">#{adMoneyRatio},</if>
            <if test="cashRate != null">#{cashRate},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateGdtMiniapp" parameterType="com.ruoyi.system.entity.wxiaa.GdtMiniappEntity">
        update tb_gdt_miniapp
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="originalId != null and originalId != ''">original_id = #{originalId},</if>
            <if test="appName != null and appName != ''">app_name = #{appName},</if>
            <if test="appSecret != null and appSecret != ''">app_secret = #{appSecret},</if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="adMoneyRatio != null">ad_money_ratio = #{adMoneyRatio},</if>
            <if test="cashRate != null">cash_rate = #{cashRate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtMiniappById" parameterType="Long">
        delete from tb_gdt_miniapp where id = #{id}
    </delete>

    <delete id="deleteGdtMiniappByIds" parameterType="String">
        delete from tb_gdt_miniapp where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
