<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.wxiaa.GdtCreativeMapper">

    <resultMap type="com.ruoyi.system.entity.wxiaa.GdtCreativeEntity" id="GdtCreativeResult">
            <result property="id"    column="id"    />
            <result property="accountId"    column="account_id"    />
            <result property="advertisementId"    column="advertisement_id"    />
            <result property="adgroupId"    column="adgroup_id"    />
            <result property="creativeId"    column="creative_id"    />
            <result property="creativeName"    column="creative_name"    />
            <result property="createdTime"    column="created_time"    />
            <result property="lastModifiedTime"    column="last_modified_time"    />
            <result property="isDeleted"    column="is_deleted"    />
            <result property="creativeDetail"    column="creative_detail"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectGdtCreativeVo">
        select id, account_id, advertisement_id, adgroup_id, creative_id, creative_name, created_time, last_modified_time, is_deleted, creative_detail, gmt_create, gmt_modified from tb_gdt_creative
    </sql>

    <select id="selectGdtCreativeList" parameterType="com.ruoyi.system.entity.wxiaa.GdtCreativeEntity" resultMap="GdtCreativeResult">
        <include refid="selectGdtCreativeVo"/>
        <where>
                        <if test="accountId != null "> and account_id = #{accountId}</if>
                        <if test="advertisementId != null "> and advertisement_id = #{advertisementId}</if>
                        <if test="adgroupId != null "> and adgroup_id = #{adgroupId}</if>
                        <if test="creativeId != null "> and creative_id = #{creativeId}</if>
                        <if test="creativeName != null  and creativeName != ''"> and creative_name like concat('%', #{creativeName}, '%')</if>
                        <if test="createdTime != null "> and created_time = #{createdTime}</if>
                        <if test="lastModifiedTime != null "> and last_modified_time = #{lastModifiedTime}</if>
                        <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
                        <if test="creativeDetail != null  and creativeDetail != ''"> and creative_detail = #{creativeDetail}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
        order by id desc
    </select>

    <select id="selectGdtCreativeById" parameterType="Long" resultMap="GdtCreativeResult">
            <include refid="selectGdtCreativeVo"/>
            where id = #{id}
    </select>

    <insert id="insertGdtCreative" parameterType="com.ruoyi.system.entity.wxiaa.GdtCreativeEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_gdt_creative
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">account_id,</if>
                    <if test="advertisementId != null">advertisement_id,</if>
                    <if test="adgroupId != null">adgroup_id,</if>
                    <if test="creativeId != null">creative_id,</if>
                    <if test="creativeName != null and creativeName != ''">creative_name,</if>
                    <if test="createdTime != null">created_time,</if>
                    <if test="lastModifiedTime != null">last_modified_time,</if>
                    <if test="isDeleted != null">is_deleted,</if>
                    <if test="creativeDetail != null">creative_detail,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="accountId != null">#{accountId},</if>
                    <if test="advertisementId != null">#{advertisementId},</if>
                    <if test="adgroupId != null">#{adgroupId},</if>
                    <if test="creativeId != null">#{creativeId},</if>
                    <if test="creativeName != null and creativeName != ''">#{creativeName},</if>
                    <if test="createdTime != null">#{createdTime},</if>
                    <if test="lastModifiedTime != null">#{lastModifiedTime},</if>
                    <if test="isDeleted != null">#{isDeleted},</if>
                    <if test="creativeDetail != null">#{creativeDetail},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateGdtCreative" parameterType="com.ruoyi.system.entity.wxiaa.GdtCreativeEntity">
        update tb_gdt_creative
        <trim prefix="SET" suffixOverrides=",">
                    <if test="accountId != null">account_id = #{accountId},</if>
                    <if test="advertisementId != null">advertisement_id = #{advertisementId},</if>
                    <if test="adgroupId != null">adgroup_id = #{adgroupId},</if>
                    <if test="creativeId != null">creative_id = #{creativeId},</if>
                    <if test="creativeName != null and creativeName != ''">creative_name = #{creativeName},</if>
                    <if test="createdTime != null">created_time = #{createdTime},</if>
                    <if test="lastModifiedTime != null">last_modified_time = #{lastModifiedTime},</if>
                    <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
                    <if test="creativeDetail != null">creative_detail = #{creativeDetail},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdtCreativeById" parameterType="Long">
        delete from tb_gdt_creative where id = #{id}
    </delete>

    <delete id="deleteGdtCreativeByIds" parameterType="String">
        delete from tb_gdt_creative where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="batchInsertUpdate">
        insert into tb_gdt_creative (`account_id`,`advertisement_id`,`adgroup_id`,`creative_id`,`creative_name`,`created_time`,`last_modified_time`,`is_deleted`,`creative_detail`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.accountId}, #{item.advertisementId}, #{item.adgroupId}, #{item.creativeId}, #{item.creativeName}, #{item.createdTime}, #{item.lastModifiedTime}, #{item.isDeleted}, #{item.creativeDetail})
        </foreach>
        on duplicate key update
        creative_name = values(creative_name),
        created_time = values(created_time),
        last_modified_time = values(last_modified_time),
        is_deleted = values(is_deleted),
        creative_detail = values(creative_detail)
    </insert>
</mapper>
